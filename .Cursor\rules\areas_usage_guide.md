# 中国地区数据功能说明

本项目已成功集成了中国省市区三级地区数据，包含完整的数据表、模型关联和API接口。

## 功能特性

- ✅ 完整的中国省市区三级地区数据
- ✅ 用户表和商家用户表的地区关联
- ✅ 灵活的地区查询API
- ✅ 地区层级关系支持
- ✅ 拼音搜索支持

## 数据库结构

### areas表
```sql
- id: 地区ID (主键)
- name: 地区名称
- pid: 父级地区ID
- sname: 简称
- level: 级别 (1:省份, 2:城市, 3:区县)
- citycode: 城市代码
- yzcode: 邮政编码
- mername: 合并名称
- Lng: 经度
- Lat: 纬度
- pinyin: 拼音
```

### 用户表扩展字段
```sql
- province_id: 省份ID
- city_id: 城市ID
- district_id: 区县ID
- address: 详细地址
```

## 模型关联

### Area模型
```php
// 获取父级地区
$area->parent

// 获取子级地区
$area->children

// 获取所有后代地区
$area->descendants
```

### User/MerchantUser模型
```php
// 获取用户所在省份
$user->province

// 获取用户所在城市
$user->city

// 获取用户所在区县
$user->district

// 获取完整地址
$user->full_address
```

## API接口

### 基础路径
```
/api/areas/
```

### 接口列表

#### 1. 获取省份列表
```
GET /api/areas/provinces
```

**响应示例:**
```json
{
  "code": 200,
  "message": "获取省份列表成功",
  "data": [
    {
      "id": 1,
      "name": "北京市",
      "pinyin": "beijing"
    }
  ]
}
```

#### 2. 获取城市列表
```
GET /api/areas/cities?province_id=1
```

#### 3. 获取区县列表
```
GET /api/areas/districts?city_id=2
```

#### 4. 搜索地区
```
GET /api/areas/search?keyword=北京
```

#### 5. 获取地区路径
```
GET /api/areas/path?area_id=1
```

**响应示例:**
```json
{
  "code": 200,
  "message": "获取地区路径成功",
  "data": {
    "area": {...},
    "path": [
      {"id": 1, "name": "北京市", "level": 1},
      {"id": 2, "name": "北京市", "level": 2},
      {"id": 3, "name": "东城区", "level": 3}
    ]
  }
}
```

#### 6. 获取用户地址 (需要认证)
```
GET /api/areas/user/address
Authorization: Bearer {token}
```

## 使用示例

### 1. 在控制器中使用
```php
use App\Models\Area;
use App\Models\User;

// 获取所有省份
$provinces = Area::where('level', 1)->get();

// 获取某省份下的城市
$cities = Area::where('pid', $provinceId)->where('level', 2)->get();

// 创建用户时设置地区
$user = User::create([
    'name' => '张三',
    'email' => '<EMAIL>',
    'province_id' => 1,
    'city_id' => 2,
    'district_id' => 3,
    'address' => '某某街道123号'
]);

// 获取用户完整地址
echo $user->full_address; // 输出: 北京市北京市东城区某某街道123号
```

### 2. 在前端中使用
```javascript
// 获取省份列表
fetch('/api/areas/provinces')
  .then(response => response.json())
  .then(data => {
    console.log(data.data); // 省份列表
  });

// 级联选择：根据省份获取城市
function getCities(provinceId) {
  fetch(`/api/areas/cities?province_id=${provinceId}`)
    .then(response => response.json())
    .then(data => {
      console.log(data.data); // 城市列表
    });
}

// 搜索地区
function searchAreas(keyword) {
  fetch(`/api/areas/search?keyword=${keyword}`)
    .then(response => response.json())
    .then(data => {
      console.log(data.data); // 搜索结果
    });
}
```

## 数据导入状态

当前areas表中的数据记录数量较少，如需完整的地区数据，可以：

1. 检查原始SQL文件的编码问题
2. 使用其他方式导入完整的地区数据
3. 或者从可靠的数据源重新获取地区数据

## 注意事项

1. 地区数据的level字段：1=省份，2=城市，3=区县
2. pid字段表示父级地区ID，顶级省份的pid为0
3. 用户表中的地区字段都是可选的，可以根据业务需求设置为必填
4. 建议在生产环境中为地区相关字段添加适当的索引以提高查询性能

## 扩展建议

1. 可以添加地区缓存机制提高查询性能
2. 可以添加地区数据的定期更新机制
3. 可以根据业务需求添加更多地区相关的功能，如距离计算、地区统计等 