<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * 团队模型
 * 
 * @property int $id
 * @property string $name 团队名称
 * @property int|null $leader_id 团队长ID
 * @property int|null $parent_id 上级团队ID
 * @property int $member_count 成员数量
 * @property int $level 团队级别
 * @property string|null $description 团队描述
 * @property array|null $reward_config 奖励配置
 * @property array|null $management_permissions 管理权限
 * @property string $status 状态
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Salesperson|null $leader
 * @property-read \App\Models\Team|null $parent
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Team[] $children
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Salesperson[] $members
 */
class Team extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'leader_id',
        'parent_id',
        'member_count',
        'level',
        'description',
        'reward_config',
        'management_permissions',
        'status',
    ];

    protected $casts = [
        'reward_config' => 'array',
        'management_permissions' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 状态常量
     */
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';
    const STATUS_DISBANDED = 'disbanded';

    /**
     * 团队级别常量
     */
    const LEVEL_PRIMARY = 1;    // 初级团队
    const LEVEL_INTERMEDIATE = 2; // 中级团队
    const LEVEL_ADVANCED = 3;   // 高级团队
    const LEVEL_ELITE = 4;      // 精英团队
    const LEVEL_MASTER = 5;     // 大师团队

    /**
     * 获取状态选项
     */
    public static function getStatusOptions()
    {
        return [
            self::STATUS_ACTIVE => '活跃',
            self::STATUS_INACTIVE => '非活跃',
            self::STATUS_DISBANDED => '已解散',
        ];
    }

    /**
     * 获取团队级别选项
     */
    public static function getLevelOptions()
    {
        return [
            self::LEVEL_PRIMARY => '初级团队',
            self::LEVEL_INTERMEDIATE => '中级团队',
            self::LEVEL_ADVANCED => '高级团队',
            self::LEVEL_ELITE => '精英团队',
            self::LEVEL_MASTER => '大师团队',
        ];
    }

    /**
     * 获取默认奖励配置
     */
    public static function getDefaultRewardConfig($level)
    {
        $configs = [
            self::LEVEL_PRIMARY => [
                'base_commission_rate' => 0.01,
                'performance_bonus' => [
                    'monthly_target' => 10000,
                    'bonus_rate' => 0.005,
                ],
                'team_bonus' => [
                    'member_bonus' => 100,
                    'leader_bonus' => 500,
                ],
            ],
            self::LEVEL_INTERMEDIATE => [
                'base_commission_rate' => 0.015,
                'performance_bonus' => [
                    'monthly_target' => 20000,
                    'bonus_rate' => 0.008,
                ],
                'team_bonus' => [
                    'member_bonus' => 200,
                    'leader_bonus' => 800,
                ],
            ],
            self::LEVEL_ADVANCED => [
                'base_commission_rate' => 0.02,
                'performance_bonus' => [
                    'monthly_target' => 50000,
                    'bonus_rate' => 0.01,
                ],
                'team_bonus' => [
                    'member_bonus' => 300,
                    'leader_bonus' => 1200,
                ],
            ],
            self::LEVEL_ELITE => [
                'base_commission_rate' => 0.025,
                'performance_bonus' => [
                    'monthly_target' => 100000,
                    'bonus_rate' => 0.012,
                ],
                'team_bonus' => [
                    'member_bonus' => 500,
                    'leader_bonus' => 2000,
                ],
            ],
            self::LEVEL_MASTER => [
                'base_commission_rate' => 0.03,
                'performance_bonus' => [
                    'monthly_target' => 200000,
                    'bonus_rate' => 0.015,
                ],
                'team_bonus' => [
                    'member_bonus' => 800,
                    'leader_bonus' => 3000,
                ],
            ],
        ];

        return $configs[$level] ?? $configs[self::LEVEL_PRIMARY];
    }

    /**
     * 获取默认管理权限
     */
    public static function getDefaultManagementPermissions()
    {
        return [
            'manage_members' => true,
            'view_performance' => true,
            'assign_tasks' => true,
            'approve_leave' => false,
            'manage_rewards' => false,
            'view_reports' => true,
        ];
    }

    /**
     * 关联团队长
     */
    public function leader()
    {
        return $this->belongsTo(Salesperson::class, 'leader_id');
    }

    /**
     * 关联上级团队
     */
    public function parent()
    {
        return $this->belongsTo(Team::class, 'parent_id');
    }

    /**
     * 关联下级团队
     */
    public function children()
    {
        return $this->hasMany(Team::class, 'parent_id');
    }

    /**
     * 关联团队成员
     */
    public function members()
    {
        return $this->hasMany(Salesperson::class, 'team_id');
    }

    /**
     * 获取状态标签
     */
    public function getStatusLabelAttribute()
    {
        return self::getStatusOptions()[$this->status] ?? $this->status;
    }

    /**
     * 获取状态颜色
     */
    public function getStatusColorAttribute()
    {
        $colors = [
            self::STATUS_ACTIVE => 'success',
            self::STATUS_INACTIVE => 'warning',
            self::STATUS_DISBANDED => 'danger',
        ];
        return $colors[$this->status] ?? 'default';
    }

    /**
     * 获取团队级别标签
     */
    public function getLevelLabelAttribute()
    {
        return self::getLevelOptions()[$this->level] ?? "级别 {$this->level}";
    }

    /**
     * 获取团队长名称
     */
    public function getLeaderNameAttribute()
    {
        return $this->leader ? $this->leader->name : '无';
    }

    /**
     * 获取上级团队名称
     */
    public function getParentNameAttribute()
    {
        return $this->parent ? $this->parent->name : '无';
    }

    /**
     * 获取实际成员数量
     */
    public function getActualMemberCountAttribute()
    {
        return $this->members()->count();
    }

    /**
     * 获取下级团队数量
     */
    public function getChildrenCountAttribute()
    {
        return $this->children()->count();
    }

    /**
     * 获取团队层级路径
     */
    public function getHierarchyPathAttribute()
    {
        $path = [$this->name];
        $parent = $this->parent;
        
        while ($parent) {
            array_unshift($path, $parent->name);
            $parent = $parent->parent;
        }
        
        return implode(' > ', $path);
    }

    /**
     * 获取团队深度
     */
    public function getDepthAttribute()
    {
        $depth = 0;
        $parent = $this->parent;
        
        while ($parent) {
            $depth++;
            $parent = $parent->parent;
        }
        
        return $depth;
    }

    /**
     * 获取奖励配置值
     */
    public function getRewardConfigValue($key, $default = null)
    {
        return data_get($this->reward_config, $key, $default);
    }

    /**
     * 设置奖励配置值
     */
    public function setRewardConfigValue($key, $value)
    {
        $config = $this->reward_config ?? [];
        data_set($config, $key, $value);
        $this->reward_config = $config;
        return $this;
    }

    /**
     * 获取管理权限
     */
    public function hasManagementPermission($permission)
    {
        return $this->management_permissions[$permission] ?? false;
    }

    /**
     * 设置管理权限
     */
    public function setManagementPermission($permission, $value = true)
    {
        $permissions = $this->management_permissions ?? [];
        $permissions[$permission] = $value;
        $this->management_permissions = $permissions;
        return $this;
    }

    /**
     * 作用域：按状态筛选
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 作用域：活跃团队
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    /**
     * 作用域：按级别筛选
     */
    public function scopeByLevel($query, $level)
    {
        return $query->where('level', $level);
    }

    /**
     * 作用域：按团队长筛选
     */
    public function scopeByLeader($query, $leaderId)
    {
        return $query->where('leader_id', $leaderId);
    }

    /**
     * 作用域：顶级团队
     */
    public function scopeTopLevel($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * 作用域：搜索
     */
    public function scopeSearch($query, $keyword)
    {
        return $query->where(function ($q) use ($keyword) {
            $q->where('name', 'like', "%{$keyword}%")
              ->orWhere('description', 'like', "%{$keyword}%");
        });
    }

    /**
     * 添加成员
     */
    public function addMember(Salesperson $salesperson)
    {
        $salesperson->update(['team_id' => $this->id]);
        $this->updateMemberCount();
        return $this;
    }

    /**
     * 移除成员
     */
    public function removeMember(Salesperson $salesperson)
    {
        $salesperson->update(['team_id' => null]);
        $this->updateMemberCount();
        return $this;
    }

    /**
     * 更新成员数量
     */
    public function updateMemberCount()
    {
        $this->update(['member_count' => $this->members()->count()]);
        return $this;
    }

    /**
     * 设置团队长
     */
    public function setLeader(Salesperson $salesperson)
    {
        // 确保团队长在团队中
        if ($salesperson->team_id !== $this->id) {
            $this->addMember($salesperson);
        }
        
        // 更新角色为团队长
        $salesperson->update(['role' => Salesperson::ROLE_TEAM_LEADER]);
        
        // 设置为团队长
        $this->update(['leader_id' => $salesperson->id]);
        
        return $this;
    }

    /**
     * 升级团队
     */
    public function upgrade()
    {
        if ($this->level < self::LEVEL_MASTER) {
            $newLevel = $this->level + 1;
            $this->update([
                'level' => $newLevel,
                'reward_config' => self::getDefaultRewardConfig($newLevel),
            ]);
        }
        
        return $this;
    }

    /**
     * 降级团队
     */
    public function downgrade()
    {
        if ($this->level > self::LEVEL_PRIMARY) {
            $newLevel = $this->level - 1;
            $this->update([
                'level' => $newLevel,
                'reward_config' => self::getDefaultRewardConfig($newLevel),
            ]);
        }
        
        return $this;
    }

    /**
     * 解散团队
     */
    public function disband()
    {
        // 移除所有成员的团队关联
        $this->members()->update(['team_id' => null]);
        
        // 解散所有子团队
        $this->children()->each(function ($child) {
            $child->disband();
        });
        
        // 更新状态
        $this->update([
            'status' => self::STATUS_DISBANDED,
            'leader_id' => null,
            'member_count' => 0,
        ]);
        
        return $this;
    }

    /**
     * 检查是否可以删除
     */
    public function canDelete()
    {
        // 有成员或子团队时不能删除
        return $this->member_count == 0 && $this->children()->count() == 0;
    }

    /**
     * 获取所有下级团队（递归）
     */
    public function getAllDescendants()
    {
        $descendants = collect();
        
        foreach ($this->children as $child) {
            $descendants->push($child);
            $descendants = $descendants->merge($child->getAllDescendants());
        }
        
        return $descendants;
    }

    /**
     * 获取所有上级团队（递归）
     */
    public function getAllAncestors()
    {
        $ancestors = collect();
        $parent = $this->parent;
        
        while ($parent) {
            $ancestors->push($parent);
            $parent = $parent->parent;
        }
        
        return $ancestors;
    }

    /**
     * 获取团队业绩统计
     */
    public function getPerformanceStats($startDate = null, $endDate = null)
    {
        // 这里应该根据实际业务逻辑计算团队业绩
        // 暂时返回模拟数据
        return [
            'total_revenue' => 0,
            'member_performance' => [],
            'team_ranking' => 0,
            'achievement_rate' => 0,
        ];
    }

    /**
     * 计算团队奖励
     */
    public function calculateRewards($performance)
    {
        $config = $this->reward_config;
        if (!$config) {
            return 0;
        }
        
        $baseCommission = $performance * ($config['base_commission_rate'] ?? 0);
        $performanceBonus = 0;
        
        // 计算业绩奖金
        if (isset($config['performance_bonus'])) {
            $target = $config['performance_bonus']['monthly_target'] ?? 0;
            $bonusRate = $config['performance_bonus']['bonus_rate'] ?? 0;
            
            if ($performance >= $target) {
                $performanceBonus = ($performance - $target) * $bonusRate;
            }
        }
        
        return $baseCommission + $performanceBonus;
    }

    /**
     * 重置为默认配置
     */
    public function resetToDefaultConfig()
    {
        $this->update([
            'reward_config' => self::getDefaultRewardConfig($this->level),
            'management_permissions' => self::getDefaultManagementPermissions(),
        ]);
        
        return $this;
    }

    /**
     * 获取团队统计信息
     */
    public static function getStatistics()
    {
        return [
            'total_teams' => self::count(),
            'active_teams' => self::active()->count(),
            'by_level' => self::selectRaw('level, count(*) as count')
                             ->groupBy('level')
                             ->pluck('count', 'level')
                             ->toArray(),
            'avg_member_count' => self::avg('member_count'),
            'top_teams' => self::orderBy('member_count', 'desc')
                              ->limit(10)
                              ->get(['id', 'name', 'member_count']),
        ];
    }
}