<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            <label for="settlement_cycle" class="required">结算周期</label>
            <select class="form-control" name="settlement_cycle" id="settlement_cycle" required>
                <option value="">请选择结算周期</option>
                <option value="weekly" {{ (isset($agent_region) && $agent_region->settlement_cycle == 'weekly') ? 'selected' : '' }}>每周结算</option>
                <option value="monthly" {{ (isset($agent_region) && $agent_region->settlement_cycle == 'monthly') ? 'selected' : '' }}>每月结算</option>
                <option value="quarterly" {{ (isset($agent_region) && $agent_region->settlement_cycle == 'quarterly') ? 'selected' : '' }}>每季度结算</option>
            </select>
            <small class="text-muted">返佣款项结算的时间周期</small>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="form-group">
            <label for="settlement_day">结算间隔天数</label>
            <input type="number" class="form-control" name="settlement_day" id="settlement_day" 
                   min="1" max="31" placeholder="请输入结算间隔天数"
                   value="{{ isset($agent_region) ? $agent_region->settlement_day : '' }}">
            <small class="text-muted">结算间隔天数与结算周期可二选一</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            <label for="settlement_account_name">银行账户名</label>
            <input type="text" class="form-control" name="settlement_account_name" id="settlement_account_name" 
                   placeholder="请输入银行账户名"
                   value="{{ isset($agent_region) ? $agent_region->settlement_account_name : '' }}">
            <small class="text-muted">用于结算的银行账户持有人姓名</small>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="form-group">
            <label for="settlement_bank_account">银行账号</label>
            <input type="text" class="form-control" name="settlement_bank_account" id="settlement_bank_account" 
                   placeholder="请输入银行账号"
                   value="{{ isset($agent_region) ? $agent_region->settlement_bank_account : '' }}">
            <small class="text-muted">用于接收返佣款项的银行账号</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            <label for="settlement_bank_name">开户银行</label>
            <input type="text" class="form-control" name="settlement_bank_name" id="settlement_bank_name" 
                   placeholder="请输入开户银行名称"
                   value="{{ isset($agent_region) ? $agent_region->settlement_bank_name : '' }}">
            <small class="text-muted">银行账户开户的银行名称</small>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="form-group">
            <label for="bank_branch">开户支行</label>
            <input type="text" class="form-control" name="bank_branch" id="bank_branch" 
                   placeholder="请输入开户支行名称"
                   value="{{ isset($agent_region) ? $agent_region->bank_branch : '' }}">
            <small class="text-muted">具体的开户支行信息</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            <label for="min_settlement_amount">最低结算金额(元)</label>
            <input type="number" class="form-control" name="min_settlement_amount" id="min_settlement_amount" 
                   min="0" step="0.01" placeholder="0.00"
                   value="{{ isset($agent_region) ? $agent_region->min_settlement_amount : '' }}">
            <small class="text-muted">达到此金额才进行结算，避免小额转账成本</small>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="form-group">
            <label for="settlement_fee_rate">结算手续费率(%)</label>
            <input type="number" class="form-control" name="settlement_fee_rate" id="settlement_fee_rate" 
                   min="0" max="10" step="0.01" placeholder="0.00"
                   value="{{ isset($agent_region) ? $agent_region->settlement_fee_rate : '' }}">
            <small class="text-muted">银行转账等手续费率，从返佣中扣除</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            <label for="last_settlement_at">上次结算日期</label>
            <input type="date" class="form-control" name="last_settlement_at" id="last_settlement_at"
                   value="{{ isset($agent_region) && $agent_region->last_settlement_at ? $agent_region->last_settlement_at->format('Y-m-d') : '' }}">
            <small class="text-muted">最近一次结算的日期</small>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="form-group">
            <label for="next_settlement_at">下次结算日期</label>
            <input type="date" class="form-control" name="next_settlement_at" id="next_settlement_at"
                   value="{{ isset($agent_region) && $agent_region->next_settlement_at ? $agent_region->next_settlement_at->format('Y-m-d') : '' }}">
            <small class="text-muted">系统根据结算周期自动计算，亦可手动修改</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label for="settlement_notes">结算备注</label>
            <textarea class="form-control" name="settlement_notes" id="settlement_notes" rows="4" 
                      placeholder="请输入结算相关的备注信息...">{{ isset($agent_region) ? $agent_region->settlement_notes : '' }}</textarea>
            <small class="text-muted">记录特殊的结算要求或注意事项</small>
        </div>
    </div>
</div>

<div class="alert alert-info">
    <i class="fa fa-info-circle"></i>
    <strong>结算配置说明：</strong>
    <ul class="mb-0 mt-2">
        <li>结算周期和结算日决定了返佣款项的支付时间</li>
        <li>银行账户信息必须准确，错误信息会导致结算失败</li>
        <li>设置最低结算金额可以减少频繁的小额转账</li>
        <li>手续费率会从返佣金额中自动扣除</li>
        <li>下次结算日期会根据周期设置自动计算</li>
    </ul>
</div>

<script>
$(function() {
    var nextSettlementAtTouched = false;
    function updateNextSettlementAt() {
        if(nextSettlementAtTouched) return; // 用户手动输入后不再自动覆盖
        var settlementDay = $("#settlement_day").val();
        var settlementCycle = $("#settlement_cycle").val();
        var commissionEffectiveDate = $("#commission_effective_date").val() || $("input[name='commission_effective_date']").val();
        var lastSettlementAt = $("#last_settlement_at").val();
        var nextDate = '';
        var baseDate;
        if (lastSettlementAt) {
            baseDate = new Date(lastSettlementAt);
        } else if (commissionEffectiveDate) {
            baseDate = new Date(commissionEffectiveDate);
        } else {
            baseDate = new Date(); // 若无返佣生效日期则用今天
        }
        if (settlementDay) {
            baseDate.setDate(baseDate.getDate() + parseInt(settlementDay));
            nextDate = baseDate.toISOString().slice(0,10);
        } else if (settlementCycle) {
            switch(settlementCycle) {
                case 'weekly':
                    baseDate.setDate(baseDate.getDate() + 7);
                    break;
                case 'monthly':
                    baseDate.setMonth(baseDate.getMonth() + 1);
                    break;
                case 'quarterly':
                    baseDate.setMonth(baseDate.getMonth() + 3);
                    break;
            }
            nextDate = baseDate.toISOString().slice(0,10);
        }
        $("#next_settlement_at").val(nextDate);
    }
    function updateDisableState() {
        var settlementDay = $("#settlement_day").val();
        var settlementCycle = $("#settlement_cycle").val();
        if (settlementDay) {
            $("#settlement_cycle").prop('disabled', true);
        } else {
            $("#settlement_cycle").prop('disabled', false);
        }
        if (settlementCycle) {
            $("#settlement_day").prop('disabled', true);
        } else {
            $("#settlement_day").prop('disabled', false);
        }
    }
    $("#settlement_day").on('input', function() {
        if ($(this).val()) {
            $("#settlement_cycle").val('');
        }
        nextSettlementAtTouched = false;
        updateDisableState();
        updateNextSettlementAt();
    });
    $("#settlement_cycle").on('change', function() {
        if ($(this).val()) {
            $("#settlement_day").val('');
        }
        nextSettlementAtTouched = false;
        updateDisableState();
        updateNextSettlementAt();
    });
    $("#commission_effective_date, #last_settlement_at").on('change', function() {
        nextSettlementAtTouched = false;
        updateNextSettlementAt();
    });
    $("#next_settlement_at").on('input change', function() {
        nextSettlementAtTouched = true;
    });
    // 页面加载时自动计算
    updateDisableState();
    updateNextSettlementAt();
});
</script> 