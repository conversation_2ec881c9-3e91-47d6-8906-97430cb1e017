{"info": {"name": "Laravel Access Token API", "description": "获取和验证Access Token的API接口集合", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "获取 Access Token", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "appKey", "value": "red.qzgiaXL14ncBwlBz", "type": "text"}, {"key": "appSecret", "value": "cf2ef172726d1272c4ed661e1c059a45", "type": "text"}, {"key": "grant_type", "value": "client_credentials", "type": "text"}]}, "url": {"raw": "http://localhost/pyp-<PERSON><PERSON>-new/public/api/auth/access-token", "protocol": "http", "host": ["localhost"], "path": ["pyp-<PERSON><PERSON>-new", "public", "api", "auth", "access-token"]}, "description": "通过appKey和appSecret获取access_token"}, "response": [{"name": "成功响应示例", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "appKey", "value": "red.qzgiaXL14ncBwlBz"}, {"key": "appSecret", "value": "cf2ef172726d1272c4ed661e1c059a45"}, {"key": "grant_type", "value": "client_credentials"}]}, "url": {"raw": "http://localhost/pyp-<PERSON><PERSON>-new/public/api/auth/access-token", "host": ["localhost"], "path": ["pyp-<PERSON><PERSON>-new", "public", "api", "auth", "access-token"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"code\": 200,\n  \"message\": \"获取access_token成功\",\n  \"data\": {\n    \"access_token\": \"eyJhcHBfa2V5IjoicmVkLnF6Z2lhWEwxNG5jQndsQnoiLCJpYXQiOjE3NTIxMzA0OTUsImV4cCI6MTc1MjEzNzY5NSwic2NvcGUiOiJyZWFkIHdyaXRlIn0.NTE5Yjk0OTU3NzIxNGE3NzM0M2Q4MTRkZTFjOWZlODNiOGZjZWE5NTM3NjI1NGU1NTVhMDZmNjZkMmRjYWE3NQ==\",\n    \"token_type\": \"Bearer\",\n    \"expires_in\": 7200,\n    \"scope\": \"read write\"\n  },\n  \"timestamp\": 1752130495\n}"}]}, {"name": "验证 Access Token", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "access_token", "value": "{{access_token}}", "type": "text", "description": "从获取Token接口返回的access_token"}]}, "url": {"raw": "http://localhost/pyp-<PERSON><PERSON>-new/public/api/auth/verify-token", "protocol": "http", "host": ["localhost"], "path": ["pyp-<PERSON><PERSON>-new", "public", "api", "auth", "verify-token"]}, "description": "验证access_token的有效性"}}, {"name": "错误示例 - 无效凭据", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "appKey", "value": "wrong_key", "type": "text"}, {"key": "appSecret", "value": "wrong_secret", "type": "text"}, {"key": "grant_type", "value": "client_credentials", "type": "text"}]}, "url": {"raw": "http://localhost/pyp-<PERSON><PERSON>-new/public/api/auth/access-token", "protocol": "http", "host": ["localhost"], "path": ["pyp-<PERSON><PERSON>-new", "public", "api", "auth", "access-token"]}, "description": "测试错误凭据的响应"}, "response": [{"name": "错误响应示例", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "appKey", "value": "wrong_key"}, {"key": "appSecret", "value": "wrong_secret"}]}, "url": {"raw": "http://localhost/pyp-<PERSON><PERSON>-new/public/api/auth/access-token", "host": ["localhost"], "path": ["pyp-<PERSON><PERSON>-new", "public", "api", "auth", "access-token"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"code\": 400,\n  \"message\": \"无效的appKey或appSecret\",\n  \"timestamp\": 1752130495\n}"}]}], "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["// 自动提取access_token到环境变量", "if (pm.response.code === 200) {", "    var jsonData = pm.response.json();", "    if (jsonData.data && jsonData.data.access_token) {", "        pm.environment.set('access_token', jsonData.data.access_token);", "        console.log('Access token saved:', jsonData.data.access_token);", "    }", "}"]}}], "variable": [{"key": "base_url", "value": "http://localhost/pyp-Laravel-new/public"}]}