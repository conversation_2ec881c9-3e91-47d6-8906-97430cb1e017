<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>结算详情</title>
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- 只加载必要的CSS -->
    <link rel="stylesheet" href="{{ admin_asset('vendor/laravel-admin/AdminLTE/bootstrap/css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ admin_asset('vendor/laravel-admin/font-awesome/css/font-awesome.min.css') }}">

    <style>
        body {
            font-family: "Source Sans Pro", "Helvetica Neue", Helvetica, Arial, sans-serif;
            background-color: #ecf0f5;
            margin: 0;
            padding: 20px;
        }

        .main-header {
            background: #3c8dbc;
            color: white;
            padding: 15px 20px;
            margin-bottom: 20px;
            border-radius: 3px;
        }

        .main-header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: normal;
        }

        .container-fluid {
            padding: 0;
        }

        .box {
            background: #fff;
            border-radius: 3px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
            margin-bottom: 20px;
        }

        .box-header {
            border-bottom: 1px solid #f4f4f4;
            padding: 10px 15px;
            position: relative;
        }

        .box-header.with-border {
            border-bottom: 1px solid #d2d6de;
        }

        .box-title {
            font-size: 18px;
            margin: 0;
            line-height: 1.5;
            color: #444;
        }

        .box-tools {
            position: absolute;
            right: 15px;
            top: 10px;
        }

        .box-body {
            padding: 15px;
        }

        .detail-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .detail-table th,
        .detail-table td {
            border: 1px solid #ddd;
            padding: 8px 12px;
            text-align: left;
        }

        .detail-table th {
            background-color: #f5f5f5;
            font-weight: bold;
            width: 150px;
        }

        .stores-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .stores-table th,
        .stores-table td {
            border: 1px solid #ddd;
            padding: 6px 8px;
            text-align: left;
            font-size: 12px;
        }

        .stores-table th {
            background-color: #f5f5f5;
            font-weight: bold;
            white-space: nowrap;
        }

        .settlement-info,
        .store-info {
            margin-bottom: 30px;
        }

        .settlement-info h4,
        .store-info h4 {
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3c8dbc;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .error-message {
            color: #d9534f;
            background-color: #f2dede;
            border: 1px solid #ebccd1;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }

        .label {
            display: inline-block;
            padding: 2px 6px;
            font-size: 11px;
            font-weight: bold;
            line-height: 1;
            color: #fff;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: 3px;
        }

        .label-default {
            background-color: #777;
        }

        .label-success {
            background-color: #5cb85c;
        }

        .label-info {
            background-color: #5bc0de;
        }

        .label-warning {
            background-color: #f0ad4e;
        }

        .label-danger {
            background-color: #d9534f;
        }

        .btn {
            display: inline-block;
            padding: 6px 12px;
            margin-bottom: 0;
            font-size: 14px;
            font-weight: normal;
            line-height: 1.42857143;
            text-align: center;
            white-space: nowrap;
            vertical-align: middle;
            cursor: pointer;
            border: 1px solid transparent;
            border-radius: 4px;
            text-decoration: none;
        }

        .btn-default {
            color: #333;
            background-color: #fff;
            border-color: #ccc;
        }

        .btn-default:hover {
            color: #333;
            background-color: #e6e6e6;
            border-color: #adadad;
            text-decoration: none;
        }

        .btn-sm {
            padding: 5px 10px;
            font-size: 12px;
            line-height: 1.5;
            border-radius: 3px;
        }

        .calculation-formula {
            background-color: #f8f9fa;
            padding: 10px;
            border-left: 4px solid #3c8dbc;
            margin: 5px 0;
        }

        .text-info {
            color: #31708f !important;
            font-weight: bold;
        }

        .text-muted {
            color: #777 !important;
        }
    </style>
</head>

<body>
    <div class="main-header">
        <h1>结算详情</h1>
    </div>

    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="box">
                    <div class="box-header with-border">
                        <h3 class="box-title">结算详情</h3>
                        <div class="box-tools">
                            <a href="{{ admin_url('commission-settlements') }}" class="btn btn-sm btn-default">
                                <i class="fa fa-list"></i> 返回列表
                            </a>
                        </div>
                    </div>
                    <div class="box-body">
                        <div id="settlement-detail"
                            data-id="{{ $id }}"
                            data-api-url="{{ admin_url('commission-settlements/'.$id.'/api-detail') }}">
                            <!-- 结算信息 -->
                            <div class="settlement-info">
                                <h4>结算信息</h4>
                                <div id="settlement-loading" class="loading">
                                    <span class="spinner"></span>正在加载结算数据...
                                </div>
                                <div id="settlement-error" class="error-message" style="display: none;">
                                    加载结算数据失败，请刷新页面重试。
                                </div>
                                <table id="settlement-table" class="detail-table" style="display: none;">
                                    <tbody id="settlement-tbody">
                                        <!-- 结算数据将通过JavaScript动态加载 -->
                                    </tbody>
                                </table>
                            </div>

                            <!-- 商铺信息 -->
                            <div class="store-info">
                                <h4>商铺信息</h4>
                                <div id="stores-loading" class="loading">
                                    <span class="spinner"></span>正在加载商铺数据...
                                </div>
                                <div id="stores-error" class="error-message" style="display: none;">
                                    加载商铺数据失败，请刷新页面重试。
                                </div>
                                <table id="stores-table" class="stores-table" style="display: none;">
                                    <thead>
                                        <tr>
                                            <th>商铺ID</th>
                                            <th>商铺名称</th>
                                            <th>联系人</th>
                                            <th>联系电话</th>
                                            <th>地址</th>
                                            <th>商品金额</th>
                                            <th>是否已结算</th>
                                            <th>结算状态</th>
                                            <th>代理商ID</th>
                                            <th>省份</th>
                                            <th>城市</th>
                                            <th>区县</th>
                                            <th>审核状态</th>
                                            <th>商铺状态</th>
                                            <th>审核备注</th>
                                            <th>最近结算单ID</th>
                                        </tr>
                                    </thead>
                                    <tbody id="stores-tbody">
                                        <!-- 商铺数据将通过JavaScript动态加载 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript文件按依赖顺序加载 -->
    <script src="{{ admin_asset('vendor/laravel-admin/AdminLTE/plugins/jQuery/jQuery-2.1.4.min.js') }}"></script>
    <script src="{{ asset('js/commission-settlement-detail.js') }}?v={{ time() }}"></script>
</body>

</html>