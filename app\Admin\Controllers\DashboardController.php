<?php

namespace App\Admin\Controllers;

use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Layout\Content;
use Encore\Admin\Widgets\InfoBox;
use Encore\Admin\Widgets\Box;
use Encore\Admin\Widgets\Table;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use App\Models\Agent;
use App\Models\Store;
use App\Models\Salesperson;
use App\Models\Team;
use App\Services\PermissionService;
use Encore\Admin\Admin;
use Exception;

class DashboardController extends AdminController
{
    public function __construct()
    {
        // 添加Chart.js资源
        Admin::js('https://cdn.jsdelivr.net/npm/chart.js');
    }

    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = '仪表板';

    /**
     * 仪表板首页
     */
    public function index(Content $content)
    {
        // 检查权限
        if (!PermissionService::hasPermission('dashboard.view')) {
            abort(403, '您没有权限访问仪表板');
        }

        Admin::style('.box-body { padding: 15px; }');

        return $content
            ->title('仪表板')
            ->description('数据概览')
            ->row(function ($row) {
                $boxes = $this->statisticsBoxes();
                foreach ($boxes as $box) {
                    $row->column(3, $box);
                }
            })
            ->row(function ($row) {
                $row->column(8, $this->revenueChart());
                $row->column(4, $this->userTypeChart());
            })
            ->row(function ($row) {
                $row->column(6, $this->recentAgents());
                $row->column(6, $this->recentMerchants());
            })
            ->row($this->systemStatus());
    }

    /**
     * 统计数据盒子
     */
    protected function statisticsBoxes()
    {
        return [
            new InfoBox('总代理商数', 'users', 'aqua', '/admin/agents', $this->getTotalAgents()),
            new InfoBox('总商铺数', 'shopping-bag', 'green', '/admin/stores', $this->getTotalMerchants()),
            new InfoBox('总业务员数', 'user-tie', 'yellow', '/admin/salespersons', $this->getTotalSalespersons()),
            new InfoBox('今日收入', 'money', 'red', '/admin/statistics', '￥' . number_format($this->getTodayRevenue(), 2)),
        ];
    }

    /**
     * 收入趋势图表
     * 作者: lauJinyu
     * 使用Chart.js创建收入趋势折线图
     */
    protected function revenueChart()
    {
        $revenueData = $this->getRevenueData();

        $options = [
            'type' => 'line',
            'data' => [
                'labels' => $revenueData['labels'],
                'datasets' => [
                    [
                        'label' => '收入趋势',
                        'data' => array_map('floatval', $revenueData['data']),
                        'borderColor' => '#36A2EB',
                        'backgroundColor' => 'rgba(54, 162, 235, 0.2)',
                        'fill' => true
                    ]
                ]
            ],
            'options' => [
                'responsive' => true,
                'maintainAspectRatio' => false,
                'scales' => [
                    'y' => [
                        'beginAtZero' => true
                    ]
                ]
            ]
        ];

        $box = new Box('收入趋势');
        $box->content(view('admin.charts.chart', ['options' => json_encode($options)]));
        return $box;
    }

    /**
     * 用户类型分布图表
     * 作者: lauJinyu
     * 使用Chart.js创建用户分布饼图
     */
    protected function userTypeChart()
    {
        $userTypeData = $this->getUserTypeData();

        $options = [
            'type' => 'pie',
            'data' => [
                'labels' => $userTypeData['labels'],
                'datasets' => [
                    [
                        'data' => array_map('intval', $userTypeData['data']),
                        'backgroundColor' => [
                            '#FF6384',
                            '#36A2EB',
                            '#FFCE56',
                            '#4BC0C0'
                        ]
                    ]
                ]
            ],
            'options' => [
                'responsive' => true,
                'maintainAspectRatio' => false,
                'plugins' => [
                    'legend' => [
                        'position' => 'right',
                        'labels' => [
                            'font' => [
                                'size' => 14
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $box = new Box('用户分布');
        $box->content(view('admin.charts.chart', ['options' => json_encode($options)]));
        return $box;
    }

    /**
     * 最近注册的代理商
     */
    protected function recentAgents()
    {
        $headers = ['姓名', '手机号', '注册时间', '状态'];
        $rows = [
            ['张三', '13800138001', '2024-01-15 10:30', '<span class="label label-success">正常</span>'],
            ['李四', '13800138002', '2024-01-14 15:20', '<span class="label label-success">正常</span>'],
            ['王五', '13800138003', '2024-01-13 09:45', '<span class="label label-warning">待审核</span>'],
            ['赵六', '13800138004', '2024-01-12 14:10', '<span class="label label-success">正常</span>'],
            ['钱七', '13800138005', '2024-01-11 16:30', '<span class="label label-danger">已禁用</span>'],
        ];

        $table = new Table($headers, $rows);

        return new Box('最近注册代理商', $table->render());
    }

    /**
     * 最近注册的商铺
     */
    protected function recentMerchants()
    {
        $headers = ['商铺名称', '联系人', '注册时间', '状态'];
        $rows = [
            ['星巴克咖啡', '张经理', '2024-01-15 11:20', '<span class="label label-success">营业中</span>'],
            ['麦当劳', '李经理', '2024-01-14 16:30', '<span class="label label-success">营业中</span>'],
            ['肯德基', '王经理', '2024-01-13 10:15', '<span class="label label-warning">待审核</span>'],
            ['必胜客', '赵经理', '2024-01-12 13:45', '<span class="label label-success">营业中</span>'],
            ['汉堡王', '钱经理', '2024-01-11 17:20', '<span class="label label-danger">已关闭</span>'],
        ];

        $table = new Table($headers, $rows);

        return new Box('最近注册商铺', $table->render());
    }

    /**
     * 系统状态
     */
    protected function systemStatus()
    {
        $status = [
            '服务器状态' => '<span class="label label-success">正常</span>',
            '数据库状态' => '<span class="label label-success">正常</span>',
            '缓存状态' => '<span class="label label-success">正常</span>',
            '队列状态' => '<span class="label label-warning">部分异常</span>',
            '存储状态' => '<span class="label label-success">正常</span>',
            '邮件服务' => '<span class="label label-success">正常</span>',
            '短信服务' => '<span class="label label-success">正常</span>',
            '支付服务' => '<span class="label label-success">正常</span>',
        ];

        $html = '<div class="row">';
        foreach ($status as $service => $state) {
            $html .= '<div class="col-md-3 col-sm-6 col-xs-12">';
            $html .= '<div class="info-box">';
            $html .= '<span class="info-box-icon bg-blue"><i class="fa fa-server"></i></span>';
            $html .= '<div class="info-box-content">';
            $html .= '<span class="info-box-text">' . $service . '</span>';
            $html .= '<span class="info-box-number">' . $state . '</span>';
            $html .= '</div></div></div>';
        }
        $html .= '</div>';

        return new Box('系统状态监控', $html);
    }

    /**
     * 获取总代理商数
     */
    protected function getTotalAgents()
    {
        // 根据权限过滤数据
        $agentQuery = Agent::query();
        if (!PermissionService::isPlatformAdmin()) {
            $agentQuery->whereIn('id', PermissionService::getAccessibleAgentIds());
        }
        return $agentQuery->count();
    }

    /**
     * 获取总商铺数
     */
    protected function getTotalMerchants()
    {
        // 根据权限过滤数据
        $merchantQuery = Store::query();
        if (!PermissionService::isPlatformAdmin()) {
            $accessibleAgentIds = PermissionService::getAccessibleAgentIds();
            $merchantQuery->whereIn('agent_id', $accessibleAgentIds);
        }
        return $merchantQuery->count();
    }

    /**
     * 获取总业务员数
     */
    protected function getTotalSalespersons()
    {
        // 根据权限过滤数据
        $salespersonQuery = Salesperson::query();
        if (!PermissionService::isPlatformAdmin()) {
            $accessibleAgentIds = PermissionService::getAccessibleAgentIds();
            $salespersonQuery->whereIn('agent_id', $accessibleAgentIds);
        }
        return $salespersonQuery->count();
    }

    /**
     * 获取总团队长数
     */
    protected function getTotalTeamLeaders()
    {
        // 这里应该从数据库获取真实数据
        return 23;
    }

    /**
     * 获取今日收入
     * 作者: lauJinyu
     * 注意: 这里应该从数据库获取真实数据
     */
    protected function getTodayRevenue()
    {
        // TODO: 从数据库获取今日收入
        // return Order::whereDate('created_at', today())->sum('amount');
        return rand(1000, 5000);
    }

    /**
     * 获取收入趋势数据
     * 作者: lauJinyu
     * 返回最近7天的收入数据用于图表显示
     */
    protected function getRevenueData()
    {
        $labels = [];
        $data = [];

        for ($i = 6; $i >= 0; $i--) {
            $date = date('m-d', strtotime("-{$i} days"));
            $labels[] = $date;
            // TODO: 从数据库获取真实收入数据
            // $revenue = Order::whereDate('created_at', date('Y-m-d', strtotime("-{$i} days")))->sum('amount');
            $data[] = rand(1000, 5000);
        }

        return [
            'labels' => $labels,
            'data' => $data
        ];
    }

    /**
     * 获取用户类型分布数据
     * 作者: lauJinyu
     * 返回各类型用户数量用于饼图显示
     */
    protected function getUserTypeData()
    {
        $labels = ['代理商', '商铺', '业务员', '团队长'];
        $data = [
            $this->getTotalAgents(),
            $this->getTotalMerchants(),
            $this->getTotalSalespersons(),
            $this->getTotalTeamLeaders()
        ];

        return [
            'labels' => $labels,
            'data' => $data
        ];
    }

    /**
     * 获取实时数据
     */
    public function getRealTimeData(Request $request)
    {
        // 检查权限
        if (!PermissionService::hasPermission('dashboard.view')) {
            abort(403, '您没有权限获取实时数据');
        }

        $type = $request->get('type', 'overview');

        switch ($type) {
            case 'overview':
                return response()->json([
                    'total_agents' => $this->getTotalAgents(),
                    'total_merchants' => $this->getTotalMerchants(),
                    'total_salespersons' => $this->getTotalSalespersons(),
                    'today_revenue' => $this->getTodayRevenue(),
                ]);

            case 'chart':
                return response()->json([
                    'revenue' => $this->getRevenueData(),
                    'user_type' => $this->getUserTypeData(),
                ]);

            default:
                return response()->json(['error' => '不支持的数据类型'], 400);
        }
    }

    /**
     * 导出数据
     */
    public function exportData(Request $request)
    {
        // 检查权限
        if (!PermissionService::hasPermission('statistics.export')) {
            abort(403, '您没有权限导出仪表板数据');
        }

        try {
            $data = [
                'total_agents' => $this->getTotalAgents(),
                'total_merchants' => $this->getTotalMerchants(),
                'total_salespersons' => $this->getTotalSalespersons(),
                'today_revenue' => $this->getTodayRevenue(),
                'revenue_data' => $this->getRevenueData(),
                'user_type_data' => $this->getUserTypeData(),
                'export_time' => now()->format('Y-m-d H:i:s'),
            ];

            $filename = 'dashboard_data_' . now()->format('YmdHis') . '.json';
            Storage::put('exports/' . $filename, json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

            return response()->download(storage_path('app/exports/' . $filename));
        } catch (Exception $e) {
            return response()->json(['error' => '导出失败：' . $e->getMessage()], 500);
        }
    }

    /**
     * 系统健康检查
     */
    public function healthCheck()
    {
        $checks = [
            'database' => $this->checkDatabase(),
            'cache' => $this->checkCache(),
            'storage' => $this->checkStorage(),
            'queue' => $this->checkQueue()
        ];

        return response()->json($checks);
    }

    /**
     * 检查数据库连接
     */
    protected function checkDatabase()
    {
        try {
            DB::connection()->getPdo();
            return ['status' => 'ok', 'message' => '数据库连接正常'];
        } catch (\Exception $e) {
            return ['status' => 'error', 'message' => '数据库连接失败: ' . $e->getMessage()];
        }
    }

    /**
     * 检查缓存
     */
    protected function checkCache()
    {
        try {
            Cache::put('health_check', 'ok', 60);
            $value = Cache::get('health_check');
            return $value === 'ok'
                ? ['status' => 'ok', 'message' => '缓存服务正常']
                : ['status' => 'error', 'message' => '缓存服务异常'];
        } catch (\Exception $e) {
            return ['status' => 'error', 'message' => '缓存服务失败: ' . $e->getMessage()];
        }
    }

    /**
     * 检查存储
     */
    protected function checkStorage()
    {
        try {
            $disk = Storage::disk('public');
            $testFile = 'health_check.txt';
            $disk->put($testFile, 'test');
            $content = $disk->get($testFile);
            $disk->delete($testFile);

            return $content === 'test'
                ? ['status' => 'ok', 'message' => '存储服务正常']
                : ['status' => 'error', 'message' => '存储服务异常'];
        } catch (\Exception $e) {
            return ['status' => 'error', 'message' => '存储服务失败: ' . $e->getMessage()];
        }
    }

    /**
     * 检查队列
     */
    protected function checkQueue()
    {
        try {
            // 这里可以检查队列连接状态
            return ['status' => 'ok', 'message' => '队列服务正常'];
        } catch (\Exception $e) {
            return ['status' => 'error', 'message' => '队列服务失败: ' . $e->getMessage()];
        }
    }
}
