<?php

namespace App\Providers;

use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;
use App\Services\PermissionService;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     */
    protected $policies = [
        // 'App\Models\Model' => 'App\Policies\ModelPolicy',
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        $this->registerPolicies();

        // 注册代理商相关权限
        $this->registerAgentPermissions();

        // 注册商铺相关权限
        $this->registerStorePermissions();

        // 注册业务员相关权限
        $this->registerSalespersonPermissions();

        // 注册素材相关权限
        $this->registerMaterialPermissions();

        // 注册统计相关权限
        $this->registerStatisticsPermissions();
    }

    /**
     * 注册代理商相关权限
     */
    private function registerAgentPermissions()
    {
        Gate::define('agent.list', function ($user) {
            return PermissionService::canPerformAction('view_agents');
        });

        Gate::define('agent.create', function ($user) {
            return PermissionService::canPerformAction('create_agent');
        });

        Gate::define('agent.edit', function ($user, $agentId = null) {
            return PermissionService::canPerformAction('edit_agent', $agentId);
        });

        Gate::define('agent.delete', function ($user, $agentId = null) {
            return PermissionService::canPerformAction('delete_agent', $agentId);
        });

        Gate::define('agent.hierarchy', function ($user) {
            return PermissionService::canPerformAction('view_hierarchy');
        });
    }

    /**
     * 注册商铺相关权限
     */
    private function registerStorePermissions()
    {
        Gate::define('store.list', function ($user) {
            return PermissionService::canPerformAction('view_stores');
        });

        Gate::define('store.create', function ($user) {
            return PermissionService::canPerformAction('create_store');
        });

        Gate::define('store.edit', function ($user, $storeId = null) {
            return PermissionService::canPerformAction('edit_store', $storeId);
        });

        Gate::define('store.delete', function ($user, $storeId = null) {
            return PermissionService::canPerformAction('delete_store', $storeId);
        });
    }

    /**
     * 注册业务员相关权限
     */
    private function registerSalespersonPermissions()
    {
        Gate::define('salesperson.list', function ($user) {
            return PermissionService::canPerformAction('view_salespersons');
        });

        Gate::define('salesperson.create', function ($user) {
            return PermissionService::canPerformAction('create_salesperson');
        });

        Gate::define('salesperson.edit', function ($user, $salespersonId = null) {
            return PermissionService::canPerformAction('edit_salesperson', $salespersonId);
        });
    }

    /**
     * 注册素材相关权限
     */
    private function registerMaterialPermissions()
    {
        Gate::define('material.list', function ($user) {
            return true; // 所有登录用户都可以查看素材
        });

        Gate::define('material.create', function ($user) {
            return PermissionService::canPerformAction('create_material');
        });

        Gate::define('material.edit', function ($user, $materialId = null) {
            return PermissionService::canPerformAction('edit_material', $materialId);
        });
    }

    /**
     * 注册统计相关权限
     */
    private function registerStatisticsPermissions()
    {
        Gate::define('statistics.view', function ($user) {
            return PermissionService::canPerformAction('view_statistics');
        });

        Gate::define('statistics.export', function ($user) {
            return PermissionService::canPerformAction('export_data');
        });
    }
}
