<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

/**
 * 认证API控制器
 * 
 * @apiDefine AuthGroup 认证相关接口
 */
class AuthController extends Controller
{
    /**
     * 获取访问令牌
     * 
     * @api {POST} /api/auth/access-token 获取Access Token
     * @apiName GetAccessToken
     * @apiGroup AuthGroup
     * @apiDescription 通过appKey和appSecret获取access_token，用于后续API调用认证
     * 
     * @apiParam {String} appKey 应用密钥
     * @apiParam {String} appSecret 应用密码
     * @apiParam {String} [grant_type="client_credentials"] 授权类型，默认为client_credentials
     * 
     * @apiSuccess {Number} code 响应状态码，200表示成功
     * @apiSuccess {String} message 响应消息
     * @apiSuccess {Object} data 响应数据
     * @apiSuccess {String} data.access_token 访问令牌
     * @apiSuccess {String} data.token_type 令牌类型，通常为"Bearer"
     * @apiSuccess {Number} data.expires_in 令牌过期时间（秒）
     * @apiSuccess {String} data.scope 授权范围
     * @apiSuccess {Number} timestamp 时间戳
     * 
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "code": 200,
     *       "message": "获取access_token成功",
     *       "data": {
     *         "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
     *         "token_type": "Bearer",
     *         "expires_in": 7200,
     *         "scope": "read write"
     *       },
     *       "timestamp": 1640995200
     *     }
     * 
     * @apiError {Number} code 错误状态码
     * @apiError {String} message 错误信息
     * @apiError {Number} timestamp 时间戳
     * 
     * @apiErrorExample Error-Response:
     *     HTTP/1.1 400 Bad Request
     *     {
     *       "code": 400,
     *       "message": "无效的appKey或appSecret",
     *       "timestamp": 1640995200
     *     }
     */
    public function getAccessToken(Request $request)
    {
        try {
            // 检查请求Content-Type，支持JSON和form-data
            $isJson = $request->isJson() || $request->header('Content-Type') === 'application/json';

            // 验证请求参数
            $validated = $request->validate([
                'appKey' => 'required|string',
                'appSecret' => 'required|string',
                'grant_type' => 'string|in:client_credentials'
            ]);

            $appKey = $validated['appKey'];
            $appSecret = $validated['appSecret'];
            $grantType = $validated['grant_type'] ?? 'client_credentials';

            // 记录请求信息
            Log::info('Access Token 请求', [
                'appKey' => $appKey,
                'grant_type' => $grantType,
                'content_type' => $request->header('Content-Type'),
                'is_json' => $isJson
            ]);

            // 验证appKey和appSecret
            if (!$this->validateCredentials($appKey, $appSecret)) {
                return $this->errorResponse('无效的appKey或appSecret', 400);
            }

            // 检查是否已有有效的token缓存
            $cacheKey = "access_token:{$appKey}";
            $cachedToken = Cache::get($cacheKey);

            if ($cachedToken) {
                Log::info('返回缓存的access_token', ['appKey' => $appKey]);
                return $this->successResponse($cachedToken, '获取access_token成功（缓存）');
            }

            // 生成新的access_token
            $tokenData = $this->generateAccessToken($appKey, $appSecret, $grantType);

            // 缓存token（有效期2小时）
            Cache::put($cacheKey, $tokenData, 7200);

            Log::info('生成新的access_token', [
                'appKey' => $appKey,
                'expires_in' => $tokenData['expires_in']
            ]);

            return $this->successResponse($tokenData, '获取access_token成功');
        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->errorResponse('参数验证失败: ' . implode(', ', $e->validator->errors()->all()), 422);
        } catch (\Exception $e) {
            Log::error('获取access_token失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse('服务器内部错误', 500);
        }
    }

    /**
     * 验证凭据
     * 
     * @param string $appKey
     * @param string $appSecret
     * @return bool
     */
    private function validateCredentials($appKey, $appSecret)
    {
        // 预定义的有效凭据 - 实际项目中应从数据库或配置文件读取
        $validCredentials = [
            'red.qzgiaXL14ncBwlBz' => 'cf2ef172726d1272c4ed661e1c059a45'
        ];

        return isset($validCredentials[$appKey]) &&
            $validCredentials[$appKey] === $appSecret;
    }

    /**
     * 生成访问令牌
     * 
     * @param string $appKey
     * @param string $appSecret
     * @param string $grantType
     * @return array
     */
    private function generateAccessToken($appKey, $appSecret, $grantType)
    {
        // 生成JWT或简单token - 这里使用简单的加密token
        $payload = [
            'app_key' => $appKey,
            'iat' => time(),
            'exp' => time() + 7200, // 2小时过期
            'scope' => 'read write'
        ];

        // 生成token（实际项目中建议使用JWT）
        $token = base64_encode(json_encode($payload) . '.' . hash_hmac('sha256', json_encode($payload), $appSecret));

        return [
            'access_token' => $token,
            'token_type' => 'Bearer',
            'expires_in' => 7200,
            'scope' => 'read write'
        ];
    }

    /**
     * 验证访问令牌
     * 
     * @api {POST} /api/auth/verify-token 验证Access Token
     * @apiName VerifyAccessToken
     * @apiGroup AuthGroup
     * @apiDescription 验证access_token的有效性
     * 
     * @apiParam {String} access_token 访问令牌
     * 
     * @apiSuccess {Number} code 响应状态码，200表示成功
     * @apiSuccess {String} message 响应消息
     * @apiSuccess {Object} data 响应数据
     * @apiSuccess {Boolean} data.valid 令牌是否有效
     * @apiSuccess {Object} data.payload 令牌载荷信息
     * @apiSuccess {Number} timestamp 时间戳
     */
    public function verifyToken(Request $request)
    {
        try {
            $validated = $request->validate([
                'access_token' => 'required|string'
            ]);

            $token = $validated['access_token'];
            $verification = $this->validateAccessToken($token);

            if ($verification['valid']) {
                return $this->successResponse($verification, '令牌验证成功');
            } else {
                return $this->errorResponse($verification['message'], 401);
            }
        } catch (\Exception $e) {
            Log::error('令牌验证失败', [
                'error' => $e->getMessage()
            ]);
            return $this->errorResponse('令牌验证失败', 401);
        }
    }

    /**
     * 验证访问令牌
     * 
     * @param string $token
     * @return array
     */
    private function validateAccessToken($token)
    {
        try {
            // 解析token
            $decoded = base64_decode($token);

            // 检查是否能够正确解码
            if ($decoded === false) {
                return ['valid' => false, 'message' => '令牌格式无效'];
            }

            $parts = explode('.', $decoded);

            if (count($parts) !== 2) {
                return ['valid' => false, 'message' => '令牌格式无效'];
            }

            $payload = json_decode($parts[0], true);

            if (!$payload) {
                return ['valid' => false, 'message' => '令牌载荷无效'];
            }

            // 检查过期时间
            if (isset($payload['exp']) && $payload['exp'] < time()) {
                return ['valid' => false, 'message' => '令牌已过期'];
            }

            // 检查必要字段
            if (!isset($payload['app_key']) || !isset($payload['iat'])) {
                return ['valid' => false, 'message' => '令牌载荷不完整'];
            }

            return [
                'valid' => true,
                'payload' => $payload,
                'message' => '令牌有效'
            ];
        } catch (\Exception $e) {
            return ['valid' => false, 'message' => '令牌解析失败: ' . $e->getMessage()];
        }
    }

    /**
     * 成功响应
     * 
     * @param mixed $data
     * @param string $message
     * @return \Illuminate\Http\JsonResponse
     */
    private function successResponse($data, $message = '操作成功')
    {
        return response()->json([
            'code' => 200,
            'message' => $message,
            'data' => $data,
            'timestamp' => time()
        ]);
    }

    /**
     * 错误响应
     * 
     * @param string $message
     * @param int $code
     * @return \Illuminate\Http\JsonResponse
     */
    private function errorResponse($message, $code = 400)
    {
        return response()->json([
            'code' => $code,
            'message' => $message,
            'timestamp' => time()
        ], $code);
    }
}
