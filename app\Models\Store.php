<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * 商铺模型
 * 
 * @property int $id
 * @property string $name 商铺名称
 * @property string|null $logo_url 商铺Logo
 * @property string|null $banner_url 横幅图片
 * @property string|null $description 商铺描述
 * @property string $contact_person 联系人
 * @property string $phone 联系电话
 * @property string|null $email 邮箱
 * @property string $address 商铺地址
 * @property float|null $latitude 纬度
 * @property float|null $longitude 经度
 * @property string|null $business_hours 营业时间
 * @property string|null $nfc_chip_id NFC芯片ID
 * @property int|null $agent_id 关联代理商ID
 * @property string $audit_status 审核状态
 * @property string $store_status 商铺状态
 * @property string|null $audit_remark 审核备注
 * @property \Carbon\Carbon|null $audit_time 审核时间
 * @property int|null $auditor_id 审核人ID
 * @property array|null $feature_config 功能配置
 * @property string|null $remark 备注
 * @property string $nfc_chip_status NFC芯片状态
 * @property array|null $promotion_permissions 推广权限
 * @property \Carbon\Carbon|null $last_active_at 最后活跃时间
 * @property int $total_views 总浏览量
 * @property int $total_promotions 总推广次数
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class Store extends Model
{
    use HasFactory;

    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'stores';

    /**
     * 可批量赋值的字段
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'logo_url',
        'banner_url',
        'description',
        'contact_person',
        'phone',
        'email',
        'address',
        'latitude',
        'longitude',
        'business_hours',
        'nfc_chip_id',
        'agent_id',
        'audit_status',
        'store_status',
        'audit_remark',
        'audit_time',
        'auditor_id',
        'feature_config',
        'remark',
        'nfc_chip_status',
        'promotion_permissions',
        'last_active_at',
        'total_views',
        'total_promotions',
        'settlement_status',
        'last_settlement_id',
        'product_amount',
        'is_settled',
    ];

    /**
     * 字段类型转换
     *
     * @var array
     */
    protected $casts = [
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'feature_config' => 'array',
        'promotion_permissions' => 'array',
        'audit_time' => 'datetime',
        'last_active_at' => 'datetime',
        'total_views' => 'integer',
        'total_promotions' => 'integer',
        'product_amount' => 'decimal:2',
        'is_settled' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 审核状态常量
     */
    const AUDIT_STATUS_PENDING = 'pending';
    const AUDIT_STATUS_APPROVED = 'approved';
    const AUDIT_STATUS_REJECTED = 'rejected';

    /**
     * 商铺状态常量
     */
    const STORE_STATUS_ACTIVE = 'active';
    const STORE_STATUS_INACTIVE = 'inactive';
    const STORE_STATUS_SUSPENDED = 'suspended';

    /**
     * NFC芯片状态常量
     */
    const NFC_STATUS_UNBOUND = 'unbound';
    const NFC_STATUS_BOUND = 'bound';
    const NFC_STATUS_DISABLED = 'disabled';

    /**
     * 获取审核状态选项
     *
     * @return array
     */
    public static function getAuditStatusOptions()
    {
        return [
            self::AUDIT_STATUS_PENDING => '待审核',
            self::AUDIT_STATUS_APPROVED => '已通过',
            self::AUDIT_STATUS_REJECTED => '已拒绝',
        ];
    }

    /**
     * 获取商铺状态选项
     *
     * @return array
     */
    public static function getStoreStatusOptions()
    {
        return [
            self::STORE_STATUS_ACTIVE => '营业中',
            self::STORE_STATUS_INACTIVE => '已关闭',
            self::STORE_STATUS_SUSPENDED => '已暂停',
        ];
    }

    /**
     * 获取NFC状态选项
     *
     * @return array
     */
    public static function getNfcStatusOptions()
    {
        return [
            self::NFC_STATUS_UNBOUND => '未绑定',
            self::NFC_STATUS_BOUND => '已绑定',
            self::NFC_STATUS_DISABLED => '已禁用',
        ];
    }

    /**
     * 关联代理商
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function agent()
    {
        return $this->belongsTo(Agent::class);
    }

    /**
     * 关联审核人
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function auditor()
    {
        return $this->belongsTo(User::class, 'auditor_id');
    }

    /**
     * 获取审核状态标签
     *
     * @return string
     */
    public function getAuditStatusLabelAttribute()
    {
        return self::getAuditStatusOptions()[$this->audit_status] ?? $this->audit_status;
    }

    /**
     * 获取商铺状态标签
     *
     * @return string
     */
    public function getStoreStatusLabelAttribute()
    {
        return self::getStoreStatusOptions()[$this->store_status] ?? $this->store_status;
    }

    /**
     * 获取NFC状态标签
     *
     * @return string
     */
    public function getNfcStatusLabelAttribute()
    {
        return self::getNfcStatusOptions()[$this->nfc_chip_status] ?? $this->nfc_chip_status;
    }

    /**
     * 获取代理商名称
     *
     * @return string
     */
    public function getAgentNameAttribute()
    {
        return $this->agent ? $this->agent->name : '无';
    }

    /**
     * 获取完整地址（包含坐标）
     *
     * @return string
     */
    public function getFullAddressAttribute()
    {
        $address = $this->address;
        if ($this->latitude && $this->longitude) {
            $address .= ' (' . $this->latitude . ', ' . $this->longitude . ')';
        }
        return $address;
    }

    /**
     * 查询作用域：按审核状态筛选
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $status
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByAuditStatus($query, $status)
    {
        return $query->where('audit_status', $status);
    }

    /**
     * 查询作用域：按商铺状态筛选
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $status
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByStoreStatus($query, $status)
    {
        return $query->where('store_status', $status);
    }

    /**
     * 查询作用域：按代理商筛选
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $agentId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByAgent($query, $agentId)
    {
        return $query->where('agent_id', $agentId);
    }

    /**
     * 查询作用域：已通过审核
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeApproved($query)
    {
        return $query->where('audit_status', self::AUDIT_STATUS_APPROVED);
    }

    /**
     * 查询作用域：营业中
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('store_status', self::STORE_STATUS_ACTIVE);
    }

    /**
     * 检查是否可以推广
     *
     * @return bool
     */
    public function canPromote()
    {
        return $this->audit_status === self::AUDIT_STATUS_APPROVED
            && $this->store_status === self::STORE_STATUS_ACTIVE;
    }

    /**
     * 增加浏览量
     *
     * @return void
     */
    public function incrementViews()
    {
        $this->increment('total_views');
        $this->update(['last_active_at' => now()]);
    }

    /**
     * 增加推广次数
     *
     * @return void
     */
    public function incrementPromotions()
    {
        $this->increment('total_promotions');
        $this->update(['last_active_at' => now()]);
    }
}
