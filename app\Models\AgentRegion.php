<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * 代理商区域配置模型
 * 
 * @property int $id
 * @property int $agent_id 代理商ID
 * @property int $area_id 区域ID(areas表的id)
 * @property int|null $province_id 省份ID
 * @property int|null $city_id 城市ID  
 * @property int|null $district_id 区县ID
 * @property string|null $province_name 省份名称
 * @property string|null $city_name 城市名称
 * @property string|null $district_name 区县名称
 * @property string|null $province_code 省份代码
 * @property string|null $city_code 城市代码
 * @property string|null $district_code 区县代码
 * @property bool $is_exclusive 是否独占
 * @property string|null $remark 备注
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class AgentRegion extends Model
{
    use HasFactory;

    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'agent_regions';

    /**
     * 可批量赋值的字段
     *
     * @var array
     */
    protected $fillable = [
        'agent_id',
        'area_id',
        'province_id',
        'city_id',
        'district_id',
        'province_name',
        'province_code',
        'city_name',
        'city_code',
        'district_name',
        'district_code',
        'full_region',
        'is_exclusive',
        'remark',
        'region_status',
        
        // Step2: 合约信息
        'contract_status',
        'contract_no',
        'contract_title',
        'signed_at',
        'contract_start_date',
        'contract_end_date',
        'contract_file_url',
        
        // Step3: 返佣配置
        'commission_type',
        'commission_rate',
        'min_commission_amount',
        'max_commission_amount',
        
        // Step4: 结算配置
        'settlement_cycle',
        'settlement_day',
        'settlement_bank_account',
        'settlement_bank_name',
        'settlement_account_name',
        
        // 系统字段
        'next_settlement_at',
        'last_settlement_at',
        'total_settled_amount',
        'pending_settlement_amount',
        'total_stores',
        'active_stores',
        'total_revenue',
        'last_updated_stats_at',
        'contract_notes',
        'commission_amount',
        'min_order_amount',
        'max_commission_per_order',
        'max_commission_per_month',
        'commission_rules',
        'commission_effective_date',
        'commission_expire_date',
        'settlement_notes',
        'notes',
        'created_by',
        'contact_info',
        'bank_branch',
    ];

    /**
     * 字段类型转换
     *
     * @var array
     */
    protected $casts = [
        'is_exclusive' => 'boolean',
        'signed_at' => 'date',
        'contract_start_date' => 'date',
        'contract_end_date' => 'date',
        'commission_rate' => 'decimal:2',
        'min_commission_amount' => 'decimal:2',
        'max_commission_amount' => 'decimal:2',
        'total_settled_amount' => 'decimal:2',
        'pending_settlement_amount' => 'decimal:2',
        'total_revenue' => 'decimal:2',
        'last_settlement_at' => 'datetime',
        'next_settlement_at' => 'date',
        'last_updated_stats_at' => 'datetime',
        'total_stores' => 'integer',
        'active_stores' => 'integer',
        'settlement_day' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'commission_amount' => 'decimal:2',
        'min_order_amount' => 'decimal:2',
        'max_commission_per_order' => 'decimal:2',
        'max_commission_per_month' => 'decimal:2',
        'commission_effective_date' => 'date',
        'commission_expire_date' => 'date',
        'bank_branch' => 'string',
    ];

    /**
     * 关联代理商
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function agent()
    {
        return $this->belongsTo(Agent::class);
    }

    /**
     * 关联省份
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function province()
    {
        return $this->belongsTo(Area::class, 'province_id', 'id');
    }

    /**
     * 关联城市
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function city()
    {
        return $this->belongsTo(Area::class, 'city_id', 'id');
    }

    /**
     * 关联区县
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function district()
    {
        return $this->belongsTo(Area::class, 'district_id', 'id');
    }

    /**
     * 关联区域(areas表)
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function area()
    {
        return $this->belongsTo(Area::class, 'area_id', 'id');
    }

    /**
     * 获取完整的地区名称
     *
     * @return string
     */
    public function getFullRegionAttribute()
    {
        $parts = array_filter([$this->province_name, $this->city_name, $this->district_name]);
        return implode(' ', $parts);
    }

    /**
     * 获取简化的地区名称（用于列表显示）
     *
     * @return string
     */
    public function getShortRegionAttribute()
    {
        if ($this->district_name) {
            return $this->province_name . ' ' . $this->city_name . ' ' . $this->district_name;
        } elseif ($this->city_name) {
            return $this->province_name . ' ' . $this->city_name;
        } else {
            return $this->province_name;
        }
    }

    /**
     * 获取是否独占的中文显示
     *
     * @return string
     */
    public function getIsExclusiveTextAttribute()
    {
        return $this->is_exclusive ? '是' : '否';
    }

    /**
     * 查询作用域：根据代理商ID筛选
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $agentId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByAgent($query, $agentId)
    {
        return $query->where('agent_id', $agentId);
    }

    /**
     * 查询作用域：根据地区筛选
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int|null $provinceId
     * @param int|null $cityId
     * @param int|null $districtId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByRegion($query, $provinceId = null, $cityId = null, $districtId = null)
    {
        if ($provinceId) {
            $query->where('province_id', $provinceId);
        }
        
        if ($cityId) {
            $query->where('city_id', $cityId);
        }
        
        if ($districtId) {
            $query->where('district_id', $districtId);
        }
        
        return $query;
    }

    /**
     * 查询作用域：只查询独占区域
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeExclusive($query)
    {
        return $query->where('is_exclusive', true);
    }

    /**
     * 检查区域是否被其他代理商独占
     *
     * @param int $areaId
     * @param int|null $excludeAgentId 排除的代理商ID（用于编辑时检查）
     * @return bool
     */
    public static function isAreaExclusive($areaId, $excludeAgentId = null)
    {
        $query = static::where('area_id', $areaId)->where('is_exclusive', true);
        
        if ($excludeAgentId) {
            $query->where('agent_id', '!=', $excludeAgentId);
        }
        
        return $query->exists();
    }

    /**
     * 获取完整的区域名称
     */
    public function getFullRegionNameAttribute()
    {
        $parts = array_filter([
            $this->province_name,
            $this->city_name,
            $this->district_name
        ]);
        
        return implode(',', $parts);
    }

    /**
     * 获取区域独占状态描述
     */
    public function getExclusiveStatusAttribute()
    {
        return $this->is_exclusive ? '独占' : '非独占';
    }

    /**
     * 获取合约状态描述
     */
    public function getContractStatusTextAttribute()
    {
        $statusMap = [
            'draft' => '草案',
            'signed' => '已签约',
            'expired' => '已过期',
            'terminated' => '已终止',
        ];
        
        return $statusMap[$this->contract_status] ?? '未知';
    }

    /**
     * 获取佣金比例（处理可空值）
     */
    public function getCommissionRateDisplayAttribute()
    {
        return $this->commission_rate ? $this->commission_rate . '%' : '未设置';
    }

    /**
     * 获取结算周期描述（处理可空值）
     */
    public function getSettlementCycleTextAttribute()
    {
        if (!$this->settlement_cycle) {
            return '未设置';
        }
        
        $cycleMap = [
            'weekly' => '每周',
            'monthly' => '每月',
            'quarterly' => '每季度',
        ];
        
        return $cycleMap[$this->settlement_cycle] ?? '未知';
    }

    /**
     * 获取区域状态描述
     */
    public function getRegionStatusTextAttribute()
    {
        $statusMap = [
            'active' => '活跃',
            'inactive' => '非活跃',
            'suspended' => '暂停',
            'terminated' => '终止',
        ];
        
        return $statusMap[$this->region_status] ?? '未知';
    }

    /**
     * 获取佣金类型描述（处理可空值）
     */
    public function getCommissionTypeTextAttribute()
    {
        if (!$this->commission_type) {
            return '未设置';
        }
        
        $typeMap = [
            'percentage' => '百分比',
            'fixed' => '固定金额',
        ];
        
        return $typeMap[$this->commission_type] ?? '未知';
    }

    /**
     * 获取统计数据显示（处理可空值）
     */
    public function getStoresStatsDisplayAttribute()
    {
        if ($this->total_stores === null && $this->active_stores === null) {
            return '暂无统计';
        }
        
        $total = $this->total_stores ?? 0;
        $active = $this->active_stores ?? 0;
        
        return "总数: {$total} | 活跃: {$active}";
    }

    /**
     * 获取收入统计显示（处理可空值）
     */
    public function getRevenueDisplayAttribute()
    {
        if ($this->total_revenue === null) {
            return '暂无统计';
        }
        
        return '¥' . number_format($this->total_revenue, 2);
    }

    /**
     * 检查是否已配置返佣信息
     */
    public function hasCommissionConfig()
    {
        return $this->commission_rate !== null && $this->commission_type !== null;
    }

    /**
     * 检查是否已配置结算信息
     */
    public function hasSettlementConfig()
    {
        return $this->settlement_cycle !== null && $this->settlement_day !== null;
    }

    /**
     * 获取配置完整性状态
     */
    public function getConfigStatusAttribute()
    {
        $status = [];
        
        if (!$this->hasCommissionConfig()) {
            $status[] = '缺少返佣配置';
        }
        
        if (!$this->hasSettlementConfig()) {
            $status[] = '缺少结算配置';
        }
        
        if (empty($status)) {
            return '配置完整';
        }
        
        return implode('、', $status);
    }

    /**
     * 计算下次结算日期
     */
    public function calculateNextSettlementDate()
    {
        if (!$this->settlement_cycle || !$this->settlement_day) {
            return null;
        }

        $now = now();
        
        switch ($this->settlement_cycle) {
            case 'weekly':
                // 每周结算，settlement_day表示星期几（1-7）
                $nextDate = $now->next($this->settlement_day);
                break;
                
            case 'monthly':
                // 每月结算，settlement_day表示每月几号（1-31）
                $nextDate = $now->copy()->day($this->settlement_day);
                if ($nextDate <= $now) {
                    $nextDate->addMonth();
                }
                break;
                
            case 'quarterly':
                // 每季度结算，settlement_day表示季度内几号
                $currentQuarter = ceil($now->month / 3);
                $quarterStartMonth = ($currentQuarter - 1) * 3 + 1;
                $nextDate = $now->copy()->month($quarterStartMonth)->day($this->settlement_day);
                
                if ($nextDate <= $now) {
                    $nextDate->addQuarter();
                }
                break;
                
            default:
                return null;
        }

        return $nextDate->toDateString();
    }

    /**
     * 更新统计数据
     */
    public function updateStatistics()
    {
        // 🔄开发阶段：模拟统计数据更新
        // 🚀联调阶段：实现真实的统计计算逻辑
        
        $this->update([
            'last_updated_stats_at' => now(),
            // 其他统计字段的计算将在联调阶段实现
        ]);
    }
}
