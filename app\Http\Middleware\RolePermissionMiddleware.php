<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Services\PermissionService;
use Symfony\Component\HttpFoundation\Response;

class RolePermissionMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, ...$permissions): Response
    {
        // 检查用户是否已登录
        if (!auth('admin')->check()) {
            return redirect(admin_url('auth/login'));
        }

        // 如果没有指定权限要求，直接通过
        if (empty($permissions)) {
            return $next($request);
        }

        // 平台管理员拥有所有权限
        if (PermissionService::isPlatformAdmin()) {
            return $next($request);
        }

        // 检查每个权限
        foreach ($permissions as $permission) {
            if (!$this->checkPermission($permission, $request)) {
                abort(403, '您没有权限执行此操作');
            }
        }

        return $next($request);
    }

    /**
     * 检查具体权限
     */
    private function checkPermission(string $permission, Request $request): bool
    {
        $routeParams = $request->route()->parameters();

        switch ($permission) {
            case 'agent.view':
                return $this->checkAgentViewPermission($routeParams);

            case 'agent.edit':
                return $this->checkAgentEditPermission($routeParams);

            case 'agent.create':
                return PermissionService::canPerformAction('create_agent');

            case 'store.view':
                return $this->checkStoreViewPermission($routeParams);

            case 'store.edit':
                return $this->checkStoreEditPermission($routeParams);

            case 'store.create':
                return PermissionService::canPerformAction('create_store');

            case 'material.view':
                return true; // 所有登录用户都可以查看素材

            case 'statistics.view':
                return PermissionService::canPerformAction('view_statistics');

            case 'data.export':
                return PermissionService::canPerformAction('export_data');

            default:
                return false;
        }
    }

    /**
     * 检查代理商查看权限
     */
    private function checkAgentViewPermission(array $routeParams): bool
    {
        if (isset($routeParams['id'])) {
            return PermissionService::canAccessAgent((int)$routeParams['id']);
        }
        return true; // 列表页面会在控制器中进行数据过滤
    }

    /**
     * 检查代理商编辑权限
     */
    private function checkAgentEditPermission(array $routeParams): bool
    {
        if (isset($routeParams['id'])) {
            return PermissionService::canPerformAction('edit_agent', (int)$routeParams['id']);
        }
        return false;
    }

    /**
     * 检查商铺查看权限
     */
    private function checkStoreViewPermission(array $routeParams): bool
    {
        if (isset($routeParams['id'])) {
            return PermissionService::canAccessStore((int)$routeParams['id']);
        }
        return true; // 列表页面会在控制器中进行数据过滤
    }

    /**
     * 检查商铺编辑权限
     */
    private function checkStoreEditPermission(array $routeParams): bool
    {
        if (isset($routeParams['id'])) {
            return PermissionService::canPerformAction('edit_store', (int)$routeParams['id']);
        }
        return false;
    }
}
