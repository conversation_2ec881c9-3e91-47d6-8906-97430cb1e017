## 管理平台功能结构文档

### 0. 技术架构说明

**0.1 开发框架与标准**
- **后端框架：** Laravel 9+ + Laravel-Admin
- **开发规范：** 严格遵循MVC架构模式和PSR-4编码标准
- **代码分离原则：** PHP/HTML/JavaScript严格分离，确保代码结构清晰和可维护性
- **开发环境：** Windows系统，VSCode + Intelephense，严格的代码格式化和类型检查

**0.2 MVC架构严格分离标准**
- **Controller层：** 严格禁止直接数据库操作、HTML代码、echo语句，只负责业务逻辑和API响应
- **Model层：** 严格禁止HTML代码和echo语句，只负责数据模型和数据库交互
- **View层：** 只负责HTML结构和模板渲染，不包含业务逻辑
- **JavaScript层：** 负责数据渲染、用户交互、Ajax通信，实现数据层与视图层分离

**0.3 开发流程规范**
- **列表页面：** 通过Laravel-Admin系统方法生成，遵循grid()模式
- **非列表页面：** 开发顺序为 View → Model → Controller → JavaScript
- **数据交互：** Controller提供API方法，JavaScript调用实现数据交互
- **错误处理：** 前后端双重错误处理，完善的调试日志记录

**0.4 Laravel Admin开发黄金法则**
- **代码分离：** PHP/HTML/JS严格分离，避免代码耦合
- **依赖管理：** 避免循环依赖，保持模块独立性
- **PJAX兼容：** 支持页面无刷新切换，提升用户体验
- **调试优先：** 完善的错误处理和日志记录，便于问题定位

### 1. 项目概述

本项目旨在通过NFC技术，实现对商家的多媒体运营推广。用户通过手机触碰商家NFC芯片，即可打开一个H5页面（如您提供的截图所示）。在该H5页面中，用户可以选择不同的功能维度，例如：

*   **发视频：** 用户可以选择将视频发布到抖音、小红书、视频号、快手等平台。
*   **点评打卡：** 用户可以选择在小红书笔记、大众点评、抖音点评、高德点评、美团点评等平台进行点评打卡。
*   **微信营销：** 用户可以选择添加微信好友或发布朋友圈。
*   **商家团购：** 用户可以选择参与大众点评团或抖音团购。
*   **关注账号：** 用户可以选择关注抖音或小红书账号。

当用户选择对应的媒体平台（如抖音）后，系统将自动打开该平台的视频发送页面，并携带预设的视频文件和文案，实现"一键发送"。不同平台会携带对应平台的视频、图片以及文案，用户无需自行准备任何素材，即可为商铺进行推广。

本项目主要分为以下几个端：

*   **用户端：** 即用户通过NFC触碰后打开的H5页面，提供上述多媒体运营推广功能，是商家推广内容触达最终用户的直接界面。
*   **商家端：** 商家可以通过该端查看其推广数据，了解推广效果，并配置该商家的推广素材，实现对自身推广内容的自主管理和优化。
*   **招募端：** 用于代理商或平台自营招募业务员，推广该系统给商家，是拓展商家规模、扩大市场覆盖的关键环节。
*   **管理平台：** 本文档主要描述的部分，作为整个系统的核心管理中枢，其功能用途在于：
    *   **代理商管理：** 负责对代理商进行全面的生命周期管理，包括账号分配、权限配置、返佣结算等，确保代理商体系的健康运作。
    *   **商铺管理：** 集中管理所有入驻商铺的信息、推广配置和数据，是平台对商家资源进行统一调配和监控的入口。
    *   **多媒体配置：** 灵活配置和维护H5页面中展示的各类媒体平台及其对应的推广规则和素材要求，保证系统能够适应不断变化的市场需求和平台规则。
    *   **素材库管理：** 统一管理平台所有可用的推广素材，并进行审核、分类和标签化，为商家提供丰富且合规的推广内容选择。
    *   **业务员管理：** 对业务员进行精细化管理，包括业绩考核、提成结算、团队归属等，激励业务员高效推广。
    *   **数据统计与分析：** 收集、汇总并分析用户端、商家端、招募端等各环节产生的运营数据，为平台决策者提供全面的数据支持，以便进行市场策略调整、产品功能优化和资源分配。
    *   **权限与系统设置：** 确保系统安全稳定运行，通过精细的权限控制保障数据安全，并通过系统配置满足日常运营和维护的需求。

### 2. 模块划分

#### 2.1. 代理商管理

*   **代理商列表：**
    *   查看所有代理商信息，包括代理商名称、联系方式、注册时间、状态等。
    *   支持代理商的添加、编辑、删除操作。
    *   支持代理商账号的启用/禁用。
    *   支持代理商的搜索和筛选。
*   **区域配置：**
    *   配置代理商的管理区域（省市区三级联动选择）。
    *   配置代理商的返佣比例、结算周期（支持自定义周期，如周结、月结、季结等）。
    *   配置代理商的权限，包括可管理的模块和功能。
    *   配置代理商的专属推广链接或二维码。
*   **层级管理：**
    *   **层级结构展示：** 
        *   可视化展示代理商层级关系，支持树形结构和层级路径导航
        *   统计展示各层级代理商数量和商铺分布情况
        *   支持ECharts图表展示层级结构，便于直观理解层级关系
    *   **下级代理商管理：**
        *   一级代理商可以创建和管理二级代理商
        *   配置下级代理商的基本信息、区域权限和业务配置
        *   设置下级代理商数量限制和直推商铺数量限制
        *   支持下级代理商的推广码生成和返佣比例设置
    *   **层级详情管理：**
        *   查看代理商的上级关系和下级结构
        *   展示代理商的层级路径和业务范围
        *   统计下级代理商的业绩和商铺推广情况
        *   支持层级关系的调整和管理操作
    *   **数量限制管理：**
        *   设置代理商可发展的下级代理商数量上限
        *   设置代理商可直推的商铺数量上限
        *   支持进度条展示当前使用情况和剩余配额
        *   提供超限提醒和管理功能
*   **结算与分佣管理：**
    *   查看所有佣金结算记录，包括结算周期、结算金额、结算状态、代理商信息等。
    *   支持结算记录的查询、筛选和导出。
    *   提供详细的结算信息展示，包括代理商基本信息、区域配置、关联商铺等。
    *   显示结算计算公式和明细数据，确保结算透明度。
    *   支持手动触发结算流程和自动定时结算。
    *   提供结算审核功能，确保结算数据准确性。
    *   记录结算操作日志，便于审计和追溯。
*   **代理商数据统计：**
    *   统计代理商的推广数据，如签约商家数量、活跃商家数量、推广效果等。
    *   统计代理商的佣金收入和结算情况。
    *   支持数据导出。

#### 2.2. 平台商铺管理

*   **商铺列表：**
    *   查看所有已入驻商铺信息，包括商铺名称、地址、联系方式、所属代理商、状态等。
    *   支持商铺的添加、编辑、删除操作。
    *   支持商铺的强制解绑代理商功能。
    *   支持商铺的搜索和筛选。
    *   支持批量导入/导出商铺信息。
*   **商铺配置：**
    *   配置商铺的基本信息，如营业时间、联系电话、商铺备注等。
    *   配置商铺的多媒体推广内容，包括视频、图片、文案等。
    *   配置商铺的NFC芯片绑定信息。
    *   配置商铺的推广权限，例如是否允许发布到特定平台。
*   **商铺数据统计：**
    *   统计商铺的推广数据，如视频播放量、点评打卡量、团购参与量、关注量等。
    *   统计商铺的活跃度。
    *   支持数据导出。

#### 2.3. 多媒体配置管理

*   **平台多媒体维度配置：**
    *   配置H5页面中展示的媒体平台类型（如抖音、小红书、视频号、快手、大众点评、美团点评、高德点评、微信等）。
    *   配置每个媒体平台对应的图标和名称。
    *   配置每个媒体平台所需携带的素材类型（视频、图片、文案）。
    *   配置每个媒体平台特有的字段，例如抖音的挑战赛ID、小红书的笔记标签等。
*   **多媒体内容模板管理：**
    *   创建和管理不同媒体平台的视频、图片、文案模板。
    *   支持模板的预览、编辑、删除。
    *   支持模板的分类和标签管理。

#### 2.4. 素材库管理

*   **素材列表：**
    *   管理平台所有可用的多媒体素材，包括图片、视频、AI提示词等。
    *   支持素材的上传、预览、下载。
    *   支持素材的分类、标签管理。
    *   支持根据用途维度配置素材，例如哪些素材适用于"发视频"，哪些适用于"点评打卡"。
*   **素材审核：**
    *   对用户或商家上传的素材进行审核，确保内容合规。
    *   支持审核通过、驳回、删除等操作。

#### 2.5. 业务员管理

*   **业务员列表：**
    *   查看所有业务员信息，包括业务员名称、联系方式、所属代理商/平台、注册时间、状态等。
    *   支持业务员的添加、编辑操作。
    *   支持业务员账号的启用/禁用。
    *   支持业务员的搜索和筛选。
    *   **注意：** 业务员的删除功能需谨慎考虑，建议采用禁用或离职标记。
*   **业务员配置：**
    *   配置业务员的推广权限和可管理的商家数量。
    *   配置业务员的提成比例和奖励机制（包括扩展激励配置）。
*   **业务员数据统计：**
    *   统计业务员的推广数据，如签约商家数量、推广效果等。
    *   统计业务员的提成收入。
    *   支持数据导出。

#### 2.6. 招募管理

*   **招募申请管理：**
    *   管理业务员的招募申请，包括用户自助申请和平台录入。
    *   支持申请的查看、审核（通过/驳回）。
    *   记录审核历史和操作日志。
*   **招募渠道管理：**
    *   配置和管理不同的招募渠道，如线上推广、线下活动等。
    *   统计各渠道的招募效果。

#### 2.7. 团队与团队长管理

*   **团队列表：**
    *   查看所有团队信息，包括团队名称、团队长、团队成员数量等。
    *   支持团队的创建、编辑、解散。
    *   支持团队归属的设置。
*   **团队长配置：**
    *   设置团队长，并配置团队长的奖励机制和管理权限。
    *   支持团队长的变更和历史记录。
*   **团队结构管理：**
    *   清晰展示团队层级结构，支持团队成员的调动和管理。

#### 2.8. 数据统计中心

*   **平台运营数据统计：**
    *   统计平台总签约商家数、活跃商家数、新增商家数。
    *   统计平台总应收/分佣数据、总提成金额。
    *   统计各媒体平台推广数据（视频播放量、点评打卡量、团购参与量、关注量）。
    *   统计业务员数量（按类型划分，如代理商业务员、平台自营业务员）。
    *   支持自定义时间范围筛选和数据导出（支持多种格式，如Excel、CSV）。
*   **代理商运营数据统计：**
    *   统计各代理商的签约商家数、活跃商家数、新增商家数。
    *   统计各代理商的应收/分佣数据、提成金额。
    *   支持自定义时间范围筛选和数据导出。
*   **报表管理：**
    *   生成各类运营报表，如日/周/月报表。
    *   支持报表的自定义配置和定时生成。

### 3. 权限管理

*   **角色管理：**
    *   创建和管理不同的角色，如超级管理员、代理商管理员、运营人员等。
    *   配置每个角色可访问的模块和功能权限。
*   **用户管理：**
    *   管理管理平台的用户账号，包括添加、编辑、删除。
    *   为用户分配角色。
*   **操作日志：**
    *   记录所有管理平台用户的操作行为，包括操作时间、操作人、操作内容等，便于审计和追溯。

### 4. 系统设置

*   **基础配置：**
    *   系统名称、Logo、备案信息等。
    *   短信、邮件等通知配置。
*   **支付配置：**
    *   集成第三方支付接口（如微信支付、支付宝）。
*   **系统日志：**
    *   查看系统运行日志和错误日志。

### 5. 平台模块目录结构示意

```
├── Dashboard              # 控制台首页
│
├── 代理商管理
│   ├── 代理商列表
│   ├── 新增/编辑代理商
│   ├── 区域配置
│   ├── 层级管理
│   │   ├── 层级结构展示
│   │   ├── 下级代理商管理
│   │   ├── 层级详情管理
│   │   └── 数量限制管理
│   └── 结算与分佣管理
│
├── 商铺管理（平台）
│   ├── 商铺列表（全量）
│   ├── 批量导入/导出
│   ├── 物料管理
│   └── 强制解绑操作
│
├── 多媒体配置管理
│   ├── 维度管理
│   ├── 平台项配置（含WiFi）
│   └── 启用状态控制
│
├── 素材库管理
│   ├── 视频素材（单条限制）
│   ├── 图片素材
│   └── AI提示词库
│
├── 业务员管理
│   ├── 业务员列表
│   ├── 激励规则配置
│   └── 身份类型管理
│
├── 招募管理
│   ├── 申请审核
│   ├── 归属分配
│   └── 来源统计
│
├── 团队与团队长管理
│   ├── 团队架构
│   ├── 团队长任命
│   └── 团队奖励设置
│
└── 数据统计中心
    ├── 财务数据报表
    ├── 推广效果分析
    └── 实时数据看板
    
## 6. 数据库架构设计

### 6.1 核心业务表结构

基于当前数据库设计，系统包含以下核心业务表：

#### 6.1.1 管理员系统表
- **admin_users** - 管理员用户表
- **admin_roles** - 管理员角色表  
- **admin_permissions** - 管理员权限表
- **admin_menu** - 管理员菜单表
- **admin_operation_log** - 管理员操作日志表
- **admin_role_users** - 管理员角色用户关联表
- **admin_role_permissions** - 管理员角色权限关联表
- **admin_role_menu** - 管理员角色菜单关联表
- **admin_user_permissions** - 管理员用户权限关联表

#### 6.1.2 代理商管理表
- **agents** - 代理商基础信息表
- **agent_regions** - 代理商区域业务配置表（包含合约、返佣、结算等完整配置）

#### 6.1.3 商铺管理表
- **stores** - 商铺基础信息表
- **store_materials** - 商铺素材关联表

#### 6.1.4 业务员管理表
- **salespeople** - 业务员信息表
- **teams** - 团队管理表
- **recruitment_applications** - 招募申请表
- **recruitment_channels** - 招募渠道表

#### 6.1.5 素材管理表
- **materials** - 素材库表
- **material_templates** - 素材模板表
- **media_platforms** - 媒体平台配置表

#### 6.1.6 商家用户表
- **merchant_users** - 商家用户表
- **merchant_user_stores** - 商家用户商铺关联表

#### 6.1.7 佣金结算表
- **commission_settlements** - 佣金结算记录表

#### 6.1.8 统计分析表
- **promotion_statistics** - 推广统计表
- **promotion_data_stats** - 推广数据统计表
- **user_behavior_logs** - 用户行为日志表
- **financial_reports** - 财务报告表

#### 6.1.9 系统配置表
- **system_configs** - 系统配置表
- **system_notifications** - 系统通知表
- **operation_logs** - 操作日志表

#### 6.1.10 基础数据表
- **areas** - 区域数据表（省市区三级联动）
- **users** - 用户表
- **migrations** - 迁移记录表
- **failed_jobs** - 失败任务表
- **password_reset_tokens** - 密码重置令牌表
- **personal_access_tokens** - 个人访问令牌表

### 6.2 关键表设计说明

#### 6.2.1 agent_regions表 - 代理商区域配置核心表
该表是系统的核心业务表，包含代理商的完整业务配置：

**区域管理字段：**
- 省市区三级联动（province_id, city_id, district_id）
- 区域名称和代码存储
- 独占权限控制（is_exclusive）

**合约管理字段：**
- 合约编号、标题、文件URL
- 签约日期、合约期限
- 合约状态管理（draft, signed, expired, terminated）

**返佣配置字段：**
- 返佣类型（percentage, fixed）
- 返佣比例和金额
- 阶梯式返佣规则
- 返佣生效和到期日期

**结算配置字段：**
- 结算周期（weekly, monthly, quarterly）
- 结算间隔天数
- 银行账户信息
- 结算状态跟踪

#### 6.2.2 commission_settlements表 - 佣金结算表
**结算对象管理：**
- target_type：支持代理商、业务员、团队长三种结算对象
- target_id：对应的对象ID

**结算金额计算：**
- total_amount：总金额
- commission_amount：佣金金额  
- deduction_amount：扣除金额
- actual_amount：实际结算金额

**结算流程管理：**
- status：结算状态（pending, approved, paid, rejected）
- 审核和支付时间跟踪
- 支付方式和凭证记录

#### 6.2.3 stores表 - 商铺信息表
**基础信息：**
- 商铺名称、logo、描述
- 联系人和联系方式
- 详细地址和地理坐标

**NFC管理：**
- nfc_chip_id：NFC芯片唯一标识
- nfc_chip_status：芯片状态管理

**结算关联：**
- product_amount：商品金额
- settlement_status：结算状态
- settlement_id：关联结算单ID

### 6.3 数据库设计特点

#### 6.3.1 完整的业务流程支持
- 从代理商签约到商铺入驻的完整流程
- 从推广统计到佣金结算的闭环管理
- 多层级的权限和角色管理

#### 6.3.2 灵活的配置能力
- 支持多种返佣模式和结算周期
- 可配置的媒体平台和素材管理
- 灵活的团队层级结构

#### 6.3.3 完善的审计跟踪
- 详细的操作日志记录
- 状态变更历史跟踪
- 用户行为数据收集

#### 6.3.4 高性能设计
- 合理的索引设计
- 适当的数据冗余以提升查询性能
- JSON字段存储复杂配置信息

### 6.4 模型关联关系

#### 6.4.1 核心关联
- Agent → AgentRegion（一对多）
- AgentRegion → Store（一对多）
- Agent → Salesperson（一对多）
- Team → Salesperson（一对多）
- Store → StoreMaterial（一对多）
- Material → StoreMaterial（一对多）

#### 6.4.2 结算关联
- AgentRegion → CommissionSettlement（一对多）
- Salesperson → CommissionSettlement（一对多）
- Team → CommissionSettlement（一对多）

#### 6.4.3 统计关联
- Store → PromotionStatistics（一对多）
- Store → UserBehaviorLog（一对多）

这种数据库设计确保了系统的可扩展性、数据完整性和业务逻辑的完整实现。

---

## 7. 权限管理系统需求

### 7.1 权限系统架构

#### 7.1.1 权限模型设计
基于Laravel-Admin的RBAC（Role-Based Access Control）模型：

**核心数据表：**
- `admin_permissions` - 权限定义表
- `admin_roles` - 角色定义表  
- `admin_role_permissions` - 角色权限关联表
- `admin_role_users` - 用户角色关联表
- `admin_menu` - 菜单权限配置表

**权限命名规范：**
- 格式：`模块.动作` (例如：`agent.create`, `store.show`, `material.delete`)
- 模块：`agent`, `store`, `material`, `salesperson`, `team`, `recruitment`, `statistics`, `system`
- 动作：`list`, `create`, `edit`, `show`, `delete`, `manage`, `config`, `export`, `import`

#### 7.1.2 角色层级结构

**超级管理员 (super-admin)**
- 系统最高权限
- 可访问所有模块和功能
- 系统配置和用户管理权限

**平台管理员 (platform-admin)**
- 业务管理权限
- 所有业务模块的完整CRUD权限
- 数据统计和报表权限
- 系统配置权限（除用户管理外）

**一级代理商 (primary-agent)**
- 有限的管理权限
- 可管理下级二级代理商
- 可管理直属商铺和业务员
- 查看自己和下级的数据统计

**二级代理商 (secondary-agent)**
- 只读权限为主
- 只能查看自己的数据
- 可管理自己的直属商铺
- 有限的业务员管理权限

**运营人员 (operation-staff)**
- 特定模块权限
- 素材管理和审核权限
- 招募管理权限
- 数据查看权限

### 7.2 角色权限配置矩阵

#### 7.2.1 代理商管理模块权限
| 功能       | 超级管理员 | 平台管理员 | 一级代理商 | 二级代理商 | 运营人员 |
| ---------- | ---------- | ---------- | ---------- | ---------- | -------- |
| 代理商列表 | ✅          | ✅          | ✅(下级)    | ✅(自己)    | ❌        |
| 创建代理商 | ✅          | ✅          | ✅(二级)    | ❌          | ❌        |
| 编辑代理商 | ✅          | ✅          | ✅(下级)    | ✅(自己)    | ❌        |
| 查看代理商 | ✅          | ✅          | ✅(下级)    | ✅(自己)    | ❌        |
| 删除代理商 | ✅          | ✅          | ❌          | ❌          | ❌        |
| 区域配置   | ✅          | ✅          | ✅(下级)    | ✅(自己)    | ❌        |
| 层级管理   | ✅          | ✅          | ✅(下级)    | ❌          | ❌        |

#### 7.2.2 商铺管理模块权限
| 功能     | 超级管理员 | 平台管理员 | 一级代理商 | 二级代理商 | 运营人员 |
| -------- | ---------- | ---------- | ---------- | ---------- | -------- |
| 商铺列表 | ✅          | ✅          | ✅(下级)    | ✅(自己)    | ✅(只读)  |
| 创建商铺 | ✅          | ✅          | ✅          | ✅          | ❌        |
| 编辑商铺 | ✅          | ✅          | ✅(管辖)    | ✅(自己)    | ❌        |
| 查看商铺 | ✅          | ✅          | ✅(管辖)    | ✅(自己)    | ✅        |
| 删除商铺 | ✅          | ✅          | ❌          | ❌          | ❌        |
| 强制解绑 | ✅          | ✅          | ❌          | ❌          | ❌        |
| 商铺配置 | ✅          | ✅          | ✅(管辖)    | ✅(自己)    | ❌        |

#### 7.2.3 素材管理模块权限
| 功能     | 超级管理员 | 平台管理员 | 一级代理商 | 二级代理商 | 运营人员 |
| -------- | ---------- | ---------- | ---------- | ---------- | -------- |
| 素材列表 | ✅          | ✅          | ✅(只读)    | ✅(只读)    | ✅        |
| 上传素材 | ✅          | ✅          | ❌          | ❌          | ✅        |
| 编辑素材 | ✅          | ✅          | ❌          | ❌          | ✅        |
| 删除素材 | ✅          | ✅          | ❌          | ❌          | ✅        |
| 素材审核 | ✅          | ✅          | ❌          | ❌          | ✅        |

#### 7.2.4 业务员管理模块权限
| 功能       | 超级管理员 | 平台管理员 | 一级代理商 | 二级代理商 | 运营人员 |
| ---------- | ---------- | ---------- | ---------- | ---------- | -------- |
| 业务员列表 | ✅          | ✅          | ✅(下级)    | ✅(自己)    | ✅(只读)  |
| 创建业务员 | ✅          | ✅          | ✅          | ✅          | ❌        |
| 编辑业务员 | ✅          | ✅          | ✅(管辖)    | ✅(自己)    | ❌        |
| 查看业务员 | ✅          | ✅          | ✅(管辖)    | ✅(自己)    | ✅        |
| 业务员配置 | ✅          | ✅          | ✅(管辖)    | ✅(自己)    | ❌        |

#### 7.2.5 数据统计模块权限
| 功能           | 超级管理员 | 平台管理员 | 一级代理商   | 二级代理商 | 运营人员 |
| -------------- | ---------- | ---------- | ------------ | ---------- | -------- |
| 平台数据统计   | ✅          | ✅          | ❌            | ❌          | ✅(只读)  |
| 代理商数据统计 | ✅          | ✅          | ✅(自己+下级) | ✅(自己)    | ✅(只读)  |
| 财务报表       | ✅          | ✅          | ✅(自己)      | ✅(自己)    | ❌        |
| 数据导出       | ✅          | ✅          | ✅(有限)      | ❌          | ✅        |

### 7.3 权限实施标准流程

#### 7.3.1 权限创建流程
```sql
-- 第一步：创建权限
INSERT INTO admin_permissions (name, slug, http_method, http_path) VALUES
('代理商列表', 'agent.list', '["GET"]', '["/agents"]'),
('代理商创建', 'agent.create', '["GET","POST"]', '["/agents/create","/agents"]'),
('代理商编辑', 'agent.edit', '["GET","PUT"]', '["/agents/*/edit","/agents/*"]'),
('代理商查看', 'agent.show', '["GET"]', '["/agents/*"]'),
('代理商删除', 'agent.delete', '["DELETE"]', '["/agents/*"]'),
('代理商管理', 'agent.manage', '["GET","POST","PUT"]', '["/agents/**"]');
```

#### 7.3.2 角色创建流程
```sql
-- 第二步：创建角色
INSERT INTO admin_roles (name, slug) VALUES
('超级管理员', 'super-admin'),
('平台管理员', 'platform-admin'),
('一级代理商', 'primary-agent'),
('二级代理商', 'secondary-agent'),
('运营人员', 'operation-staff');
```

#### 7.3.3 权限分配流程
```sql
-- 第三步：角色权限关联
-- 平台管理员权限配置
INSERT INTO admin_role_permissions (role_id, permission_id) 
SELECT 2, id FROM admin_permissions 
WHERE slug IN (
    'agent.list', 'agent.create', 'agent.edit', 'agent.show', 'agent.delete',
    'store.list', 'store.create', 'store.edit', 'store.show', 'store.config',
    'material.list', 'material.show',
    'salesperson.list', 'salesperson.create', 'salesperson.edit', 'salesperson.show',
    'statistics.list', 'statistics.show'
);

-- 一级代理商权限配置
INSERT INTO admin_role_permissions (role_id, permission_id) 
SELECT 3, id FROM admin_permissions 
WHERE slug IN (
    'agent.list', 'agent.show', 'agent.manage',
    'store.list', 'store.create', 'store.edit', 'store.show',
    'material.list', 'material.show',
    'salesperson.list', 'salesperson.create', 'salesperson.edit', 'salesperson.show'
);

-- 二级代理商权限配置
INSERT INTO admin_role_permissions (role_id, permission_id) 
SELECT 4, id FROM admin_permissions 
WHERE slug IN (
    'store.list', 'store.show',
    'material.list', 'material.show',
    'salesperson.list', 'salesperson.show'
);
```

#### 7.3.4 用户角色分配流程
```sql
-- 第四步：用户角色关联
INSERT INTO admin_role_users (role_id, user_id) VALUES 
(3, 3), -- agent_001 分配为一级代理商
(4, 4), -- agent_002 分配为二级代理商
(4, 5), -- agent_003 分配为二级代理商
(4, 6); -- agent_004 分配为二级代理商
```

### 7.4 控制器权限实现标准

#### 7.4.1 权限检查方法调用
```php
<?php

namespace App\Admin\Controllers;

use App\Services\PermissionService;

class AgentController extends AdminController
{
    /**
     * 代理商列表 - 需要agent.list权限
     */
    public function index()
    {
        if (!PermissionService::hasPermission(auth('admin')->user(), 'agent.list')) {
            return redirect()->route('admin.unauthorized')
                ->with('error', '您没有权限访问代理商列表');
        }
        
        // 根据用户类型过滤数据
        $user = auth('admin')->user();
        if ($user->user_type == 2) { // 一级代理商
            // 只显示自己和下级代理商
            $query = Agent::where('parent_agent_id', $user->agent_id)
                          ->orWhere('id', $user->agent_id);
        }
        
        // 业务逻辑...
    }

    /**
     * 创建代理商 - 需要agent.create权限
     */
    public function create()
    {
        if (!PermissionService::hasPermission(auth('admin')->user(), 'agent.create')) {
            abort(403, '您没有权限创建代理商');
        }
        
        // 业务逻辑...
    }

    /**
     * 编辑代理商 - 需要agent.edit权限
     */
    public function edit($id)
    {
        if (!PermissionService::hasPermission(auth('admin')->user(), 'agent.edit')) {
            abort(403, '您没有权限编辑代理商');
        }
        
        // 数据权限检查
        $user = auth('admin')->user();
        if ($user->user_type == 2) { // 一级代理商
            $agent = Agent::where('id', $id)
                          ->where(function($query) use ($user) {
                              $query->where('parent_agent_id', $user->agent_id)
                                    ->orWhere('id', $user->agent_id);
                          })->firstOrFail();
        }
        
        // 业务逻辑...
    }
}
```

#### 7.4.2 菜单权限配置
```sql
-- admin_menu表权限字段配置
UPDATE admin_menu SET permission = 'agent.list' WHERE uri = 'agents';
UPDATE admin_menu SET permission = 'agent.create' WHERE uri = 'agents/create';
UPDATE admin_menu SET permission = 'store.list' WHERE uri = 'stores';
UPDATE admin_menu SET permission = 'store.config' WHERE uri = 'stores/config';
UPDATE admin_menu SET permission = 'material.list' WHERE uri = 'materials';
UPDATE admin_menu SET permission = 'salesperson.list' WHERE uri = 'salespersons';
UPDATE admin_menu SET permission = 'statistics.list' WHERE uri = 'statistics';
```

#### 7.4.3 前端权限控制实现
```blade
{{-- Blade模板权限控制 --}}
@if(PermissionService::hasPermission(auth('admin')->user(), 'agent.create'))
    <a href="{{ route('admin.agents.create') }}" class="btn btn-primary">
        <i class="fa fa-plus"></i> 创建代理商
    </a>
@endif

@if(PermissionService::hasPermission(auth('admin')->user(), 'agent.edit'))
    <a href="{{ route('admin.agents.edit', $agent->id) }}" class="btn btn-warning">
        <i class="fa fa-edit"></i> 编辑
    </a>
@endif

@if(PermissionService::hasPermission(auth('admin')->user(), 'agent.delete'))
    <button class="btn btn-danger delete-btn" data-id="{{ $agent->id }}">
        <i class="fa fa-trash"></i> 删除
    </button>
@endif
```

```javascript
// JavaScript权限控制
$(document).ready(function() {
    // 根据权限显示/隐藏按钮
    if (!window.userPermissions.includes('agent.create')) {
        $('.create-agent-btn').hide();
    }
    
    if (!window.userPermissions.includes('agent.edit')) {
        $('.edit-agent-btn').hide();
    }
    
    if (!window.userPermissions.includes('agent.delete')) {
        $('.delete-agent-btn').hide();
    }
    
    // 数据表格权限控制
    if (window.userType === 2) { // 一级代理商
        // 只显示下级数据
        loadSubordinateData();
    } else if (window.userType === 3) { // 二级代理商
        // 只显示自己的数据
        loadOwnData();
    }
});
```

### 7.5 权限系统测试标准

#### 7.5.1 权限测试用例
```php
// 权限测试脚本示例
public function testAgentPermissions()
{
    // 测试一级代理商权限
    $primaryAgent = AdminUser::where('username', 'agent_001')->first();
    
    // 应该有权限
    $this->assertTrue(PermissionService::hasPermission($primaryAgent, 'agent.list'));
    $this->assertTrue(PermissionService::hasPermission($primaryAgent, 'agent.show'));
    $this->assertTrue(PermissionService::hasPermission($primaryAgent, 'store.create'));
    
    // 不应该有权限
    $this->assertFalse(PermissionService::hasPermission($primaryAgent, 'agent.delete'));
    $this->assertFalse(PermissionService::hasPermission($primaryAgent, 'system.config'));
    
    // 测试二级代理商权限
    $secondaryAgent = AdminUser::where('username', 'agent_002')->first();
    
    // 应该有权限
    $this->assertTrue(PermissionService::hasPermission($secondaryAgent, 'store.list'));
    $this->assertTrue(PermissionService::hasPermission($secondaryAgent, 'store.show'));
    
    // 不应该有权限
    $this->assertFalse(PermissionService::hasPermission($secondaryAgent, 'agent.create'));
    $this->assertFalse(PermissionService::hasPermission($secondaryAgent, 'store.delete'));
}
```

#### 7.5.2 数据权限测试
```php
// 数据访问权限测试
public function testDataAccessPermissions()
{
    // 测试一级代理商只能看到下级数据
    $primaryAgent = AdminUser::where('username', 'agent_001')->first();
    $accessibleAgents = AgentController::getAccessibleAgents($primaryAgent);
    
    // 应该包含自己和下级
    $this->assertContains($primaryAgent->agent_id, $accessibleAgents);
    $this->assertContains(4, $accessibleAgents); // 下级代理商
    
    // 不应该包含同级或上级
    $this->assertNotContains(2, $accessibleAgents); // 其他一级代理商
    
    // 测试二级代理商只能看到自己的数据
    $secondaryAgent = AdminUser::where('username', 'agent_002')->first();
    $accessibleStores = StoreController::getAccessibleStores($secondaryAgent);
    
    // 只应该包含自己的商铺
    foreach ($accessibleStores as $storeId) {
        $store = Store::find($storeId);
        $this->assertEquals($secondaryAgent->agent_id, $store->agent_id);
    }
}
```

### 7.6 权限系统维护规范

#### 7.6.1 权限变更流程
1. **需求分析** - 分析权限变更的业务需求
2. **影响评估** - 评估权限变更对现有用户的影响
3. **测试验证** - 在测试环境验证权限变更
4. **备份数据** - 备份权限相关数据表
5. **执行变更** - 执行权限配置变更
6. **验证结果** - 验证权限变更是否生效
7. **用户通知** - 通知相关用户权限变更

#### 7.6.2 权限审计要求
- **定期审计** - 每月审计一次用户权限配置
- **异常监控** - 监控权限异常访问行为
- **日志记录** - 记录所有权限相关操作日志
- **合规检查** - 确保权限配置符合业务合规要求

通过以上权限管理系统的设计和实施，确保系统安全性和数据访问的精确控制，满足不同角色用户的业务需求。

        