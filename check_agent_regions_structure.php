<?php

require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\\Contracts\\Console\\Kernel')->bootstrap();

try {
    echo "=== agent_regions表结构检查 ===" . PHP_EOL;

    $columns = DB::select('SHOW COLUMNS FROM agent_regions');
    foreach ($columns as $column) {
        $nullable = $column->Null === 'YES' ? '可空' : '不可空';
        $default = $column->Default ? "默认值: {$column->Default}" : '无默认值';
        echo "  {$column->Field} - {$column->Type} - {$nullable} - {$default}" . PHP_EOL;
    }

    // 检查现有数据的area_id值
    echo PHP_EOL . "现有数据的area_id值:" . PHP_EOL;
    $regions = DB::table('agent_regions')->select('id', 'agent_id', 'area_id', 'province_name', 'city_name')->get();
    foreach ($regions as $region) {
        echo "  ID: {$region->id}, 代理商ID: {$region->agent_id}, area_id: " . ($region->area_id ?? 'NULL') . ", 省份: {$region->province_name}" . PHP_EOL;
    }
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . PHP_EOL;
}
