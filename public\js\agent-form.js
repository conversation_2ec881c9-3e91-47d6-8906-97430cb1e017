$(function() {
    // 区域代码相关功能
    function setupRegionCodeHandling() {
        // 监听区域代码变化
        $('input[name="region_code"]').on('change', function() {
            var regionCode = $(this).val();
            if (regionCode) {
                loadRegionInfo(regionCode);
            }
        });
        
        // 加载区域信息函数
        function loadRegionInfo(regionCode) {
            console.log('加载区域信息:', regionCode);
            
            $.ajax({
                url: '/admin/api/area-by-code',
                type: 'GET',
                data: { code: regionCode },
                success: function(data) {
                    console.log('区域信息结果:', data);
                    
                    try {
                        $('input[name="region_name"]').val(data.full_name || data.name);
                        
                        if (data.level == 3) { // 区县级
                            $('select[name="province_id"]').val(data.province.id).trigger('change');
                            
                            setTimeout(function() {
                                $('select[name="city_id"]').val(data.city.id).trigger('change');
                                
                                setTimeout(function() {
                                    $('select[name="district_id"]').val(data.id).trigger('change');
                                }, 1000);
                            }, 1000);
                        } else if (data.level == 2) { // 市级
                            $('select[name="province_id"]').val(data.province.id).trigger('change');
                            
                            setTimeout(function() {
                                $('select[name="city_id"]').val(data.id).trigger('change');
                            }, 1000);
                        } else if (data.level == 1) { // 省级
                            $('select[name="province_id"]').val(data.id).trigger('change');
                        }
                    } catch (error) {
                        console.error('处理区域信息错误:', error);
                    }
                },
                error: function(xhr) {
                    console.error('区域信息查询失败:', xhr);
                }
            });
        }
        
        // 如果已有区域代码，自动加载
        var existingRegionCode = $('input[name="region_code"]').val();
        if (existingRegionCode) {
            loadRegionInfo(existingRegionCode);
        }
    }

    // 结算方式相关功能
    function setupSettlementHandling() {
        // 计算下次结算日期
        function calculateNextSettlement() {
            var signedAt = $('input[name="signed_at"]').val();
            var settlementCycle = $('select[name="settlement_cycle"]').val();
            var settlementDays = $('input[name="settlement_days"]').val();
            var settlementMethod = $('input[name="settlement_method"]:checked').val();
            
            $('input[name="next_settlement_at"]').val('');
            
            if (settlementMethod == 'cycle' && signedAt && settlementCycle) {
                var nextDate = new Date(signedAt);
                
                switch (settlementCycle) {
                    case 'weekly': nextDate.setDate(nextDate.getDate() + 7); break;
                    case 'monthly': nextDate.setMonth(nextDate.getMonth() + 1); break;
                    case 'quarterly': nextDate.setMonth(nextDate.getMonth() + 3); break;
                }
                
                var yyyy = nextDate.getFullYear();
                var mm = String(nextDate.getMonth() + 1).padStart(2, '0');
                var dd = String(nextDate.getDate()).padStart(2, '0');
                
                $('input[name="next_settlement_at"]').val(yyyy + '-' + mm + '-' + dd);
                
            } else if (settlementMethod == 'days' && settlementDays > 0) {
                var currentDate = new Date();
                var nextDate = new Date(currentDate);
                nextDate.setDate(currentDate.getDate() + parseInt(settlementDays));
                
                var yyyy = nextDate.getFullYear();
                var mm = String(nextDate.getMonth() + 1).padStart(2, '0');
                var dd = String(nextDate.getDate()).padStart(2, '0');
                
                $('input[name="next_settlement_at"]').val(yyyy + '-' + mm + '-' + dd);
            }
            
            $('input[name="next_settlement_at"]').trigger('change');
        }

        // 监听结算方式变化
        $('input[name="settlement_method"]').on('change', function() {
            var method = $('input[name="settlement_method"]:checked').val();
            
            if (method == 'cycle') {
                $('input[name="settlement_days"]').val('');
            } else if (method == 'days') {
                $('select[name="settlement_cycle"]').val('').trigger('change');
            }
            
            calculateNextSettlement();
        });
        
        // 监听其他字段变化
        $('input[name="signed_at"], input[name="settlement_days"]').on('input change keyup', function() {
            calculateNextSettlement();
        });
        
        $('select[name="settlement_cycle"]').on('change select2:select', function() {
            calculateNextSettlement();
        });
        
        // 使用passive事件监听器以提高性能
        $(document).on('dp.change dp.update', 'input[name="signed_at"]', function() {
            calculateNextSettlement();
        });
        
        // 将点击事件标记为passive
        $(document).on('click', '.day', {passive: true}, function() {
            setTimeout(calculateNextSettlement, 100);
        });
        
        // 初始化设置
        var hasCycle = $('select[name="settlement_cycle"]').val();
        var hasDays = $('input[name="settlement_days"]').val();
        
        if (hasDays) {
            $('input[name="settlement_method"][value="days"]').prop('checked', true).trigger('change');
        } else if (hasCycle) {
            $('input[name="settlement_method"][value="cycle"]').prop('checked', true).trigger('change');
        }
        
        // 初始计算
        setTimeout(calculateNextSettlement, 500);
    }

    // 表单提交处理
    function setupFormSubmission() {
        $('form').on('submit', function(e) {
            try {
                // 设置结算字段
                var method = $('input[name="settlement_method"]:checked').val();
                if (method == 'cycle') {
                    $('input[name="settlement_days"]').val('');
                } else if (method == 'days') {
                    $('select[name="settlement_cycle"]').val('');
                }
                
                // 设置区域代码
                var provinceId = $('select[name="province_id"]').val();
                var cityId = $('select[name="city_id"]').val();
                var districtId = $('select[name="district_id"]').val();
                
                if (districtId) {
                    $('input[name="region_code"]').val(districtId);
                } else if (cityId) {
                    $('input[name="region_code"]').val(cityId);
                } else if (provinceId) {
                    $('input[name="region_code"]').val(provinceId);
                }
                
                return true;
            } catch (error) {
                console.error('表单提交错误:', error);
                alert('提交错误: ' + error.message);
                e.preventDefault();
                return false;
            }
        });
    }

    // 初始化所有功能
    setupRegionCodeHandling();
    setupSettlementHandling();
    setupFormSubmission();
}); 