# 📘 碰一碰商家端前端项目文档（功能需求版）

本文档梳理了「碰一碰商家端」的核心功能模块与业务需求，适用于前端开发团队进行模块开发、页面搭建及前期模拟联调。

> 当前阶段所有数据均使用**前端模拟数据**，模拟结构需符合后期接口标准。

---

## 一、项目目标

开发一款商家移动端应用，用于配合用户端展示内容，支持素材配置、功能管理、多门店运营、报表分析等能力，形成闭环生态。

---

## 二、功能需求详解

### 1. 账号与权限系统

* 商家账号由管理端分配；
* 登录方式：**账号 + 密码 + 验证码（验证码图片）**；
* 可绑定多个门店；
* 门店需由管理端审核后方可生效；
* 登录成功后进入商家首页。

---

## 三、页面功能模块说明

### ✅ 登录页

* 输入框：账号、密码、验证码（文本验证码自动生成）；
* 登录按钮、记住密码功能；
* 登录失败提示和错误处理；
* 测试账号自动填充（开发调试用）；
* 登录状态管理和按钮防重复点击；
* 验证码点击刷新功能；
* 客户端验证码校验逻辑。

---

### ✅ 商家首页（概览）

* 欢迎信息；
* 当前门店绑定与审核状态；
* 快捷入口跳转（如素材管理、统计分析）；
* 公告展示区（如有）。

---

### ✅ 门店管理页

* 门店列表（门店名、状态）；
* 添加门店提示（仅管理端可添加）；
* 显示审核状态：待审核 / 通过 / 驳回；
* 可搜索、排序（模拟数据支持）；
* 每个门店项可进入详情页（预留入口）。

---

### ✅ 功能配置中心

包含以下子模块配置入口：

#### 1. 商家基本信息配置

* 展示 Logo、商家名称、Banner 图；
* 开关控制各功能模块是否开启；
* 支持模块：

  * 发视频（抖音/快手/小红书/视频号）；
  * 点评打卡（大众点评、小红书等）；
  * 微信营销（加微信、发朋友圈）；
  * 团购配置（美团、大众点评等）；
  * 关注账号（抖音、小红书、快手等）；
  * WiFi 配置（名称、密码）。

#### 2. 发视频配置

* 上传视频文件；
* 文案编辑；
* 发布平台选择。

#### 3. 点评打卡配置

* 输入打卡平台链接；
* 编辑图文模板；
* 模拟链接预览。

#### 4. 微信营销配置

* 上传微信二维码图；
* 设置朋友圈引导文案。

#### 5. 商家团购配置

* 设置团购平台入口链接；
* 团购引导文案。

#### 6. 关注账号配置

* 选择平台；
* 输入账号信息、上传引导图标；
* 配置平台引导语。

#### 7. WiFi 配置

* WiFi 名称、密码设置；
* 门店 WiFi 启用/停用开关。

---

### ✅ 素材库管理系统

#### 分类结构：

* 视频素材
* 图片素材
* 文案提示词

#### 功能特性：

* 上传素材（图片/视频/文案）；
* 素材预览与批量删除；
* 编辑素材标题与描述；
* 设置“适用模块”标签（支持多选/全选）；
* 搜索与筛选（按分类、关键词）。

---

### ✅ 报表统计中心

#### 统计维度：

* 抖音、小红书、视频号、快手的**发布次数**。

#### 时间筛选支持：

* 今日 vs 昨日；
* 本周 vs 上周；
* 本月 vs 上月；
* 自定义时间段查询。

#### 可视化展示：

* **曲线图（折线图）**：

  * 横轴：时间；
  * 纵轴：发布次数；
  * 多平台并列展示；
  * 提示悬停查看具体值；
* 环比增长/减少以百分比文字展示（如 ↑12%、↓7%）；
* 当前使用模拟数据，格式与后期接口保持一致。

---

### ✅ 个人中心页

* 展示当前账号信息；
* 所属门店及状态；
* 素材使用统计（上传数、使用数等）；
* 退出登录；
* 预留入口：帮助文档 / 联系客服 / 关于页面等。

---

## 四、开发阶段建议

### ✅ 第一阶段：架构与页面搭建

* 完成基础页面结构；
* 建立页面路由配置；
* 搭建公共组件库（按钮、上传器、弹窗等）；
* 整理并加载模拟数据结构。

### ✅ 第二阶段：基础功能实现

* 登录模块（验证码图片模拟）；
* 功能配置中心页面；
* 素材上传、预览、适用模块标记。

### ✅ 第三阶段：统计与图表展示

* 报表模块页面；
* 曲线图渲染（使用模拟数据）；
* 时间维度切换与数据变化处理逻辑。

### ✅ 第四阶段：联调准备与优化

* 全功能走查测试；
* 表单验证逻辑；
* 平台兼容性处理；
* 模拟接口结构统一封装。

---

## 五、开发注意事项

* 所有数据在联调前均使用本地模拟数据；
* 建议模拟数据统一保存在 `mock/` 或 `utils/fake-data.ts`；
* 各模块需封装好接口请求方法，便于后期对接；
* 表单、图表、上传等交互需提前做好容错与兼容性处理；
* 建议每个模块使用统一命名规范与样式风格。

---

## 六、项目运行说明

* 在 HBuilderX 中选择运行方式：

  * 浏览器运行；
  * 手机模拟器运行；
  * 小程序平台模拟器运行（如微信开发者工具）。

---

---

## 七、已完成功能模块详情

### ✅ 已实现的配置页面

#### 1. 商家基本信息配置 (`pages/config/basic/basic.vue`)
* 商家Logo上传与预览
* 商家名称、简介编辑
* Banner图片设置
* 各功能模块开关控制
* 联系方式配置
* 营业时间设置

#### 2. 发视频配置 (`pages/config/video/video.vue`)
* 视频文件上传（支持多种格式）
* 视频标题和描述编辑
* 发布平台选择（抖音、快手、小红书、视频号）
* 定时发布功能
* 视频预览效果
* 发布状态管理

#### 3. 点评打卡配置 (`pages/config/review/review.vue`)
* 打卡平台选择（大众点评、小红书等）
* 平台链接配置与验证
* 引导图片上传
* 引导文案编辑
* 奖励描述设置
* 链接预览功能

#### 4. 微信营销配置 (`pages/config/wechat/wechat.vue`)
* 微信二维码上传
* 添加微信引导文案
* 朋友圈分享内容设置
* 朋友圈配图上传
* 自动回复内容配置
* 营销效果预览

#### 5. 团购配置 (`pages/config/group/group.vue`)
* 团购平台选择（美团、大众点评、饿了么、拼多多）
* 团购商品信息配置（名称、原价、团购价）
* 商品图片上传
* 活动时间设置
* 引导文案编辑
* 团购效果预览（包含折扣计算）

#### 6. 抽奖配置 (`pages/config/lottery/lottery.vue`)
* 抽奖活动基本信息设置
* 活动海报上传
* 多奖品配置（支持动态添加/删除）
* 奖品图片、名称、数量、中奖概率设置
* 参与条件配置（关注、分享、评论、限制次数）
* 活动时间管理
* 完整的抽奖预览界面

#### 7. WiFi配置 (`pages/config/wifi/wifi.vue`)
* WiFi网络名称和密码设置
* 自动连接开关控制
* 门店WiFi启用/停用管理
* WiFi信息预览和二维码生成
* 多门店WiFi配置支持
* 安全设置和访客网络配置

---

## 八、技术架构与实现

### 8.1 项目技术栈
* **框架**: uni-app（支持多端发布）
* **UI组件**: uni-ui组件库
* **开发工具**: HBuilderX
* **样式**: SCSS + uni.scss
* **数据管理**: 本地模拟数据 + API封装

### 8.2 项目文件结构
```
地瓜贴/
├── pages/                    # 页面文件
│   ├── login/               # 登录页面
│   ├── home/                # 首页
│   ├── store/               # 门店管理
│   ├── config/              # 功能配置中心
│   │   ├── config.vue       # 配置中心主页
│   │   ├── basic/           # 基本信息配置
│   │   ├── group/           # 团购配置
│   │   ├── lottery/         # 抽奖配置
│   │   ├── review/          # 点评打卡配置
│   │   └── wechat/          # 微信营销配置
│   ├── material/            # 素材库管理
│   └── profile/             # 个人中心
├── utils/                   # 工具类
│   ├── api.js              # API接口封装
│   └── mock-data.js        # 模拟数据
├── static/                  # 静态资源
├── App.vue                  # 应用入口
├── main.js                  # 主入口文件
├── pages.json              # 页面路由配置
└── manifest.json           # 应用配置
```

### 8.3 核心功能实现

#### 8.3.1 表单验证系统
* 使用uni-forms组件进行统一表单验证
* 支持必填项、格式验证、自定义验证规则
* 实时验证反馈和错误提示

#### 8.3.2 文件上传功能
* 使用uni-file-picker组件
* 支持图片、视频多种格式
* 文件大小和格式限制
* 上传进度显示和错误处理

#### 8.3.3 预览功能
* 各配置页面都提供实时预览
* 模拟真实用户端展示效果
* 响应式预览适配

#### 8.3.4 数据持久化
* 使用uni.setStorage进行本地数据存储
* 配置数据自动保存和恢复
* 支持数据导入导出（预留）

#### 8.3.5 登录与状态管理
* 完善的登录状态管理机制
* 登录按钮防重复点击保护
* 客户端验证码生成和校验
* 测试账号自动填充（开发环境）
* 登录错误处理和用户反馈
* 记住密码功能实现

---

## 九、API接口设计

### 9.1 接口规范
* **请求方式**: RESTful API
* **数据格式**: JSON
* **响应格式**: 统一响应结构
```javascript
{
  code: 200,        // 状态码
  message: 'success', // 消息
  data: {}          // 数据
}
```

### 9.2 已封装的API模块
* `loginApi` - 登录相关接口
* `storeApi` - 门店管理接口
* `configApi` - 功能配置接口
* `materialApi` - 素材管理接口
* `reportApi` - 报表统计接口
* `userApi` - 用户信息接口

### 9.3 模拟数据结构
* 完整的业务数据模拟
* 符合后端接口标准
* 支持各种业务场景测试

---

## 十、开发规范与约定

### 10.1 代码规范
* 使用ES6+语法
* 组件命名采用PascalCase
* 方法命名采用camelCase
* 常量命名采用UPPER_SNAKE_CASE
* 添加完整的JSDoc注释

### 10.2 样式规范
* 使用SCSS预处理器
* 采用BEM命名规范
* 响应式设计适配多端
* 统一的颜色和字体规范

### 10.3 组件规范
* 单一职责原则
* 可复用性设计
* Props类型检查
* 事件命名规范

---

## 十一、测试与部署

### 11.1 测试环境
* **浏览器测试**: Chrome、Safari、Firefox
* **移动端测试**: iOS Safari、Android Chrome
* **小程序测试**: 微信开发者工具

### 11.2 部署方式
* **H5版本**: 可部署到Web服务器
* **小程序版本**: 发布到微信小程序平台
* **App版本**: 打包为原生应用

### 11.3 性能优化
* 图片懒加载和压缩
* 代码分包和按需加载
* 缓存策略优化
* 网络请求优化

---

## 十二、后续开发计划

### 12.1 待完成功能
* 关注账号配置页面
* 报表统计页面（图表展示）
* 个人中心页面
* 素材库管理系统完善
* 门店管理详情页面

### 12.2 功能增强
* 批量操作功能
* 数据导入导出
* 多语言支持
* 主题切换

### 12.3 性能优化
* 首屏加载优化
* 内存使用优化
* 网络请求缓存
* 离线功能支持

---

## 十三、最新开发进展

### 13.1 近期完成的功能
* **登录系统优化**：完善了登录状态管理、错误处理和防重复点击机制
* **WiFi配置页面**：新增完整的WiFi配置功能，支持多门店管理
* **验证码系统改进**：从图片验证码改为文本验证码，提升用户体验
* **测试环境优化**：添加测试账号自动填充功能，提高开发效率
* **错误处理完善**：统一的错误提示和状态重置机制

### 13.2 技术债务处理
* 修复了登录按钮状态管理问题
* 优化了API接口的错误处理逻辑
* 完善了表单验证和用户反馈机制
* 统一了代码注释和文档规范

---

## 十四、项目总结

当前项目已完成核心功能模块的开发，包括完整的功能配置中心、素材库管理系统、WiFi配置等。项目采用uni-app框架，具有良好的跨平台兼容性。所有功能都基于模拟数据开发，接口结构清晰，便于后期与后端对接。

**项目亮点**：
* 完整的业务功能覆盖
* 优秀的用户体验设计
* 规范的代码结构和注释
* 完善的表单验证和错误处理
* 实时预览功能提升配置体验
* 响应式设计适配多种设备
* 健壮的登录和状态管理系统
* 完善的开发调试支持

**开发质量保证**：
* 统一的错误处理机制
* 完善的用户反馈系统
* 防重复操作保护
* 开发环境友好的调试功能

推荐开发团队继续按照既定计划推进剩余功能开发，并为后期接口联调做好准备。当前代码质量良好，架构清晰，为后续开发奠定了坚实基础。


