<?php

namespace App\Admin\Controllers;

use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;
use App\Models\MediaConfig;

class MediaConfigController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = '多媒体配置管理';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new MediaConfig());

        $grid->column('id', __('ID'));
        $grid->column('platform_name', __('平台名称'));
        $grid->column('platform_type', __('平台类型'))->using([
            'video' => '发视频',
            'review' => '点评打卡',
            'wechat' => '微信营销',
            'group_buy' => '团购',
            'follow' => '关注账号',
            'wifi' => 'WiFi配置'
        ]);
        $grid->column('icon', __('图标'))->image('', 50, 50);
        $grid->column('required_materials', __('所需素材类型'));
        $grid->column('is_enabled', __('启用状态'))->switch();
        $grid->column('sort_order', __('排序'));
        $grid->column('created_at', __('创建时间'));
        $grid->column('updated_at', __('更新时间'));

        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(MediaConfig::findOrFail($id));

        $show->field('id', __('ID'));
        $show->field('platform_name', __('平台名称'));
        $show->field('platform_type', __('平台类型'));
        $show->field('icon', __('图标'))->image();
        $show->field('required_materials', __('所需素材类型'));
        $show->field('special_fields', __('特有字段配置'));
        $show->field('template_config', __('模板配置'));
        $show->field('is_enabled', __('启用状态'));
        $show->field('sort_order', __('排序'));
        $show->field('description', __('描述'));
        $show->field('created_at', __('创建时间'));
        $show->field('updated_at', __('更新时间'));

        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new MediaConfig());

        $form->text('platform_name', __('平台名称'))->required();
        $form->select('platform_type', __('平台类型'))->options([
            'video' => '发视频',
            'review' => '点评打卡',
            'wechat' => '微信营销',
            'group_buy' => '团购',
            'follow' => '关注账号',
            'wifi' => 'WiFi配置'
        ])->required();
        $form->image('icon', __('平台图标'))->required();
        $form->checkbox('required_materials', __('所需素材类型'))->options([
            'video' => '视频',
            'image' => '图片',
            'text' => '文案',
            'audio' => '音频'
        ]);
        $form->textarea('special_fields', __('特有字段配置'))->placeholder('JSON格式，例如：{"challenge_id":"挑战赛ID","tags":"标签"}');
        $form->textarea('template_config', __('模板配置'))->placeholder('JSON格式的模板配置');
        $form->switch('is_enabled', __('启用状态'))->default(1);
        $form->number('sort_order', __('排序'))->default(0);
        $form->textarea('description', __('描述'));

        return $form;
    }
}