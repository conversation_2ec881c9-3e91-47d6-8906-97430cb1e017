<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class AddSignedAtToAgentsTable extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'agents:add-signed-at';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '为代理商表添加签约日期和下次结算日期字段';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('开始添加签约日期字段...');

        try {
            // 检查字段是否已存在
            $columnExists = DB::select("SHOW COLUMNS FROM `agents` LIKE 'signed_at'");
            
            if (empty($columnExists)) {
                DB::statement("ALTER TABLE `agents` ADD COLUMN `signed_at` DATE NULL COMMENT '签约日期'");
                $this->info('已添加签约日期字段');
            } else {
                $this->info('签约日期字段已存在，无需添加');
            }

            $this->info('开始添加下次结算日期字段...');
            $columnExists = DB::select("SHOW COLUMNS FROM `agents` LIKE 'next_settlement_at'");
            
            if (empty($columnExists)) {
                DB::statement("ALTER TABLE `agents` ADD COLUMN `next_settlement_at` DATE NULL COMMENT '下次结算日期'");
                $this->info('已添加下次结算日期字段');
            } else {
                $this->info('下次结算日期字段已存在，无需添加');
            }

            $this->info('字段添加完成！');
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('添加字段失败：' . $e->getMessage());
            return Command::FAILURE;
        }
    }
} 