<?php

use Illuminate\Support\Facades\Route;
use App\Admin\Controllers\DashboardController;

/*
|--------------------------------------------------------------------------
| Dashboard API Routes
|--------------------------------------------------------------------------
|
| 仪表板API路由 - 独立于Laravel Admin中间件
| 支持外部调用（如Postman等工具）
| 作者: lauJinyu
|
*/

Route::group([
    'prefix' => 'api/dashboard',
    'middleware' => ['api'] // 仅使用API中间件，不包含admin认证
], function () {
    
    /**
     * 获取仪表板图表数据
     * GET /api/dashboard/charts?type=all
     */
    Route::get('charts', [DashboardController::class, 'apiGetCharts'])
         ->name('api.dashboard.charts');
    
    /**
     * 获取仪表板统计数据  
     * GET /api/dashboard/statistics
     */
    Route::get('statistics', [DashboardController::class, 'apiGetStatistics'])
         ->name('api.dashboard.statistics');
    
    /**
     * 获取实时数据
     * GET /api/dashboard/realtime?type=revenue
     */
    Route::get('realtime', [DashboardController::class, 'getRealTimeData'])
         ->name('api.dashboard.realtime');
});

// 🔄 开发阶段专用路由 - 图表测试页面
Route::get('dashboard/chart-test', function () {
    return view('admin.charts.test');
})->name('dashboard.chart.test'); 