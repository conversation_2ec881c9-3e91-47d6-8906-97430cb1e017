<?php

namespace App\Admin\Controllers;

use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;
use App\Models\Team;
use App\Models\Salesperson;

class TeamController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = '团队与团队长管理';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new Team());

        $grid->column('id', __('ID'));
        $grid->column('name', __('团队名称'));
        $grid->column('leader.name', __('团队长'));
        $grid->column('parent_team.name', __('上级团队'));
        $grid->column('member_count', __('成员数量'))->display(function () {
            return $this->members()->count();
        });
        $grid->column('level', __('团队层级'));
        $grid->column('reward_config', __('奖励配置'))->display(function ($config) {
            $data = json_decode($config, true);
            return $data ? '已配置' : '未配置';
        });
        $grid->column('status', __('状态'))->using([
            1 => '正常',
            0 => '解散'
        ])->label([
            1 => 'success',
            0 => 'danger'
        ]);
        $grid->column('created_at', __('创建时间'));
        $grid->column('updated_at', __('更新时间'));

        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(Team::findOrFail($id));

        $show->field('id', __('ID'));
        $show->field('name', __('团队名称'));
        $show->field('leader.name', __('团队长'));
        $show->field('parent_team.name', __('上级团队'));
        $show->field('level', __('团队层级'));
        $show->field('description', __('团队描述'));
        $show->field('reward_config', __('奖励配置'));
        $show->field('management_permissions', __('管理权限'));
        $show->field('status', __('状态'));
        $show->field('created_at', __('创建时间'));
        $show->field('updated_at', __('更新时间'));

        // 显示团队成员
        $show->members('团队成员', function ($members) {
            $members->resource('/admin/salespersons');
            $members->id();
            $members->name('姓名');
            $members->phone('电话');
            $members->identity_type('身份类型');
            $members->status('状态');
        });

        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new Team());

        $form->text('name', __('团队名称'))->required();
        $form->select('leader_id', __('团队长'))->options(Salesperson::where('status', 1)->pluck('name', 'id'));
        $form->select('parent_team_id', __('上级团队'))->options(Team::where('status', 1)->pluck('name', 'id'));
        $form->number('level', __('团队层级'))->default(1)->min(1)->max(10);
        $form->textarea('description', __('团队描述'));
        
        $form->textarea('reward_config', __('奖励配置'))->placeholder('JSON格式的奖励配置，例如：
{
  "performance_bonus": {
    "monthly_target": 10,
    "bonus_rate": 0.05
  },
  "team_bonus": {
    "team_performance_rate": 0.02
  }
}');
        
        $form->checkbox('management_permissions', __('管理权限'))->options([
            'member_management' => '成员管理',
            'performance_view' => '业绩查看',
            'reward_distribution' => '奖励分配',
            'team_statistics' => '团队统计'
        ]);
        
        $form->select('status', __('状态'))->options([
            1 => '正常',
            0 => '解散'
        ])->default(1);

        return $form;
    }
}