# Laravel项目开发规范

## 📊 Laravel-Admin图表开发规范（关键规范）

### 🚨 重要警告：PJAX兼容性问题

Laravel-Admin使用PJAX技术进行页面无刷新切换，在开发图表功能时必须特别注意兼容性问题，**违反此规范将导致页面切换时出现JavaScript语法错误**。

### 📋 图表开发强制规范

#### 1. **代码分离原则**（🔥 重要）
```php
// ❌ 严重错误：在PHP中嵌入复杂JavaScript
$chartHtml = '<script>
(function() {
    var option = { 
        tooltip: { formatter: "复杂字符串" } // 会导致语法冲突
    };
    echarts.init(document.getElementById("chart")).setOption(option);
})();
</script>';

// ✅ 正确方式：数据分离，逻辑外置
$chartHtml = '<div id="revenue-chart" style="width: 100%; height: 300px;"></div>
<script>window.dashboardRevenueData = ' . json_encode($data) . ';</script>';
```

#### 2. **依赖加载顺序**（🔥 重要）
**在 `app/Admin/bootstrap.php` 中严格按顺序加载：**
```php
// 必须按此顺序，不得调整
Admin::js('/js/echarts.min.js');      // 第一个：基础图表库
Admin::js('/js/chart.min.js');        // 第二个：备用图表库  
Admin::js('/js/dashboard-charts.js'); // 第三个：自定义图表逻辑
```

#### 3. **PJAX事件兼容**（🔥 重要）
**必须在图表JavaScript文件中监听PJAX事件：**
```javascript
// 监听PJAX页面切换事件
$(document).on('pjax:complete pjax:end', function() {
    // 重新初始化图表
    if (typeof window.dashboardRevenueData !== 'undefined') {
        initCharts();
    }
});

// 实现容器等待机制
function waitForContainer(containerId, callback, maxAttempts = 10) {
    let attempts = 0;
    function check() {
        const container = document.getElementById(containerId);
        if (container) {
            callback(container);
        } else if (attempts < maxAttempts) {
            attempts++;
            setTimeout(check, 100);
        }
    }
    check();
}
```

#### 4. **图表实例管理**（🔥 重要）
```javascript
// 全局图表实例管理
window.chartInstances = window.chartInstances || {};

function createChart(containerId, option) {
    // 清理旧实例
    if (window.chartInstances[containerId]) {
        window.chartInstances[containerId].dispose();
    }
    
    // 创建新实例
    const container = document.getElementById(containerId);
    if (container) {
        window.chartInstances[containerId] = echarts.init(container);
        window.chartInstances[containerId].setOption(option);
    }
}
```

#### 5. **调试和监控**
**必须添加调试日志确保问题可追踪：**
```php
// 在控制器中添加调试脚本
Admin::script('
    console.log("🔧 图表页面开始加载");
    console.log("ECharts状态:", typeof echarts !== "undefined" ? "✅ 已加载" : "❌ 未加载");
    setTimeout(function() {
        console.log("🔧 延迟检查 - 图表实例数量:", Object.keys(window.chartInstances || {}).length);
    }, 2000);
');
```

### 🛡️ 问题预防检查清单

**每次开发图表功能时必须检查：**
- [ ] ✅ JavaScript代码是否已完全外置到独立文件
- [ ] ✅ bootstrap.php中的文件加载顺序是否正确
- [ ] ✅ 是否监听了pjax:complete和pjax:end事件
- [ ] ✅ 是否实现了容器等待机制
- [ ] ✅ 是否添加了图表实例清理逻辑
- [ ] ✅ 是否测试了页面切换场景
- [ ] ✅ 是否添加了充分的调试日志

### 🚨 常见错误警告

1. **语法错误**：`Uncaught SyntaxError: Invalid or unexpected token`
   - **原因**：在PHP字符串中嵌入了复杂JavaScript代码
   - **解决**：将JavaScript代码移到独立文件

2. **容器未找到**：`Cannot read property 'style' of null`
   - **原因**：PJAX切换时容器还未加载完成
   - **解决**：使用容器等待机制

3. **重复初始化**：图表显示异常或报错
   - **原因**：未清理旧的图表实例
   - **解决**：实现正确的实例管理

### 📁 标准文件结构
```
public/js/
├── echarts.min.js           # ECharts基础库
├── chart.min.js             # Chart.js备用库
└── dashboard-charts.js      # 自定义图表逻辑（遵循规范编写）

app/Admin/
├── bootstrap.php            # 统一管理JavaScript加载顺序
└── Controllers/
    └── DashboardController.php  # 只负责数据准备和HTML结构
```

**⚠️ 重要提醒：此规范是解决PJAX环境下图表问题的核心，必须严格遵循，不得简化或跳过任何步骤。** 