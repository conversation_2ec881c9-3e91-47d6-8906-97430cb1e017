<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

/**
 * 系统设置模型
 * 
 * @property int $id
 * @property string $key 设置键
 * @property mixed $value 设置值
 * @property string|null $description 描述
 * @property string $type 数据类型
 * @property string $group 分组
 * @property bool $is_public 是否公开
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 */
class SystemSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'key',
        'value',
        'description',
        'type',
        'group',
        'is_public',
    ];

    protected $casts = [
        'is_public' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 数据类型常量
     */
    const TYPE_STRING = 'string';
    const TYPE_INTEGER = 'integer';
    const TYPE_FLOAT = 'float';
    const TYPE_BOOLEAN = 'boolean';
    const TYPE_ARRAY = 'array';
    const TYPE_JSON = 'json';

    /**
     * 设置分组常量
     */
    const GROUP_BASIC = 'basic';
    const GROUP_PAYMENT = 'payment';
    const GROUP_SMS = 'sms';
    const GROUP_EMAIL = 'email';
    const GROUP_STORAGE = 'storage';
    const GROUP_SECURITY = 'security';
    const GROUP_API = 'api';
    const GROUP_SYSTEM = 'system';

    /**
     * 缓存前缀
     */
    const CACHE_PREFIX = 'system_setting:';

    /**
     * 获取数据类型选项
     */
    public static function getTypeOptions()
    {
        return [
            self::TYPE_STRING => '字符串',
            self::TYPE_INTEGER => '整数',
            self::TYPE_FLOAT => '浮点数',
            self::TYPE_BOOLEAN => '布尔值',
            self::TYPE_ARRAY => '数组',
            self::TYPE_JSON => 'JSON',
        ];
    }

    /**
     * 获取分组选项
     */
    public static function getGroupOptions()
    {
        return [
            self::GROUP_BASIC => '基础设置',
            self::GROUP_PAYMENT => '支付设置',
            self::GROUP_SMS => '短信设置',
            self::GROUP_EMAIL => '邮件设置',
            self::GROUP_STORAGE => '存储设置',
            self::GROUP_SECURITY => '安全设置',
            self::GROUP_API => 'API设置',
            self::GROUP_SYSTEM => '系统设置',
        ];
    }

    /**
     * 获取默认设置
     */
    public static function getDefaultSettings()
    {
        return [
            // 基础设置
            'site_name' => [
                'value' => '管理平台',
                'description' => '网站名称',
                'type' => self::TYPE_STRING,
                'group' => self::GROUP_BASIC,
                'is_public' => true,
            ],
            'site_logo' => [
                'value' => '',
                'description' => '网站Logo',
                'type' => self::TYPE_STRING,
                'group' => self::GROUP_BASIC,
                'is_public' => true,
            ],
            'site_description' => [
                'value' => '专业的管理平台',
                'description' => '网站描述',
                'type' => self::TYPE_STRING,
                'group' => self::GROUP_BASIC,
                'is_public' => true,
            ],
            'contact_phone' => [
                'value' => '',
                'description' => '联系电话',
                'type' => self::TYPE_STRING,
                'group' => self::GROUP_BASIC,
                'is_public' => true,
            ],
            'contact_email' => [
                'value' => '',
                'description' => '联系邮箱',
                'type' => self::TYPE_STRING,
                'group' => self::GROUP_BASIC,
                'is_public' => true,
            ],
            
            // 支付设置
            'wechat_pay_enabled' => [
                'value' => false,
                'description' => '启用微信支付',
                'type' => self::TYPE_BOOLEAN,
                'group' => self::GROUP_PAYMENT,
                'is_public' => false,
            ],
            'wechat_pay_app_id' => [
                'value' => '',
                'description' => '微信支付AppID',
                'type' => self::TYPE_STRING,
                'group' => self::GROUP_PAYMENT,
                'is_public' => false,
            ],
            'wechat_pay_mch_id' => [
                'value' => '',
                'description' => '微信支付商户号',
                'type' => self::TYPE_STRING,
                'group' => self::GROUP_PAYMENT,
                'is_public' => false,
            ],
            'alipay_enabled' => [
                'value' => false,
                'description' => '启用支付宝',
                'type' => self::TYPE_BOOLEAN,
                'group' => self::GROUP_PAYMENT,
                'is_public' => false,
            ],
            
            // 短信设置
            'sms_enabled' => [
                'value' => false,
                'description' => '启用短信服务',
                'type' => self::TYPE_BOOLEAN,
                'group' => self::GROUP_SMS,
                'is_public' => false,
            ],
            'sms_provider' => [
                'value' => 'aliyun',
                'description' => '短信服务商',
                'type' => self::TYPE_STRING,
                'group' => self::GROUP_SMS,
                'is_public' => false,
            ],
            
            // 邮件设置
            'mail_enabled' => [
                'value' => false,
                'description' => '启用邮件服务',
                'type' => self::TYPE_BOOLEAN,
                'group' => self::GROUP_EMAIL,
                'is_public' => false,
            ],
            'mail_driver' => [
                'value' => 'smtp',
                'description' => '邮件驱动',
                'type' => self::TYPE_STRING,
                'group' => self::GROUP_EMAIL,
                'is_public' => false,
            ],
            
            // 存储设置
            'storage_driver' => [
                'value' => 'local',
                'description' => '存储驱动',
                'type' => self::TYPE_STRING,
                'group' => self::GROUP_STORAGE,
                'is_public' => false,
            ],
            'upload_max_size' => [
                'value' => 10,
                'description' => '上传文件最大大小(MB)',
                'type' => self::TYPE_INTEGER,
                'group' => self::GROUP_STORAGE,
                'is_public' => true,
            ],
            
            // 安全设置
            'login_max_attempts' => [
                'value' => 5,
                'description' => '登录最大尝试次数',
                'type' => self::TYPE_INTEGER,
                'group' => self::GROUP_SECURITY,
                'is_public' => false,
            ],
            'login_lockout_minutes' => [
                'value' => 15,
                'description' => '登录锁定时间(分钟)',
                'type' => self::TYPE_INTEGER,
                'group' => self::GROUP_SECURITY,
                'is_public' => false,
            ],
            'force_https' => [
                'value' => false,
                'description' => '强制HTTPS',
                'type' => self::TYPE_BOOLEAN,
                'group' => self::GROUP_SECURITY,
                'is_public' => false,
            ],
            
            // API设置
            'api_rate_limit' => [
                'value' => 60,
                'description' => 'API速率限制(每分钟)',
                'type' => self::TYPE_INTEGER,
                'group' => self::GROUP_API,
                'is_public' => false,
            ],
            'api_version' => [
                'value' => 'v1',
                'description' => 'API版本',
                'type' => self::TYPE_STRING,
                'group' => self::GROUP_API,
                'is_public' => true,
            ],
        ];
    }

    /**
     * 获取类型标签
     */
    public function getTypeLabelAttribute()
    {
        return self::getTypeOptions()[$this->type] ?? $this->type;
    }

    /**
     * 获取分组标签
     */
    public function getGroupLabelAttribute()
    {
        return self::getGroupOptions()[$this->group] ?? $this->group;
    }

    /**
     * 获取格式化的值
     */
    public function getFormattedValueAttribute()
    {
        switch ($this->type) {
            case self::TYPE_BOOLEAN:
                return $this->value ? '是' : '否';
            case self::TYPE_ARRAY:
            case self::TYPE_JSON:
                return is_array($this->value) ? json_encode($this->value, JSON_UNESCAPED_UNICODE) : $this->value;
            default:
                return $this->value;
        }
    }

    /**
     * 设置值的访问器
     */
    public function setValueAttribute($value)
    {
        switch ($this->type) {
            case self::TYPE_INTEGER:
                $this->attributes['value'] = (int) $value;
                break;
            case self::TYPE_FLOAT:
                $this->attributes['value'] = (float) $value;
                break;
            case self::TYPE_BOOLEAN:
                $this->attributes['value'] = (bool) $value;
                break;
            case self::TYPE_ARRAY:
            case self::TYPE_JSON:
                $this->attributes['value'] = is_array($value) ? json_encode($value) : $value;
                break;
            default:
                $this->attributes['value'] = $value;
        }
    }

    /**
     * 获取值的访问器
     */
    public function getValueAttribute($value)
    {
        switch ($this->type) {
            case self::TYPE_INTEGER:
                return (int) $value;
            case self::TYPE_FLOAT:
                return (float) $value;
            case self::TYPE_BOOLEAN:
                return (bool) $value;
            case self::TYPE_ARRAY:
            case self::TYPE_JSON:
                return is_string($value) ? json_decode($value, true) : $value;
            default:
                return $value;
        }
    }

    /**
     * 作用域：按分组筛选
     */
    public function scopeByGroup($query, $group)
    {
        return $query->where('group', $group);
    }

    /**
     * 作用域：公开设置
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * 作用域：私有设置
     */
    public function scopePrivate($query)
    {
        return $query->where('is_public', false);
    }

    /**
     * 获取设置值
     */
    public static function get($key, $default = null)
    {
        $cacheKey = self::CACHE_PREFIX . $key;
        
        return Cache::remember($cacheKey, 3600, function () use ($key, $default) {
            $setting = self::where('key', $key)->first();
            return $setting ? $setting->value : $default;
        });
    }

    /**
     * 设置值
     */
    public static function set($key, $value, $description = null, $type = self::TYPE_STRING, $group = self::GROUP_BASIC, $isPublic = false)
    {
        $setting = self::updateOrCreate(
            ['key' => $key],
            [
                'value' => $value,
                'description' => $description,
                'type' => $type,
                'group' => $group,
                'is_public' => $isPublic,
            ]
        );
        
        // 清除缓存
        Cache::forget(self::CACHE_PREFIX . $key);
        
        return $setting;
    }

    /**
     * 批量设置
     */
    public static function setMany(array $settings)
    {
        foreach ($settings as $key => $config) {
            if (is_array($config)) {
                self::set(
                    $key,
                    $config['value'],
                    $config['description'] ?? null,
                    $config['type'] ?? self::TYPE_STRING,
                    $config['group'] ?? self::GROUP_BASIC,
                    $config['is_public'] ?? false
                );
            } else {
                self::set($key, $config);
            }
        }
    }

    /**
     * 删除设置
     */
    public static function remove($key)
    {
        $setting = self::where('key', $key)->first();
        if ($setting) {
            $setting->delete();
            Cache::forget(self::CACHE_PREFIX . $key);
            return true;
        }
        return false;
    }

    /**
     * 获取分组设置
     */
    public static function getByGroup($group)
    {
        return self::byGroup($group)->pluck('value', 'key')->toArray();
    }

    /**
     * 获取公开设置
     */
    public static function getPublicSettings()
    {
        $cacheKey = self::CACHE_PREFIX . 'public_settings';
        
        return Cache::remember($cacheKey, 3600, function () {
            return self::public()->pluck('value', 'key')->toArray();
        });
    }

    /**
     * 清除所有缓存
     */
    public static function clearCache()
    {
        $keys = self::pluck('key')->toArray();
        foreach ($keys as $key) {
            Cache::forget(self::CACHE_PREFIX . $key);
        }
        Cache::forget(self::CACHE_PREFIX . 'public_settings');
    }

    /**
     * 初始化默认设置
     */
    public static function initializeDefaults()
    {
        $defaults = self::getDefaultSettings();
        
        foreach ($defaults as $key => $config) {
            $exists = self::where('key', $key)->exists();
            if (!$exists) {
                self::create([
                    'key' => $key,
                    'value' => $config['value'],
                    'description' => $config['description'],
                    'type' => $config['type'],
                    'group' => $config['group'],
                    'is_public' => $config['is_public'],
                ]);
            }
        }
    }

    /**
     * 验证设置值
     */
    public function validateValue($value)
    {
        switch ($this->type) {
            case self::TYPE_INTEGER:
                return is_numeric($value) && is_int($value + 0);
            case self::TYPE_FLOAT:
                return is_numeric($value);
            case self::TYPE_BOOLEAN:
                return is_bool($value) || in_array($value, [0, 1, '0', '1', 'true', 'false']);
            case self::TYPE_ARRAY:
            case self::TYPE_JSON:
                if (is_array($value)) {
                    return true;
                }
                if (is_string($value)) {
                    json_decode($value);
                    return json_last_error() === JSON_ERROR_NONE;
                }
                return false;
            default:
                return true;
        }
    }

    /**
     * 获取设置的输入类型
     */
    public function getInputTypeAttribute()
    {
        switch ($this->type) {
            case self::TYPE_INTEGER:
            case self::TYPE_FLOAT:
                return 'number';
            case self::TYPE_BOOLEAN:
                return 'checkbox';
            case self::TYPE_ARRAY:
            case self::TYPE_JSON:
                return 'textarea';
            default:
                return 'text';
        }
    }

    /**
     * 模型事件
     */
    protected static function boot()
    {
        parent::boot();
        
        // 保存后清除缓存
        static::saved(function ($setting) {
            Cache::forget(self::CACHE_PREFIX . $setting->key);
            Cache::forget(self::CACHE_PREFIX . 'public_settings');
        });
        
        // 删除后清除缓存
        static::deleted(function ($setting) {
            Cache::forget(self::CACHE_PREFIX . $setting->key);
            Cache::forget(self::CACHE_PREFIX . 'public_settings');
        });
    }
}