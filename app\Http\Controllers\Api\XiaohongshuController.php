<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

/**
 * 小红书API客户端控制器
 */
class XiaohongshuController extends Controller
{
    /**
     * 小红书API基础URL
     */
    private $baseUrl = 'https://edith.xiaohongshu.com';

    /**
     * 应用凭据
     */
    private $appKey = 'red.U8c2BaTqz6xllLY6';
    private $appSecret = '42aab997868559f305b55a4ffd32f688';

    /**
     * 获取小红书访问令牌
     *
     * @api {post} /api/xiaohongshu/access-token 获取小红书访问令牌
     * @apiName GetXiaohongshuAccessToken
     * @apiGroup 小红书API
     * @apiVersion 1.0.0
     * @apiDescription 通过应用凭据获取小红书API访问令牌，支持缓存机制避免频繁请求
     *
     * @apiHeader {String} Content-Type application/json
     * @apiHeader {String} Accept application/json
     *
     * @apiSuccess {Number} code 响应状态码，200表示成功
     * @apiSuccess {String} message 响应消息
     * @apiSuccess {Object} data 响应数据
     * @apiSuccess {Object} data.request_params 请求小红书接口的参数
     * @apiSuccess {String} data.request_params.app_key 应用密钥
     * @apiSuccess {String} data.request_params.nonce 随机字符串
     * @apiSuccess {Number} data.request_params.timestamp 时间戳
     * @apiSuccess {String} data.request_params.signature SHA-256签名
     * @apiSuccess {Object} data.xiaohongshu_response 小红书API原始响应
     * @apiSuccess {Object} data.token_info 解析后的token信息
     * @apiSuccess {String} data.token_info.access_token 访问令牌
     * @apiSuccess {Number} data.token_info.expires_in 令牌有效期（秒）
     * @apiSuccess {String} data.token_info.token_type 令牌类型
     * @apiSuccess {Boolean} data.from_cache 是否来自缓存
     * @apiSuccess {Number} timestamp 响应时间戳
     *
     * @apiSuccessExample {json} 成功响应示例:
     * HTTP/1.1 200 OK
     * {
     *   "code": 200,
     *   "message": "获取小红书access_token成功",
     *   "data": {
     *     "request_params": {
     *       "app_key": "red.U8c2BaTqz6xllLY6",
     *       "nonce": "a1b2c3d4e5f6g7h8",
     *       "timestamp": 1752130495000,
     *       "signature": "abc123def456..."
     *     },
     *     "xiaohongshu_response": {
     *       "access_token": "xhs.xxxxxxxxxxxxxxxxxxxxx",
     *       "expires_in": 7200,
     *       "token_type": "Bearer"
     *     },
     *     "token_info": {
     *       "access_token": "xhs.xxxxxxxxxxxxxxxxxxxxx",
     *       "expires_in": 7200,
     *       "token_type": "Bearer"
     *     },
     *     "from_cache": false
     *   },
     *   "timestamp": 1752130495
     * }
     *
     * @apiError {Number} code 错误状态码
     * @apiError {String} message 错误消息
     * @apiError {Number} timestamp 响应时间戳
     *
     * @apiErrorExample {json} 错误响应示例:
     * HTTP/1.1 400 Bad Request
     * {
     *   "code": 400,
     *   "message": "小红书API调用失败：400",
     *   "timestamp": 1752130495
     * }
     *
     * @apiNote 该接口实现了智能缓存机制，token会根据expires_in自动缓存，避免频繁请求小红书API
     * @apiNote 签名算法使用SHA-256，参数按键名排序后拼接appSecret生成
     * @apiNote 缓存键格式：xiaohongshu_access_token:{app_key}
     */
    public function getAccessToken(Request $request)
    {
        try {
            // 生成参数（无论是否有缓存都生成，用于返回request_params）
            $nonce = bin2hex(random_bytes(8));
            $timestamp = time() * 1000;
            $signature = $this->generateSignature($this->appKey, $nonce, $timestamp, $this->appSecret);

            // 准备请求数据
            $requestData = [
                'app_key' => $this->appKey,
                'nonce' => $nonce,
                'timestamp' => $timestamp,
                'signature' => $signature
            ];

            // 检查缓存
            $cacheKey = "xiaohongshu_access_token:{$this->appKey}";
            $cachedToken = Cache::get($cacheKey);

            if ($cachedToken) {
                Log::info('返回缓存的小红书access_token');

                // 即使是缓存，也返回完整的数据结构
                $result = [
                    'request_params' => $requestData,  // 当前生成的请求参数
                    'xiaohongshu_response' => $cachedToken,  // 缓存的响应
                    'token_info' => $cachedToken,  // token信息
                    'from_cache' => true  // 标识来自缓存
                ];

                return $this->successResponse($result, '获取小红书access_token成功（缓存）');
            }

            // 调用小红书API
            $xiaohongshuUrl = $this->baseUrl . '/api/sns/v1/ext/access/token';

            // 发送HTTP请求到小红书API
            $response = Http::timeout(30)
                ->withHeaders([
                    'Accept' => 'application/json',
                    'User-Agent' => 'Laravel-Client/1.0'
                ])
                ->asForm()
                ->post($xiaohongshuUrl, $requestData);

            // 检查响应状态
            if (!$response->successful()) {
                Log::error('小红书API调用失败', [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);
                return $this->errorResponse('小红书API调用失败：' . $response->status(), $response->status());
            }

            $responseData = $response->json();

            // 准备返回数据，包含请求参数和响应数据
            $result = [
                'request_params' => $requestData,  // 请求小红书接口的四个参数
                'xiaohongshu_response' => $responseData,  // 小红书API的原始响应
                'from_cache' => false  // 标识来自新请求
            ];

            // 如果小红书返回了token，缓存它
            if (
                isset($responseData['access_token']) ||
                (isset($responseData['data']) && isset($responseData['data']['access_token']))
            ) {
                $tokenData = isset($responseData['data']) ? $responseData['data'] : $responseData;

                // 缓存token
                $expiresIn = $tokenData['expires_in'] ?? 7200;
                Cache::put($cacheKey, $tokenData, $expiresIn);

                // 在结果中添加解析后的token信息
                $result['token_info'] = $tokenData;

                return $this->successResponse($result, '获取小红书access_token成功');
            }

            // 直接返回包含请求参数和响应的数据
            return $this->successResponse($result, '小红书API响应');
        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->errorResponse('参数验证失败: ' . implode(', ', $e->validator->errors()->all()), 422);
        } catch (\Exception $e) {
            Log::error('获取小红书access_token失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse('服务器内部错误', 500);
        }
    }

    /**
     * 生成小红书API签名
     *
     * @apiPrivate
     * @apiDescription 内部方法：生成小红书API请求所需的SHA-256签名
     *
     * @param string $appKey 应用密钥
     * @param string $nonce 随机字符串
     * @param number $timestamp 时间戳（毫秒）
     * @param string $appSecret 应用密码
     * @return string 返回SHA-256签名字符串
     *
     * @apiNote 签名参数使用驼峰命名：appKey, nonce, timeStamp
     * @apiNote 请求参数使用下划线命名：app_key, nonce, timestamp
     * @apiNote 签名算法：参数按键名排序 → 拼接字符串 → 追加appSecret → SHA-256哈希
     * @apiNote 参数拼接格式：key1=value1&key2=value2&...&appSecret
     */
    private function generateSignature($appKey, $nonce, $timestamp, $appSecret)
    {
        // 构建签名参数 - 注意这里使用timeStamp
        $params = [
            'appKey' => $appKey,
            'nonce' => $nonce,
            'timeStamp' => $timestamp  // 签名中使用timeStamp
        ];

        // 按键名排序
        ksort($params);

        // 拼接参数字符串
        $sorted = [];
        foreach ($params as $key => $value) {
            $sorted[] = $key . '=' . $value;
        }
        $str = implode('&', $sorted) . $appSecret;

        // 生成SHA-256签名
        return hash('sha256', $str);
    }

    /**
     * 统一成功响应格式
     *
     * @apiPrivate
     * @apiDescription 内部方法：生成统一格式的成功响应
     *
     * @param mixed $data 响应数据
     * @param string $message 响应消息，默认为"操作成功"
     * @return \Illuminate\Http\JsonResponse JSON响应对象
     *
     * @apiNote 响应格式：{code: 200, message: string, data: mixed, timestamp: number}
     * @apiNote HTTP状态码固定为200
     */
    private function successResponse($data, $message = '操作成功')
    {
        return response()->json([
            'code' => 200,
            'message' => $message,
            'data' => $data,
            'timestamp' => time()
        ]);
    }

    /**
     * 统一错误响应格式
     *
     * @apiPrivate
     * @apiDescription 内部方法：生成统一格式的错误响应
     *
     * @param string $message 错误消息
     * @param int $code 错误状态码，默认为400
     * @return \Illuminate\Http\JsonResponse JSON响应对象
     *
     * @apiNote 响应格式：{code: number, message: string, timestamp: number}
     * @apiNote HTTP状态码与响应体中的code保持一致
     */
    private function errorResponse($message, $code = 400)
    {
        return response()->json([
            'code' => $code,
            'message' => $message,
            'timestamp' => time()
        ], $code);
    }
}
