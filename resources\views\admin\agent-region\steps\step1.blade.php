<div class="row" 
     @if(isset($agent_region))
     data-current-province="{{ $agent_region->province_id ?? '' }}"
     data-current-city="{{ $agent_region->city_id ?? '' }}"
     data-current-district="{{ $agent_region->district_id ?? '' }}"
     @endif
>
    <div class="col-md-6">
        <div class="form-group">
            <label for="agent_id" class="required">代理商 *</label>
            @if(isset($is_editing) && $is_editing && $agent_region)
                {{-- 编辑模式：显示代理商信息，不允许修改 --}}
                <input type="text" class="form-control" value="{{ $agent_region->agent->name ?? '未知代理商' }}" readonly>
                <input type="hidden" name="agent_id" id="agent_id" value="{{ $agent_region->agent_id }}">
                <small class="text-muted">编辑时不能更换代理商</small>
            @else
                {{-- 新增模式：允许选择代理商 --}}
                <select class="form-control" name="agent_id" id="agent_id" required>
                    <option value="">请选择代理商</option>
                    @foreach($agents as $id => $name)
                        <option value="{{ $id }}" {{ $agent_id == $id ? 'selected' : '' }}>{{ $name }}</option>
                    @endforeach
                </select>
                <small class="text-muted">选择要配置区域的代理商</small>
            @endif
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="form-group">
            <label for="region_status">区域状态</label>
            <select class="form-control" name="region_status" id="region_status">
                <option value="pending" {{ (isset($agent_region) && $agent_region->region_status == 'pending') ? 'selected' : '' }}>待分配</option>
                <option value="active" {{ (isset($agent_region) && $agent_region->region_status == 'active') || !isset($agent_region) ? 'selected' : '' }}>活跃</option>
                <option value="suspended" {{ (isset($agent_region) && $agent_region->region_status == 'suspended') ? 'selected' : '' }}>暂停</option>
                <option value="inactive" {{ (isset($agent_region) && $agent_region->region_status == 'inactive') ? 'selected' : '' }}>停用</option>
            </select>
            <small class="text-muted">设置区域的当前状态</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="form-group">
            <label for="province_id" class="required">省份 *</label>
            <select class="form-control" name="province_id" id="province_id" required>
                <option value="">请选择省份</option>
                @foreach($provinces as $id => $name)
                    <option value="{{ $id }}" {{ (isset($resolved_province_id) && $resolved_province_id == $id) ? 'selected' : '' }}>{{ $name }}</option>
                @endforeach
            </select>
            <small class="text-muted">必须选择省份</small>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="form-group">
            <label for="city_id" class="required">城市 *</label>
            <select class="form-control" name="city_id" id="city_id" required {{ !isset($cities) || $cities->isEmpty() ? 'disabled' : '' }}>
                <option value="">{{ !isset($resolved_province_id) ? '请先选择省份' : '请选择城市' }}</option>
                @if(isset($cities))
                    @foreach($cities as $id => $name)
                        <option value="{{ $id }}" {{ (isset($resolved_city_id) && $resolved_city_id == $id) ? 'selected' : '' }}>{{ $name }}</option>
                    @endforeach
                @endif
            </select>
            <small class="text-muted">可选择具体城市</small>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="form-group">
            <label for="district_id" class="required">区县 *</label>
            <select class="form-control" name="district_id" id="district_id" required {{ !isset($districts) || $districts->isEmpty() ? 'disabled' : '' }}>
                <option value="">{{ !isset($resolved_city_id) ? '请先选择城市' : '请选择区县' }}</option>
                @if(isset($districts))
                    @foreach($districts as $id => $name)
                        <option value="{{ $id }}" {{ (isset($resolved_district_id) && $resolved_district_id == $id) ? 'selected' : '' }}>{{ $name }}</option>
                    @endforeach
                @endif
            </select>
            <small class="text-muted">可选择具体区县</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label for="is_exclusive">区域独占</label>
            <div class="checkbox">
                <label>
                    <input type="checkbox" name="is_exclusive" id="is_exclusive" value="1" {{ (isset($agent_region) && $agent_region->is_exclusive) ? 'checked' : '' }}>
                    启用区域独占模式
                </label>
            </div>
            <small class="text-muted">启用后其他代理商无法配置此区域</small>
        </div>
    </div>
</div>

<div class="alert alert-info">
    <i class="fa fa-info-circle" style="margin-right: 8px;"></i>
    <strong>配置说明：</strong>
    <ul style="margin: 10px 0 0 0; padding-left: 20px;">
        <li>代理商和省份为必填项，是区域配置的基础</li>
        <li>区域可以精确到省/市/区县三个级别</li>
        <li>独占模式下，同一区域只能分配给一个代理商</li>
        @if(isset($is_editing) && $is_editing)
        <li style="color: #d9534f;">编辑模式下不能更换代理商，如需更换请删除后重新创建</li>
        @endif
    </ul>
</div>