<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AreaController;

/*
|--------------------------------------------------------------------------
| Area API Routes
|--------------------------------------------------------------------------
|
| 地区相关的API路由
| <AUTHOR>
|
*/

// 获取省份列表
Route::get('/provinces', [AreaController::class, 'getProvinces']);

// 根据省份获取城市列表
Route::get('/cities', [AreaController::class, 'getCitiesByProvince']);

// 根据城市获取区县列表
Route::get('/districts', [AreaController::class, 'getDistrictsByCity']);

// 搜索地区
Route::get('/search', [AreaController::class, 'searchAreas']);

// 获取地区路径
Route::get('/path', [AreaController::class, 'getAreaPath']);

// 需要认证的路由
Route::middleware('auth:sanctum')->group(function () {
    // 获取用户地址信息
    Route::get('/user/address', [AreaController::class, 'getUserAddress']);
});