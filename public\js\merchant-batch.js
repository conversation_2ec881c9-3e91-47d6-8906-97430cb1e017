/**
 * 商铺批量操作页面JavaScript
 */
$(document).ready(function () {
    // 初始化页面
    init();

    /**
     * 初始化页面
     */
    function init() {
        // 加载筛选条件数据
        loadFilterOptions();

        // 绑定事件
        bindEvents();
    }

    /**
     * 绑定事件
     */
    function bindEvents() {
        // 批量导入
        $('#import-form').on('submit', function (e) {
            e.preventDefault();
            handleImport();
        });

        // 批量编辑筛选
        $('#batch-edit-filter').on('submit', function (e) {
            e.preventDefault();
            loadStoresForBatchEdit();
        });

        // 批量编辑表单提交
        $('#batch-update-form').on('submit', function (e) {
            e.preventDefault();
            handleBatchUpdate();
        });

        // 批量导出
        $('#export-form').on('submit', function (e) {
            e.preventDefault();
            handleExport();
        });

        // 全选商铺
        $('#select-all-stores').on('change', function () {
            const isChecked = $(this).is(':checked');
            $('.store-checkbox').prop('checked', isChecked);
            updateSelectedCount();
        });

        // 单个商铺选择
        $(document).on('change', '.store-checkbox', function () {
            updateSelectedCount();
        });

        // 全选字段
        $('#select-all-fields').on('change', function () {
            const isChecked = $(this).is(':checked');
            $('input[name="fields[]"]:checked').prop('checked', isChecked);
        });

        // 省份变化时更新城市
        $('select[name="province_id"]').on('change', function () {
            const provinceId = $(this).val();
            const citySelect = $(this).closest('form').find('select[name="city_id"]');
            loadCities(provinceId, citySelect);
        });
    }

    /**
     * 加载筛选条件数据
     */
    function loadFilterOptions() {
        // 加载代理商列表
        $.ajax({
            url: '/admin/merchant/api/agents',
            type: 'GET',
            dataType: 'json',
            success: function (response) {
                if (response.success) {
                    const agentSelects = $('select[name="agent_id"]');
                    agentSelects.each(function () {
                        const select = $(this);
                        select.empty().append('<option value="">选择代理商</option>');

                        response.data.forEach(function (agent) {
                            select.append('<option value="' + agent.id + '">' + agent.name + '</option>');
                        });
                    });
                }
            }
        });

        // 加载省份列表
        $.ajax({
            url: '/admin/merchant/api/provinces',
            type: 'GET',
            dataType: 'json',
            success: function (response) {
                if (response.success) {
                    const provinceSelects = $('select[name="province_id"]');
                    provinceSelects.each(function () {
                        const select = $(this);
                        select.empty().append('<option value="">选择省份</option>');

                        response.data.forEach(function (province) {
                            select.append('<option value="' + province.id + '">' + province.name + '</option>');
                        });
                    });
                }
            }
        });
    }

    /**
     * 加载城市列表
     */
    function loadCities(provinceId, citySelect) {
        citySelect.empty().append('<option value="">选择城市</option>');

        if (!provinceId) {
            return;
        }

        $.ajax({
            url: '/admin/merchant/api/cities/' + provinceId,
            type: 'GET',
            dataType: 'json',
            success: function (response) {
                if (response.success) {
                    response.data.forEach(function (city) {
                        citySelect.append('<option value="' + city.id + '">' + city.name + '</option>');
                    });
                }
            }
        });
    }

    /**
     * 处理批量导入
     */
    function handleImport() {
        const fileInput = $('#import-file')[0];
        const file = fileInput.files[0];

        if (!file) {
            toastr.error('请选择要导入的文件');
            return;
        }

        // 验证文件类型
        const allowedTypes = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel'];
        if (!allowedTypes.includes(file.type)) {
            toastr.error('请选择Excel文件(.xlsx或.xls)');
            return;
        }

        // 验证文件大小（10MB）
        if (file.size > 10 * 1024 * 1024) {
            toastr.error('文件大小不能超过10MB');
            return;
        }

        const formData = new FormData();
        formData.append('import_file', file);
        formData.append('_token', $('meta[name="csrf-token"]').attr('content'));

        // 显示进度条
        $('#import-progress').show();
        const progressBar = $('#import-progress .progress-bar');

        $.ajax({
            url: '/admin/merchant/api/import',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            xhr: function () {
                const xhr = new window.XMLHttpRequest();
                xhr.upload.addEventListener('progress', function (evt) {
                    if (evt.lengthComputable) {
                        const percentComplete = (evt.loaded / evt.total) * 100;
                        progressBar.css('width', percentComplete + '%');
                    }
                }, false);
                return xhr;
            },
            success: function (response) {
                $('#import-progress').hide();

                if (response.success) {
                    showImportResult(response.data);
                } else {
                    toastr.error(response.message || '导入失败');
                }
            },
            error: function (xhr, status, error) {
                $('#import-progress').hide();
                console.error('导入失败:', error);
                toastr.error('导入失败，请重试');
            }
        });
    }

    /**
     * 显示导入结果
     */
    function showImportResult(data) {
        const summary = '成功导入 ' + data.success_count + ' 条记录，失败 ' + data.error_count + ' 条记录';
        $('#import-summary').text(summary);

        if (data.errors && data.errors.length > 0) {
            const errorsTbody = $('#import-errors-tbody');
            errorsTbody.empty();

            data.errors.forEach(function (error) {
                errorsTbody.append(
                    '<tr>' +
                    '<td>' + error.row + '</td>' +
                    '<td>' + error.message + '</td>' +
                    '</tr>'
                );
            });

            $('#import-errors').show();
        }

        $('#import-result').show();
    }

    /**
     * 加载商铺列表用于批量编辑
     */
    function loadStoresForBatchEdit() {
        const filterData = $('#batch-edit-filter').serialize();

        $.ajax({
            url: '/admin/merchant/api/list-for-batch-edit',
            type: 'GET',
            data: filterData,
            dataType: 'json',
            success: function (response) {
                if (response.success) {
                    populateStoreList(response.data);
                    $('#batch-edit-list').show();
                    $('#batch-edit-form').show();
                } else {
                    toastr.error(response.message || '加载商铺列表失败');
                }
            },
            error: function (xhr, status, error) {
                console.error('加载商铺列表失败:', error);
                toastr.error('网络错误，请重试');
            }
        });
    }

    /**
     * 填充商铺列表
     */
    function populateStoreList(stores) {
        const tbody = $('#batch-edit-tbody');
        tbody.empty();

        if (!stores || stores.length === 0) {
            tbody.append('<tr><td colspan="6" class="text-center">没有找到符合条件的商铺</td></tr>');
            return;
        }

        stores.forEach(function (store) {
            const statusText = getStatusText(store.status);
            const agentName = store.agent ? store.agent.name : '-';
            const contactPerson = store.contact_person || '-';
            const phone = store.phone || '-';

            tbody.append(
                '<tr>' +
                '<td><input type="checkbox" class="store-checkbox" value="' + store.id + '"></td>' +
                '<td>' + store.name + '</td>' +
                '<td>' + agentName + '</td>' +
                '<td>' + contactPerson + '</td>' +
                '<td>' + phone + '</td>' +
                '<td>' + statusText + '</td>' +
                '</tr>'
            );
        });
    }

    /**
     * 获取状态文本
     */
    function getStatusText(status) {
        const statusMap = {
            'active': '营业中',
            'inactive': '已关闭',
            'suspended': '已暂停',
            'pending': '待审核'
        };
        return statusMap[status] || status;
    }

    /**
     * 更新选中数量
     */
    function updateSelectedCount() {
        const selectedCount = $('.store-checkbox:checked').length;
        $('#selected-count').text('(已选择 ' + selectedCount + ' 个商铺)');

        // 如果有选中的商铺，显示批量编辑表单
        if (selectedCount > 0) {
            $('#batch-edit-form').show();
        } else {
            $('#batch-edit-form').hide();
        }
    }

    /**
     * 处理批量更新
     */
    function handleBatchUpdate() {
        const selectedStores = $('.store-checkbox:checked').map(function () {
            return $(this).val();
        }).get();

        if (selectedStores.length === 0) {
            toastr.error('请选择要更新的商铺');
            return;
        }

        const formData = $('#batch-update-form').serialize();

        $.ajax({
            url: '/admin/merchant/api/batch-update',
            type: 'POST',
            data: formData + '&store_ids=' + selectedStores.join(','),
            dataType: 'json',
            success: function (response) {
                if (response.success) {
                    toastr.success('成功更新 ' + response.data.updated_count + ' 个商铺');

                    // 重新加载列表
                    loadStoresForBatchEdit();
                } else {
                    toastr.error(response.message || '批量更新失败');
                }
            },
            error: function (xhr, status, error) {
                console.error('批量更新失败:', error);
                toastr.error('网络错误，请重试');
            }
        });
    }

    /**
     * 处理批量导出
     */
    function handleExport() {
        const formData = $('#export-form').serialize();

        // 检查是否选择了导出字段
        const selectedFields = $('input[name="fields[]"]:checked').length;
        if (selectedFields === 0) {
            toastr.error('请选择要导出的字段');
            return;
        }

        // 创建下载链接
        const downloadUrl = '/admin/merchant/api/export?' + formData;

        // 创建隐藏的下载链接并触发下载
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = '商铺数据_' + new Date().toISOString().split('T')[0] + '.xlsx';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        toastr.success('导出任务已开始，请稍候...');
    }
}); 