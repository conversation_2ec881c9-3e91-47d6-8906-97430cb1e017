<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class AdminTablesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     * 初始化 Laravel Admin 管理员数据
     */
    public function run(): void
    {
        // 检查是否已存在管理员用户，避免重复插入
        if (DB::table('admin_users')->where('username', 'admin')->exists()) {
            $this->command->info('Admin user already exists, skipping...');
            return;
        }

        // 创建默认管理员用户
        DB::table('admin_users')->insert([
            'username' => 'admin',
            'password' => Hash::make('admin'),
            'name' => 'Administrator',
            'avatar' => null,
            'remember_token' => null,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // 创建默认角色
        DB::table('admin_roles')->insert([
            'name' => 'Administrator',
            'slug' => 'administrator',
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // 创建默认权限
        $permissions = [
            ['name' => 'All permission', 'slug' => '*', 'http_method' => '', 'http_path' => '*'],
            ['name' => 'Dashboard', 'slug' => 'dashboard', 'http_method' => 'GET', 'http_path' => '/'],
            ['name' => 'Login', 'slug' => 'auth.login', 'http_method' => '', 'http_path' => '/auth/login\r\n/auth/logout'],
            ['name' => 'User setting', 'slug' => 'auth.setting', 'http_method' => 'GET,PUT', 'http_path' => '/auth/setting'],
            ['name' => 'Auth management', 'slug' => 'auth.management', 'http_method' => '', 'http_path' => '/auth/roles\r\n/auth/permissions\r\n/auth/menu\r\n/auth/logs'],
        ];

        foreach ($permissions as $permission) {
            $permission['created_at'] = now();
            $permission['updated_at'] = now();
            DB::table('admin_permissions')->insert($permission);
        }

        // 分配角色给用户
        DB::table('admin_role_users')->insert([
            'role_id' => 1,
            'user_id' => 1,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // 分配权限给角色
        DB::table('admin_role_permissions')->insert([
            'role_id' => 1,
            'permission_id' => 1,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // 创建默认菜单
        $menus = [
            ['parent_id' => 0, 'order' => 1, 'title' => 'Dashboard', 'icon' => 'fa-bar-chart', 'uri' => '/', 'permission' => null],
            ['parent_id' => 0, 'order' => 2, 'title' => 'Admin', 'icon' => 'fa-tasks', 'uri' => '', 'permission' => null],
            ['parent_id' => 2, 'order' => 3, 'title' => 'Users', 'icon' => 'fa-users', 'uri' => 'auth/users', 'permission' => null],
            ['parent_id' => 2, 'order' => 4, 'title' => 'Roles', 'icon' => 'fa-user', 'uri' => 'auth/roles', 'permission' => null],
            ['parent_id' => 2, 'order' => 5, 'title' => 'Permission', 'icon' => 'fa-ban', 'uri' => 'auth/permissions', 'permission' => null],
            ['parent_id' => 2, 'order' => 6, 'title' => 'Menu', 'icon' => 'fa-bars', 'uri' => 'auth/menu', 'permission' => null],
            ['parent_id' => 2, 'order' => 7, 'title' => 'Operation log', 'icon' => 'fa-history', 'uri' => 'auth/logs', 'permission' => null],
        ];

        foreach ($menus as $menu) {
            $menu['created_at'] = now();
            $menu['updated_at'] = now();
            DB::table('admin_menu')->insert($menu);
        }

        $this->command->info('Admin tables seeded successfully!');
    }
}
