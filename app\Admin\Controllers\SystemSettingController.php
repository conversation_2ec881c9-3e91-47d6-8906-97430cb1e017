<?php 

namespace App\Admin\Controllers;

use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;
use Encore\Admin\Layout\Content;
use Encore\Admin\Widgets\InfoBox;
use Encore\Admin\Widgets\Tab;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Artisan;

class SystemSettingController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = '系统设置';

    /**
     * 系统设置首页
     */
    public function index(Content $content)
    {
        return $content
            ->title('系统设置')
            ->description('系统配置管理')
            ->body($this->settingsTabs());
    }

    /**
     * 设置选项卡
     */
    protected function settingsTabs()
    {
        $tab = new Tab();

        $tab->add('基础设置', $this->basicSettings());
        $tab->add('支付设置', $this->paymentSettings());
        $tab->add('短信设置', $this->smsSettings());
        $tab->add('邮件设置', $this->emailSettings());
        $tab->add('存储设置', $this->storageSettings());
        $tab->add('安全设置', $this->securitySettings());
        $tab->add('API设置', $this->apiSettings());
        $tab->add('系统信息', $this->systemInfo());

        return $tab;
    }

    /**
     * 基础设置
     */
    protected function basicSettings()
    {
        $form = new Form(null);
        $form->action(url(config('admin.route.prefix', 'admin') . '/system-settings/basic'));
        
        $form->text('site_name', '网站名称')->default(config('app.name'));
        $form->text('site_url', '网站地址')->default(config('app.url'));
        $form->textarea('site_description', '网站描述');
        $form->text('site_keywords', '网站关键词');
        $form->image('site_logo', '网站Logo');
        $form->image('site_favicon', '网站图标');
        $form->text('icp_number', 'ICP备案号');
        $form->text('contact_phone', '联系电话');
        $form->text('contact_email', '联系邮箱');
        $form->textarea('copyright', '版权信息');
        
        $form->tools(function ($tools) {
            $tools->disableList();
            $tools->disableDelete();
            $tools->disableView();
        });

        return $form;
    }

    /**
     * 支付设置
     */
    protected function paymentSettings()
    {
        $form = new Form(null);
        $form->action(url(config('admin.route.prefix', 'admin') . '/system-settings/payment'));
        
        $form->divider('微信支付设置');
        $form->text('wechat_app_id', '微信AppID');
        $form->text('wechat_mch_id', '微信商户号');
        $form->password('wechat_key', '微信支付密钥');
        $form->file('wechat_cert_path', '微信证书文件');
        $form->file('wechat_key_path', '微信私钥文件');
        
        $form->divider('支付宝设置');
        $form->text('alipay_app_id', '支付宝AppID');
        $form->textarea('alipay_public_key', '支付宝公钥');
        $form->textarea('alipay_private_key', '支付宝私钥');
        
        $form->divider('其他设置');
        $form->currency('min_recharge_amount', '最小充值金额')->symbol('￥');
        $form->currency('max_recharge_amount', '最大充值金额')->symbol('￥');
        $form->number('commission_rate', '平台佣金比例(%)')->min(0)->max(100);
        
        $form->tools(function ($tools) {
            $tools->disableList();
            $tools->disableDelete();
            $tools->disableView();
        });

        return $form;
    }

    /**
     * 短信设置
     */
    protected function smsSettings()
    {
        $form = new Form(null);
        $form->action(url(config('admin.route.prefix', 'admin') . '/system-settings/sms'));
        
        $form->select('sms_provider', '短信服务商')->options([
            'aliyun' => '阿里云',
            'tencent' => '腾讯云',
            'huawei' => '华为云'
        ]);
        $form->text('sms_access_key', 'AccessKey');
        $form->password('sms_secret_key', 'SecretKey');
        $form->text('sms_sign_name', '短信签名');
        $form->text('sms_template_code', '短信模板ID');
        $form->number('sms_rate_limit', '短信发送频率限制(秒)')->default(60);
        $form->switch('sms_enabled', '启用短信服务')->default(1);
        
        $form->tools(function ($tools) {
            $tools->disableList();
            $tools->disableDelete();
            $tools->disableView();
        });

        return $form;
    }

    /**
     * 邮件设置
     */
    protected function emailSettings()
    {
        $form = new Form(null);
        $form->action(url(config('admin.route.prefix', 'admin') . '/system-settings/email'));
        
        $form->select('mail_driver', '邮件驱动')->options([
            'smtp' => 'SMTP',
            'sendmail' => 'Sendmail',
            'mailgun' => 'Mailgun'
        ]);
        $form->text('mail_host', 'SMTP服务器');
        $form->number('mail_port', 'SMTP端口')->default(587);
        $form->text('mail_username', 'SMTP用户名');
        $form->password('mail_password', 'SMTP密码');
        $form->select('mail_encryption', '加密方式')->options([
            'tls' => 'TLS',
            'ssl' => 'SSL'
        ]);
        $form->text('mail_from_address', '发件人邮箱');
        $form->text('mail_from_name', '发件人姓名');
        $form->switch('mail_enabled', '启用邮件服务')->default(1);
        
        $form->tools(function ($tools) {
            $tools->disableList();
            $tools->disableDelete();
            $tools->disableView();
        });

        return $form;
    }

    /**
     * 存储设置
     */
    protected function storageSettings()
    {
        $form = new Form(null);
        $form->action(url(config('admin.route.prefix', 'admin') . '/system-settings/storage'));
        
        $form->select('storage_driver', '存储驱动')->options([
            'local' => '本地存储',
            'oss' => '阿里云OSS',
            'cos' => '腾讯云COS',
            'qiniu' => '七牛云'
        ]);
        
        $form->divider('阿里云OSS设置');
        $form->text('oss_access_key', 'OSS AccessKey');
        $form->password('oss_secret_key', 'OSS SecretKey');
        $form->text('oss_bucket', 'OSS Bucket');
        $form->text('oss_endpoint', 'OSS Endpoint');
        $form->text('oss_domain', 'OSS 自定义域名');
        
        $form->divider('文件上传设置');
        $form->text('upload_max_size', '最大上传文件大小(MB)')->default(10);
        $form->text('upload_allowed_ext', '允许上传的文件类型')->placeholder('jpg,png,gif,pdf,doc,docx');
        
        $form->tools(function ($tools) {
            $tools->disableList();
            $tools->disableDelete();
            $tools->disableView();
        });

        return $form;
    }

    /**
     * 安全设置
     */
    protected function securitySettings()
    {
        $form = new Form(null);
        $form->action(url(config('admin.route.prefix', 'admin') . '/system-settings/security'));
        
        $form->number('login_max_attempts', '登录最大尝试次数')->default(5);
        $form->number('login_lockout_time', '登录锁定时间(分钟)')->default(15);
        $form->switch('force_https', '强制HTTPS')->default(0);
        $form->switch('enable_captcha', '启用验证码')->default(1);
        $form->number('session_lifetime', '会话超时时间(分钟)')->default(120);
        $form->switch('enable_ip_whitelist', '启用IP白名单')->default(0);
        $form->textarea('ip_whitelist', 'IP白名单')->placeholder('每行一个IP地址');
        $form->switch('enable_operation_log', '启用操作日志')->default(1);
        $form->number('log_retention_days', '日志保留天数')->default(30);
        
        $form->tools(function ($tools) {
            $tools->disableList();
            $tools->disableDelete();
            $tools->disableView();
        });

        return $form;
    }

    /**
     * API设置
     */
    protected function apiSettings()
    {
        $form = new Form(null);
        $form->action(url(config('admin.route.prefix', 'admin') . '/system-settings/api'));
        
        $form->switch('api_enabled', '启用API')->default(1);
        $form->number('api_rate_limit', 'API请求频率限制(次/分钟)')->default(60);
        $form->text('api_version', 'API版本')->default('v1');
        $form->textarea('api_allowed_origins', '允许的跨域来源')->placeholder('每行一个域名');
        $form->switch('api_debug_mode', 'API调试模式')->default(0);
        
        $form->divider('第三方API设置');
        $form->text('baidu_map_ak', '百度地图AK');
        $form->text('amap_key', '高德地图Key');
        $form->text('ai_api_key', 'AI服务API Key');
        $form->text('ai_api_url', 'AI服务API地址');
        
        $form->tools(function ($tools) {
            $tools->disableList();
            $tools->disableDelete();
            $tools->disableView();
        });

        return $form;
    }

    /**
     * 系统信息
     */
    protected function systemInfo()
    {
        $info = [
            'PHP版本' => phpversion(),
            'Laravel版本' => app()->version(),
            'Laravel-Admin版本' => '1.8.x',
            '服务器软件' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            '操作系统' => php_uname('s'),
            '数据库版本' => $this->getDatabaseVersion(),
            '内存限制' => ini_get('memory_limit'),
            '上传限制' => ini_get('upload_max_filesize'),
            '时区' => config('app.timezone'),
            '调试模式' => config('app.debug') ? '开启' : '关闭',
            '环境' => config('app.env'),
        ];

        $html = '<div class="box box-info"><div class="box-header with-border"><h3 class="box-title">系统信息</h3></div><div class="box-body"><table class="table table-bordered">';
        foreach ($info as $key => $value) {
            $html .= "<tr><td width='200'><strong>{$key}</strong></td><td>{$value}</td></tr>";
        }
        $html .= '</table></div></div>';

        return $html;
    }

    /**
     * 获取数据库版本
     */
    protected function getDatabaseVersion()
    {
        $result = DB::select('SELECT VERSION() as version');
        return !empty($result) ? $result[0]->version : 'Unknown';
    }

    /**
     * 保存基础设置
     */
    public function saveBasicSettings(Request $request)
    {
        // 保存设置逻辑
        return response()->json(['message' => '基础设置保存成功']);
    }

    /**
     * 保存支付设置
     */
    public function savePaymentSettings(Request $request)
    {
        // 保存设置逻辑
        return response()->json(['message' => '支付设置保存成功']);
    }

    /**
     * 保存短信设置
     */
    public function saveSmsSettings(Request $request)
    {
        // 保存设置逻辑
        return response()->json(['message' => '短信设置保存成功']);
    }

    /**
     * 保存邮件设置
     */
    public function saveEmailSettings(Request $request)
    {
        // 保存设置逻辑
        return response()->json(['message' => '邮件设置保存成功']);
    }

    /**
     * 保存存储设置
     */
    public function saveStorageSettings(Request $request)
    {
        // 保存设置逻辑
        return response()->json(['message' => '存储设置保存成功']);
    }

    /**
     * 保存安全设置
     */
    public function saveSecuritySettings(Request $request)
    {
        // 保存设置逻辑
        return response()->json(['message' => '安全设置保存成功']);
    }

    /**
     * 保存API设置
     */
    public function saveApiSettings(Request $request)
    {
        // 保存设置逻辑
        return response()->json(['message' => 'API设置保存成功']);
    }

    /**
     * 清除缓存
     */
    public function clearCache()
    {
        Artisan::call('cache:clear');
        Artisan::call('config:clear');
        Artisan::call('route:clear');
        Artisan::call('view:clear');
        
        return response()->json(['message' => '缓存清除成功']);
    }

    /**
     * 系统优化
     */
    public function optimize()
    {
        Artisan::call('optimize');
        Artisan::call('config:cache');
        Artisan::call('route:cache');
        
        return response()->json(['message' => '系统优化完成']);
    }
}