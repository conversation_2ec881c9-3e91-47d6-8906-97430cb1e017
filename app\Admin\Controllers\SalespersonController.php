<?php

namespace App\Admin\Controllers;

use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;
use Encore\Admin\Layout\Content;
use App\Models\Salesperson;
use App\Models\Agent;
use App\Services\PermissionService;

class SalespersonController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = '业务员管理';

    /**
     * 业务员列表页面
     */
    public function index(Content $content)
    {
        // 检查权限
        if (!PermissionService::hasPermission('salesperson.list')) {
            abort(403, '您没有权限访问业务员列表');
        }

        return $content
            ->title($this->title)
            ->description('业务员列表')
            ->body($this->grid());
    }

    /**
     * 业务员详情页面
     */
    public function show($id, Content $content)
    {
        // 检查权限
        if (!PermissionService::hasPermission('salesperson.show')) {
            abort(403, '您没有权限查看业务员详情');
        }

        return $content
            ->title('业务员详情')
            ->description('查看业务员详细信息')
            ->body($this->detail($id));
    }

    /**
     * 创建业务员页面
     */
    public function create(Content $content)
    {
        // 检查权限
        if (!PermissionService::hasPermission('salesperson.create')) {
            abort(403, '您没有权限创建业务员');
        }

        return $content
            ->title('创建业务员')
            ->description('添加新的业务员')
            ->body($this->form());
    }

    /**
     * 编辑业务员页面
     */
    public function edit($id, Content $content)
    {
        // 检查权限
        if (!PermissionService::hasPermission('salesperson.edit')) {
            abort(403, '您没有权限编辑业务员');
        }

        return $content
            ->title('编辑业务员')
            ->description('修改业务员信息')
            ->body($this->form()->edit($id));
    }

    /**
     * 存储业务员
     */
    public function store()
    {
        // 检查权限
        if (!PermissionService::hasPermission('salesperson.create')) {
            abort(403, '您没有权限创建业务员');
        }

        return $this->form()->store();
    }

    /**
     * 更新业务员
     */
    public function update($id)
    {
        // 检查权限
        if (!PermissionService::hasPermission('salesperson.edit')) {
            abort(403, '您没有权限编辑业务员');
        }

        return $this->form()->update($id);
    }

    /**
     * 删除业务员
     */
    public function destroy($id)
    {
        // 检查权限
        if (!PermissionService::hasPermission('salesperson.delete')) {
            abort(403, '您没有权限删除业务员');
        }

        return $this->form()->destroy($id);
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new Salesperson());

        // 根据用户权限过滤数据
        if (!PermissionService::isPlatformAdmin()) {
            $accessibleAgentIds = PermissionService::getAccessibleAgentIds();
            $grid->model()->whereIn('agent_id', $accessibleAgentIds);
        }

        $grid->column('id', __('ID'));
        $grid->column('name', __('业务员姓名'));
        $grid->column('phone', __('联系电话'));
        $grid->column('email', __('邮箱'));
        $grid->column('identity_type', __('身份类型'))->using([
            'agent' => '代理商业务员',
            'platform' => '平台自营业务员'
        ])->label([
            'agent' => 'primary',
            'platform' => 'success'
        ]);
        $grid->column('agent.name', __('所属代理商'));
        $grid->column('team.name', __('所属团队'));
        $grid->column('commission_rate', __('提成比例(%)'));
        $grid->column('max_merchants', __('可管理商家数'));
        $grid->column('status', __('状态'))->using([
            1 => '在职',
            2 => '离职',
            0 => '禁用'
        ])->label([
            1 => 'success',
            2 => 'warning',
            0 => 'danger'
        ]);
        $grid->column('created_at', __('入职时间'));
        $grid->column('updated_at', __('更新时间'));

        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(Salesperson::findOrFail($id));

        $show->field('id', __('ID'));
        $show->field('name', __('业务员姓名'));
        $show->field('phone', __('联系电话'));
        $show->field('email', __('邮箱'));
        $show->field('identity_type', __('身份类型'));
        $show->field('agent.name', __('所属代理商'));
        $show->field('team.name', __('所属团队'));
        $show->field('commission_rate', __('提成比例(%)'));
        $show->field('max_merchants', __('可管理商家数'));
        $show->field('promotion_permissions', __('推广权限'));
        $show->field('incentive_config', __('激励配置'));
        $show->field('status', __('状态'));
        $show->field('remark', __('备注'));
        $show->field('created_at', __('入职时间'));
        $show->field('updated_at', __('更新时间'));

        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new Salesperson());

        $form->text('name', __('业务员姓名'))->required();
        $form->mobile('phone', __('联系电话'))->required();
        $form->email('email', __('邮箱'))->required();
        $form->select('identity_type', __('身份类型'))->options([
            'agent' => '代理商业务员',
            'platform' => '平台自营业务员'
        ])->required();

        $form->when('identity_type', 'agent', function (Form $form) {
            // 根据权限过滤可选的代理商
            $agentOptions = Agent::query();
            if (!PermissionService::isPlatformAdmin()) {
                $agentOptions->whereIn('id', PermissionService::getAccessibleAgentIds());
            }
            $form->select('agent_id', __('所属代理商'))->options($agentOptions->pluck('name', 'id'));
        });

        $form->select('team_id', __('所属团队'))->options(\App\Models\Team::all()->pluck('name', 'id'));
        $form->decimal('commission_rate', __('提成比例(%)'))->default(5.00);
        $form->number('max_merchants', __('可管理商家数'))->default(50);

        $form->checkbox('promotion_permissions', __('推广权限'))->options([
            'add_merchant' => '添加商家',
            'edit_merchant' => '编辑商家信息',
            'view_data' => '查看数据统计',
            'material_upload' => '上传素材'
        ]);

        $form->textarea('incentive_config', __('激励配置'))->placeholder('JSON格式的激励规则配置');

        $form->select('status', __('状态'))->options([
            1 => '在职',
            2 => '离职',
            0 => '禁用'
        ])->default(1);

        $form->textarea('remark', __('备注'));

        return $form;
    }
}
