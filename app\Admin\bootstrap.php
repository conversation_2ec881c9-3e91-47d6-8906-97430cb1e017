<?php

/**
 * <PERSON>vel-admin - admin builder based on Lara<PERSON>.
 * <AUTHOR> <https://github.com/z-song>
 *
 * Bootstraper for Admin.
 *
 * Here you can remove builtin form field:
 * Encore\Admin\Form::forget(['map', 'editor']);
 *
 * Or extend custom form field:
 * Encore\Admin\Form::extend('php', PHPEditor::class);
 *
 * Or require js and css assets:
 * Admin::css('/packages/prettydocs/css/styles.css');
 * Admin::js('/packages/prettydocs/js/main.js');
 *
 */

use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;
use Encore\Admin\Admin;

// 引入图表库 - 确保加载顺序正确
Admin::js('/js/echarts.min.js');              // 第一个加载ECharts
Admin::js('/js/chart.min.js');                // 第二个加载Chart.js
Admin::js('/js/dashboard-charts.js');         // 第三个加载仪表板图表脚本，依赖前两个
Admin::js('/js/dashboard-charts-safe.js');    // 第四个加载安全的图表脚本

// 添加全局JavaScript文件
Admin::js('/js/agent-form.js');
Admin::js('/js/agent-region-form.js');

Form::forget(['map', 'editor']);
