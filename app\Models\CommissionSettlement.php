<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class CommissionSettlement extends Model
{
    protected $table = 'commission_settlements';

    protected $fillable = [
        'settlement_no',
        'target_type',
        'target_id',
        'settlement_period_start',
        'settlement_period_end',
        'total_amount',
        'commission_amount',
        'deduction_amount',
        'actual_amount',
        'store_count',
        'active_store_count',
        'status',
        'remark',
        'detail_data',
        'approved_at',
        'approved_by',
        'paid_at',
        'payment_method',
        'payment_reference'
    ];

    protected $casts = [
        'settlement_period_start' => 'date',
        'settlement_period_end' => 'date',
        'total_amount' => 'decimal:2',
        'commission_amount' => 'decimal:2',
        'deduction_amount' => 'decimal:2',
        'actual_amount' => 'decimal:2',
        'store_count' => 'integer',
        'active_store_count' => 'integer',
        'detail_data' => 'json',
        'approved_at' => 'datetime',
        'paid_at' => 'datetime'
    ];

    /**
     * 获取结算对象（多态关联）
     */
    public function target(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * 获取状态标签颜色
     */
    public function getStatusLabelAttribute()
    {
        return [
            'pending' => 'warning',
            'approved' => 'info',
            'paid' => 'success',
            'rejected' => 'danger'
        ][$this->status] ?? 'default';
    }

    /**
     * 获取状态名称
     */
    public function getStatusNameAttribute()
    {
        return [
            'pending' => '待审核',
            'approved' => '已审核',
            'paid' => '已支付',
            'rejected' => '已拒绝'
        ][$this->status] ?? '未知';
    }

    /**
     * 获取结算对象名称
     */
    public function getTargetNameAttribute()
    {
        switch ($this->target_type) {
            case 'agent':
                $agent = \App\Models\Agent::find($this->target_id);
                return $agent ? $agent->name : '未知代理商';
            case 'salesperson':
                $salesperson = \App\Models\Salesperson::find($this->target_id);
                return $salesperson ? $salesperson->name : '未知业务员';
            case 'team_leader':
                $team = \App\Models\Team::find($this->target_id);
                return $team ? $team->name : '未知团队';
            default:
                return '未知对象';
        }
    }

    /**
     * 获取结算对象类型名称
     */
    public function getTargetTypeNameAttribute()
    {
        $types = [
            'agent' => '代理商',
            'salesperson' => '业务员',
            'team_leader' => '团队长'
        ];
        return $types[$this->target_type] ?? $this->target_type;
    }

    /**
     * 获取审核人名称
     */
    public function getApproverNameAttribute()
    {
        if (!$this->approved_by) {
            return null;
        }

        $admin = \Encore\Admin\Auth\Database\Administrator::find($this->approved_by);
        return $admin ? $admin->name : '未知管理员';
    }

    /**
     * 获取审核状态中文名称
     */
    public static function getAuditStatusName($status)
    {
        $statusMap = [
            'pending' => '待审核',
            'approved' => '已通过',
            'rejected' => '已拒绝',
        ];
        return $statusMap[$status] ?? $status;
    }

    /**
     * 获取商铺状态中文名称
     */
    public static function getStoreStatusName($status)
    {
        $statusMap = [
            'active' => '营业中',
            'inactive' => '已关闭',
            'suspended' => '已暂停',
        ];
        return $statusMap[$status] ?? $status;
    }

    /**
     * 获取关联的商铺数据
     */
    public function getStoresData()
    {
        if ($this->target_type !== 'agent') {
            return collect();
        }

        // 获取与当前结算单相关的商铺（通过last_settlement_id关联）
        return \App\Models\Store::where('last_settlement_id', $this->id)
            ->get()
            ->map(function ($store) {
                return [
                    'id' => $store->id,
                    'name' => $store->name,
                    'contact_person' => $store->contact_person,
                    'phone' => $store->phone,
                    'address' => $store->address,
                    'product_amount' => $store->product_amount,
                    'is_settled' => $store->is_settled,
                    'agent_id' => $store->agent_id,
                    'province_name' => $store->province_name,
                    'city_name' => $store->city_name,
                    'district_name' => $store->district_name,
                    'audit_status' => $store->audit_status,
                    'audit_status_name' => self::getAuditStatusName($store->audit_status),
                    'store_status' => $store->store_status,
                    'store_status_name' => self::getStoreStatusName($store->store_status),
                    'audit_remark' => $store->audit_remark,
                    'last_settlement_id' => $store->last_settlement_id,
                    'settlement_status' => $store->settlement_status,
                ];
            });
    }
}
