@extends('admin::layouts.content')

@section('content')
<div class="row">
    <div class="col-md-12">
        <div class="box box-primary">
            <div class="box-header with-border">
                <h3 class="box-title">
                    <i class="fa fa-plus"></i> 为 {{ $parentAgent->name }} 添加下级代理商
                </h3>
                <div class="box-tools pull-right">
                    <a href="{{ admin_url('agents/' . $parentAgent->id . '/hierarchy-detail') }}" class="btn btn-sm btn-default">
                        <i class="fa fa-arrow-left"></i> 返回层级详情
                    </a>
                </div>
            </div>
            <div class="box-body">
                <!-- 上级代理商信息 -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="alert alert-info">
                            <h4><i class="fa fa-info-circle"></i> 上级代理商信息</h4>
                            <p>
                                <strong>代理商名称:</strong> {{ $parentAgent->name }}<br>
                                <strong>联系人:</strong> {{ $parentAgent->contact_person }}<br>
                                <strong>电话:</strong> {{ $parentAgent->phone }}<br>
                                <strong>当前下级代理商:</strong> {{ $parentAgent->current_sub_agents }}/{{ $parentAgent->max_sub_agents }}
                                @if($parentAgent->max_sub_agents > 0)
                                (还可添加 {{ $parentAgent->max_sub_agents - $parentAgent->current_sub_agents }} 个)
                                @endif
                            </p>
                        </div>
                    </div>
                </div>

                <!-- 表单 -->
                <form method="POST" action="{{ admin_url('agents/' . $parentAgent->id . '/store-sub-agent') }}" enctype="multipart/form-data">
                    @csrf

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="name">代理商名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name"
                                    value="{{ old('name') }}" required>
                                @error('name')
                                <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="contact_person">联系人 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="contact_person" name="contact_person"
                                    value="{{ old('contact_person') }}" required>
                                @error('contact_person')
                                <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="phone">联系电话 <span class="text-danger">*</span></label>
                                <input type="tel" class="form-control" id="phone" name="phone"
                                    value="{{ old('phone') }}" required>
                                @error('phone')
                                <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="email">邮箱</label>
                                <input type="email" class="form-control" id="email" name="email"
                                    value="{{ old('email') }}">
                                @error('email')
                                <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="address">地址</label>
                                <textarea class="form-control" id="address" name="address" rows="3">{{ old('address') }}</textarea>
                                @error('address')
                                <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- 数量限制设置 -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="box box-warning">
                                <div class="box-header with-border">
                                    <h3 class="box-title">
                                        <i class="fa fa-sliders"></i> 数量限制设置
                                    </h3>
                                </div>
                                <div class="box-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="max_sub_agents">最大下级代理商数量</label>
                                                <input type="number" class="form-control" id="max_sub_agents"
                                                    name="max_sub_agents" value="{{ old('max_sub_agents', 0) }}"
                                                    min="0" max="100">
                                                <small class="text-muted">二级代理商通常设置为0，不能再发展下级</small>
                                                @error('max_sub_agents')
                                                <span class="text-danger">{{ $message }}</span>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="max_direct_stores">最大直推商铺数量 <span class="text-danger">*</span></label>
                                                <input type="number" class="form-control" id="max_direct_stores"
                                                    name="max_direct_stores" value="{{ old('max_direct_stores', 10) }}"
                                                    min="1" max="1000" required>
                                                <small class="text-muted">建议设置为10-50个</small>
                                                @error('max_direct_stores')
                                                <span class="text-danger">{{ $message }}</span>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 业务配置 -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="box box-info">
                                <div class="box-header with-border">
                                    <h3 class="box-title">
                                        <i class="fa fa-cogs"></i> 业务配置
                                    </h3>
                                </div>
                                <div class="box-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="commission_rate">返佣比例 (%)</label>
                                                <input type="number" class="form-control" id="commission_rate"
                                                    name="commission_rate" value="{{ old('commission_rate') }}"
                                                    min="0" max="100" step="0.1">
                                                <small class="text-muted">留空则使用区域配置的返佣比例</small>
                                                @error('commission_rate')
                                                <span class="text-danger">{{ $message }}</span>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="promotion_code">专属推广码</label>
                                                <input type="text" class="form-control" id="promotion_code"
                                                    name="promotion_code" value="{{ old('promotion_code') }}">
                                                <small class="text-muted">留空则自动生成</small>
                                                @error('promotion_code')
                                                <span class="text-danger">{{ $message }}</span>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="signed_at">签约日期</label>
                                                <input type="date" class="form-control" id="signed_at"
                                                    name="signed_at" value="{{ old('signed_at') }}">
                                                @error('signed_at')
                                                <span class="text-danger">{{ $message }}</span>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="status">状态</label>
                                                <select class="form-control" id="status" name="status">
                                                    <option value="active" {{ old('status', 'active') == 'active' ? 'selected' : '' }}>启用</option>
                                                    <option value="inactive" {{ old('status') == 'inactive' ? 'selected' : '' }}>禁用</option>
                                                    <option value="suspended" {{ old('status') == 'suspended' ? 'selected' : '' }}>暂停</option>
                                                </select>
                                                @error('status')
                                                <span class="text-danger">{{ $message }}</span>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 备注 -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="remark">备注</label>
                                <textarea class="form-control" id="remark" name="remark" rows="3">{{ old('remark') }}</textarea>
                                @error('remark')
                                <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fa fa-save"></i> 创建下级代理商
                                </button>
                                <a href="{{ admin_url('agents/' . $parentAgent->id . '/hierarchy-detail') }}"
                                    class="btn btn-default">
                                    <i class="fa fa-times"></i> 取消
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('script')
<script>
    $(document).ready(function() {
        // 表单验证
        $('form').on('submit', function(e) {
            var maxSubAgents = parseInt($('#max_sub_agents').val()) || 0;
            var maxDirectStores = parseInt($('#max_direct_stores').val()) || 0;

            if (maxDirectStores < 1) {
                e.preventDefault();
                toastr.error('最大直推商铺数量不能少于1个');
                return false;
            }

            // 检查上级代理商是否还能添加下级
            var parentCurrentSub = {
                {
                    $parentAgent - > current_sub_agents
                }
            };
            var parentMaxSub = {
                {
                    $parentAgent - > max_sub_agents
                }
            };

            if (parentCurrentSub >= parentMaxSub) {
                e.preventDefault();
                toastr.error('上级代理商已达到最大下级代理商数量限制');
                return false;
            }

            return true;
        });

        // 推广码自动生成
        $('#promotion_code').on('blur', function() {
            if ($(this).val() === '') {
                var name = $('#name').val();
                if (name) {
                    var code = 'AGENT' + name.substring(0, 2).toUpperCase() + Math.random().toString(36).substr(2, 6).toUpperCase();
                    $(this).val(code);
                }
            }
        });
    });
</script>
@endsection