<?php

namespace App\Admin\Controllers;

use App\Models\Area;
use Encore\Admin\Controllers\AdminController;
use Illuminate\Http\Request;

class AreaController extends AdminController
{
    /**
     * 获取地区数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function areas(Request $request)
    {
        $parentId = $request->get('q');
        return Area::where('pid', $parentId)->get(['id', 'name as text']);
    }
    
    /**
     * 根据区域代码获取区域信息
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAreaByCode(Request $request)
    {
        $code = $request->get('code');
        
        if (!$code) {
            return response()->json(['error' => '区域代码不能为空'], 400);
        }
        
        $area = Area::find($code);
        
        if (!$area) {
            return response()->json(['error' => '未找到该区域'], 404);
        }
        
        // 根据区域级别返回不同的数据结构
        $result = [
            'id' => $area->id,
            'name' => $area->name,
            'level' => $area->level,
            'code' => $area->citycode,
            'full_name' => $area->mername,
        ];
        
        // 如果是区县级，返回省市区完整结构
        if ($area->level == 3) {
            $city = Area::find($area->pid);
            $province = $city ? Area::find($city->pid) : null;
            
            $result['city'] = $city ? [
                'id' => $city->id,
                'name' => $city->name,
                'code' => $city->citycode,
            ] : null;
            
            $result['province'] = $province ? [
                'id' => $province->id,
                'name' => $province->name,
            ] : null;
        } 
        // 如果是市级，返回省市完整结构
        elseif ($area->level == 2) {
            $province = Area::find($area->pid);
            
            $result['province'] = $province ? [
                'id' => $province->id,
                'name' => $province->name,
            ] : null;
            
            // 获取该市下的所有区县
            $result['districts'] = Area::where('pid', $area->id)
                ->get(['id', 'name', 'citycode'])
                ->toArray();
        }
        // 如果是省级，返回该省下的所有城市
        elseif ($area->level == 1) {
            // 获取该省下的所有城市
            $result['cities'] = Area::where('pid', $area->id)
                ->get(['id', 'name', 'citycode'])
                ->toArray();
        }
        
        return response()->json($result);
    }
} 