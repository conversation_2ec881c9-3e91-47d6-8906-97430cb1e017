@extends('admin::index')

@section('content')
<div class="content-wrapper">
    <section class="content-header">
        <h1>
            商铺数据统计
            <small>商铺运营数据统计分析</small>
        </h1>
        <ol class="breadcrumb">
            <li><a href="{{ admin_url('/') }}"><i class="fa fa-dashboard"></i> 首页</a></li>
            <li><a href="{{ admin_url('merchant/list') }}">商铺管理</a></li>
            <li class="active">数据统计</li>
        </ol>
    </section>

    <section class="content">
        <!-- 统计概览 -->
        <div class="row">
            <div class="col-lg-3 col-xs-6">
                <div class="small-box bg-aqua">
                    <div class="inner">
                        <h3 id="total-stores">-</h3>
                        <p>总商铺数</p>
                    </div>
                    <div class="icon">
                        <i class="fa fa-building"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-xs-6">
                <div class="small-box bg-green">
                    <div class="inner">
                        <h3 id="active-stores">-</h3>
                        <p>营业中商铺</p>
                    </div>
                    <div class="icon">
                        <i class="fa fa-check-circle"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-xs-6">
                <div class="small-box bg-yellow">
                    <div class="inner">
                        <h3 id="pending-stores">-</h3>
                        <p>待审核商铺</p>
                    </div>
                    <div class="icon">
                        <i class="fa fa-clock-o"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-xs-6">
                <div class="small-box bg-red">
                    <div class="inner">
                        <h3 id="inactive-stores">-</h3>
                        <p>已关闭商铺</p>
                    </div>
                    <div class="icon">
                        <i class="fa fa-times-circle"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 筛选条件 -->
        <div class="row">
            <div class="col-md-12">
                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title">筛选条件</h3>
                        <div class="box-tools pull-right">
                            <button type="button" class="btn btn-box-tool" data-widget="collapse">
                                <i class="fa fa-minus"></i>
                            </button>
                        </div>
                    </div>
                    <div class="box-body">
                        <form id="statistics-filter-form">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>代理商</label>
                                        <select name="agent_id" id="agent-select" class="form-control">
                                            <option value="">全部代理商</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>省份</label>
                                        <select name="province_id" id="province-select" class="form-control">
                                            <option value="">全部省份</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>城市</label>
                                        <select name="city_id" id="city-select" class="form-control">
                                            <option value="">全部城市</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>商铺状态</label>
                                        <select name="status" id="status-select" class="form-control">
                                            <option value="">全部状态</option>
                                            <option value="active">营业中</option>
                                            <option value="inactive">已关闭</option>
                                            <option value="suspended">已暂停</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>创建时间范围</label>
                                        <div class="input-group">
                                            <input type="date" name="start_date" id="start-date" class="form-control">
                                            <span class="input-group-addon">至</span>
                                            <input type="date" name="end_date" id="end-date" class="form-control">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>&nbsp;</label>
                                        <div class="form-group">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fa fa-search"></i> 查询
                                            </button>
                                            <button type="button" id="reset-filter" class="btn btn-default">
                                                <i class="fa fa-refresh"></i> 重置
                                            </button>
                                            <button type="button" id="export-data" class="btn btn-success">
                                                <i class="fa fa-download"></i> 导出数据
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表展示 -->
        <div class="row">
            <!-- 商铺分布图 -->
            <div class="col-md-6">
                <div class="box box-info">
                    <div class="box-header with-border">
                        <h3 class="box-title">商铺地区分布</h3>
                    </div>
                    <div class="box-body">
                        <canvas id="region-chart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>

            <!-- 商铺状态分布 -->
            <div class="col-md-6">
                <div class="box box-warning">
                    <div class="box-header with-border">
                        <h3 class="box-title">商铺状态分布</h3>
                    </div>
                    <div class="box-body">
                        <canvas id="status-chart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- 商铺增长趋势 -->
        <div class="row">
            <div class="col-md-12">
                <div class="box box-success">
                    <div class="box-header with-border">
                        <h3 class="box-title">商铺增长趋势</h3>
                    </div>
                    <div class="box-body">
                        <canvas id="growth-chart" width="800" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- 详细数据表格 -->
        <div class="row">
            <div class="col-md-12">
                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title">详细数据</h3>
                    </div>
                    <div class="box-body">
                        <div class="table-responsive">
                            <table id="statistics-table" class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>代理商</th>
                                        <th>省份</th>
                                        <th>城市</th>
                                        <th>商铺总数</th>
                                        <th>营业中</th>
                                        <th>已关闭</th>
                                        <th>已暂停</th>
                                        <th>待审核</th>
                                        <th>本月新增</th>
                                    </tr>
                                </thead>
                                <tbody id="statistics-tbody">
                                    <tr>
                                        <td colspan="9" class="text-center">
                                            <i class="fa fa-spinner fa-spin"></i> 正在加载数据...
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<script src="{{ asset('js/chart.min.js') }}"></script>
<script src="{{ asset('js/merchant-statistics.js?v=' . time()) }}"></script>
@endsection