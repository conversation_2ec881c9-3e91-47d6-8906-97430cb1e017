<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\XiaohongshuController;
use App\Http\Controllers\Api\DouyinController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// 本地认证API
Route::prefix('auth')->group(function () {
    Route::post('/access-token', [AuthController::class, 'getAccessToken']);
    Route::post('/verify-token', [AuthController::class, 'verifyToken']);
});

// 小红书API代理
Route::prefix('xiaohongshu')->group(function () {
    Route::post('/access-token', [XiaohongshuController::class, 'getAccessToken']);
    Route::post('/proxy', [XiaohongshuController::class, 'proxyRequest']);
    Route::get('/test', [XiaohongshuController::class, 'testConnection']);
});

// 抖音API代理
Route::prefix('douyin')->group(function () {
    Route::post('/client-token', [DouyinController::class, 'getClientToken']);
});

// 地区相关路由（无需认证的省市区联动接口）
Route::prefix('areas')->group(function () {
    Route::get('provinces', 'App\Http\Controllers\Api\AreaApiController@provinces');
    Route::get('cities', 'App\Http\Controllers\Api\AreaApiController@cities');
    Route::get('districts', 'App\Http\Controllers\Api\AreaApiController@districts');
    Route::get('info', 'App\Http\Controllers\Api\AreaApiController@info');
});

// 原有地区路由（保持兼容性）
Route::prefix('areas')->group(base_path('routes/area.php'));

// 代理商区域配置API接口（无需认证）
Route::group(['prefix' => 'agent-regions'], function () {
    Route::get('list', 'App\Http\Controllers\Api\AgentRegionApiController@list');
    Route::get('areas', 'App\Http\Controllers\Api\AgentRegionApiController@areas');
    Route::get('statistics', 'App\Http\Controllers\Api\AgentRegionApiController@statistics');
});

// 代理商API接口（基于新架构，无需认证）
Route::group(['prefix' => 'agents'], function () {
    Route::get('', 'App\Http\Controllers\Api\AgentApiController@index');
    Route::get('{id}', 'App\Http\Controllers\Api\AgentApiController@show');
    Route::get('{id}/regions', 'App\Http\Controllers\Api\AgentApiController@regions');
    Route::get('statistics/overview', 'App\Http\Controllers\Api\AgentApiController@statistics');
});
