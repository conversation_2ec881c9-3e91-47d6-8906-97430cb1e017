<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

/**
 * 抖音API客户端控制器
 */
class DouyinController extends Controller
{
    /**
     * 抖音API基础URL
     */
    private $baseUrl = 'https://open.douyin.com';

    /**
     * 应用凭据
     */
    private $clientKey = 'aw1fufi86b0ef0tq';  // 请替换为实际的client_key
    private $clientSecret = 'a393ed92791a4b5b0d26ec14e6334802'; // 请替换为实际的client_secret

    /**
     * 获取抖音客户端访问令牌和ticket
     *
     * @api {post} /api/douyin/client-token 获取抖音客户端访问令牌和ticket
     * @apiName GetDouyinClientToken
     * @apiGroup 抖音API
     * @apiVersion 1.0.0
     * @apiDescription 通过应用凭据获取抖音API客户端访问令牌，并自动获取ticket，支持缓存机制避免频繁请求
     *
     * @apiHeader {String} Content-Type application/json
     * @apiHeader {String} Accept application/json
     *
     * @apiSuccess {Number} code 响应状态码，200表示成功
     * @apiSuccess {String} message 响应消息
     * @apiSuccess {Object} data 响应数据
     * @apiSuccess {Object} data.request_params 请求抖音接口的参数
     * @apiSuccess {String} data.request_params.client_key 应用密钥
     * @apiSuccess {String} data.request_params.client_secret 应用密码
     * @apiSuccess {String} data.request_params.grant_type 授权类型，固定值client_credential
     * @apiSuccess {Object} data.douyin_response 抖音API原始响应
     * @apiSuccess {Object} data.token_info 解析后的token信息
     * @apiSuccess {String} data.token_info.access_token 访问令牌
     * @apiSuccess {Number} data.token_info.expires_in 令牌有效期（秒）
     * @apiSuccess {String} data.token_info.token_type 令牌类型
     * @apiSuccess {String} data.token_info.scope 授权范围
     * @apiSuccess {Object} data.ticket_info ticket获取结果
     * @apiSuccess {Boolean} data.ticket_info.success ticket获取是否成功
     * @apiSuccess {Object} data.ticket_info.ticket_response 抖音ticket接口原始响应
     * @apiSuccess {String} data.ticket_info.ticket_response.ticket ticket值
     * @apiSuccess {Number} data.ticket_info.ticket_response.expires_in ticket有效期
     * @apiSuccess {String} data.ticket_info.request_url ticket请求URL
     * @apiSuccess {Object} data.ticket_info.request_headers ticket请求头信息
     * @apiSuccess {Boolean} data.from_cache token是否来自缓存
     * @apiSuccess {Number} timestamp 响应时间戳
     *
     * @apiSuccessExample {json} 成功响应示例:
     * HTTP/1.1 200 OK
     * {
     *   "code": 200,
     *   "message": "获取抖音client_token和ticket成功",
     *   "data": {
     *     "request_params": {
     *       "client_key": "aw1fufi86b0ef0tq",
     *       "client_secret": "a393ed92791a4b5b0d26ec14e6334802",
     *       "grant_type": "client_credential"
     *     },
     *     "douyin_response": {
     *       "access_token": "act.xxxxxxxxxxxxxxxxxxxxx",
     *       "expires_in": 7200,
     *       "token_type": "Bearer",
     *       "scope": "user_info"
     *     },
     *     "token_info": {
     *       "access_token": "act.xxxxxxxxxxxxxxxxxxxxx",
     *       "expires_in": 7200,
     *       "token_type": "Bearer",
     *       "scope": "user_info"
     *     },
     *     "ticket_info": {
     *       "success": true,
     *       "ticket_response": {
     *         "ticket": "bkt.xxxxxxxxxxxxxxxxxxxxx",
     *         "expires_in": 7200
     *       },
     *       "request_url": "https://open.douyin.com/open/getticket/",
     *       "request_headers": {
     *         "Content-Type": "application/json",
     *         "access-token": "act.xxxxxx..."
     *       }
     *     },
     *     "from_cache": false
     *   },
     *   "timestamp": 1752130495
     * }
     *
     * @apiError {Number} code 错误状态码
     * @apiError {String} message 错误消息
     * @apiError {Number} timestamp 响应时间戳
     *
     * @apiErrorExample {json} 错误响应示例:
     * HTTP/1.1 400 Bad Request
     * {
     *   "code": 400,
     *   "message": "抖音API调用失败：400",
     *   "timestamp": 1752130495
     * }
     *
     * @apiNote 该接口实现了智能缓存机制，token会根据expires_in自动缓存，ticket每次实时获取
     * @apiNote 无需签名算法，直接使用client_key和client_secret进行认证
     * @apiNote 缓存键格式：douyin_client_token:{client_key}
     * @apiNote ticket获取失败不影响token的正常返回，会在ticket_info中标识失败状态
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getClientToken(Request $request)
    {
        try {
            // 准备请求数据
            $requestData = [
                'client_key' => $this->clientKey,
                'client_secret' => $this->clientSecret,
                'grant_type' => 'client_credential'
            ];

            // 检查缓存
            $cacheKey = "douyin_client_token:{$this->clientKey}";
            $cachedToken = Cache::get($cacheKey);

            if ($cachedToken) {
                Log::info('返回缓存的抖音client_token');

                // 即使是缓存，也返回完整的数据结构
                $result = [
                    'request_params' => $requestData,  // 当前生成的请求参数
                    'douyin_response' => $cachedToken,  // 缓存的响应
                    'token_info' => $cachedToken,  // token信息
                    'from_cache' => true  // 标识来自缓存
                ];

                // 获取ticket（即使token来自缓存，也重新获取ticket）
                $accessToken = $cachedToken['access_token'];
                $ticketResult = $this->getTicket($accessToken);
                $result['ticket_info'] = $ticketResult;

                return $this->successResponse($result, '获取抖音client_token成功（缓存）');
            }

            // 调用抖音API
            $douyinUrl = $this->baseUrl . '/oauth/client_token/';

            // 发送HTTP请求到抖音API
            $response = Http::timeout(30)
                ->withHeaders([
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                    'User-Agent' => 'Laravel-Client/1.0'
                ])
                ->post($douyinUrl, $requestData);

            // 检查响应状态
            if (!$response->successful()) {
                Log::error('抖音API调用失败', [
                    'status' => $response->status(),
                    'body' => $response->body(),
                    'url' => $douyinUrl,
                    'request_data' => $requestData
                ]);
                return $this->errorResponse('抖音API调用失败：' . $response->status(), $response->status());
            }

            $responseData = $response->json();

            // 准备返回数据，包含请求参数和响应数据
            $result = [
                'request_params' => $requestData,  // 请求抖音接口的参数
                'douyin_response' => $responseData,  // 抖音API的原始响应
                'from_cache' => false  // 标识来自新请求
            ];

            // 如果抖音返回了token，缓存它并获取ticket
            if (
                isset($responseData['access_token']) ||
                (isset($responseData['data']) && isset($responseData['data']['access_token']))
            ) {
                $tokenData = isset($responseData['data']) ? $responseData['data'] : $responseData;

                // 缓存token
                $expiresIn = $tokenData['expires_in'] ?? 7200;
                Cache::put($cacheKey, $tokenData, $expiresIn);

                // 在结果中添加解析后的token信息
                $result['token_info'] = $tokenData;

                // 获取ticket
                $accessToken = $tokenData['access_token'];
                $ticketResult = $this->getTicket($accessToken);
                $result['ticket_info'] = $ticketResult;

                Log::info('抖音client_token获取成功', [
                    'expires_in' => $expiresIn,
                    'token_type' => $tokenData['token_type'] ?? 'unknown',
                    'ticket_success' => isset($ticketResult['success']) ? $ticketResult['success'] : false
                ]);

                return $this->successResponse($result, '获取抖音client_token和ticket成功');
            }

            // 直接返回包含请求参数和响应的数据
            return $this->successResponse($result, '抖音API响应');
        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->errorResponse('参数验证失败: ' . implode(', ', $e->validator->errors()->all()), 422);
        } catch (\Exception $e) {
            Log::error('获取抖音client_token失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse('服务器内部错误', 500);
        }
    }

    /**
     * 获取抖音ticket
     *
     * @apiPrivate
     * @apiDescription 内部方法：使用access_token获取抖音ticket，用于后续API调用认证
     *
     * @param string $accessToken 抖音访问令牌
     * @return array 返回ticket获取结果
     * @return array.success boolean ticket获取是否成功
     * @return array.ticket_response object|null 抖音ticket接口原始响应（成功时）
     * @return array.request_url string ticket请求的URL
     * @return array.request_headers object ticket请求的头部信息
     * @return array.error string|null 错误信息（失败时）
     * @return array.status_code number|null HTTP状态码（失败时）
     *
     * @apiNote 该方法为私有方法，仅在getClientToken中调用
     * @apiNote 使用GET请求方式，将access_token放在请求头的access-token字段
     * @apiNote 请求失败时返回详细的错误信息，不抛出异常
     * @apiNote 为安全考虑，日志中只记录access_token的前10位字符
     */
    private function getTicket($accessToken)
    {
        try {
            // 调用抖音获取ticket接口
            $ticketUrl = $this->baseUrl . '/open/getticket/';

            // 发送HTTP请求到抖音ticket接口
            $response = Http::timeout(30)
                ->withHeaders([
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                    'access-token' => $accessToken,
                    'User-Agent' => 'Laravel-Client/1.0'
                ])
                ->get($ticketUrl);

            // 检查响应状态
            if (!$response->successful()) {
                Log::error('抖音ticket接口调用失败', [
                    'status' => $response->status(),
                    'body' => $response->body(),
                    'url' => $ticketUrl,
                    'access_token' => substr($accessToken, 0, 10) . '...' // 只记录部分token用于调试
                ]);

                return [
                    'success' => false,
                    'error' => '抖音ticket接口调用失败：' . $response->status(),
                    'status_code' => $response->status()
                ];
            }

            $ticketData = $response->json();

            Log::info('抖音ticket获取成功', [
                'response_keys' => array_keys($ticketData ?? [])
            ]);

            return [
                'success' => true,
                'ticket_response' => $ticketData,
                'request_url' => $ticketUrl,
                'request_headers' => [
                    'Content-Type' => 'application/json',
                    'access-token' => substr($accessToken, 0, 10) . '...'
                ]
            ];
        } catch (\Exception $e) {
            Log::error('获取抖音ticket失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => '获取ticket时发生异常：' . $e->getMessage()
            ];
        }
    }

    /**
     * 统一成功响应格式
     *
     * @apiPrivate
     * @apiDescription 内部方法：生成统一格式的成功响应
     *
     * @param mixed $data 响应数据
     * @param string $message 响应消息，默认为"操作成功"
     * @return \Illuminate\Http\JsonResponse JSON响应对象
     *
     * @apiNote 响应格式：{code: 200, message: string, data: mixed, timestamp: number}
     * @apiNote HTTP状态码固定为200
     */
    private function successResponse($data, $message = '操作成功')
    {
        return response()->json([
            'code' => 200,
            'message' => $message,
            'data' => $data,
            'timestamp' => time()
        ]);
    }

    /**
     * 统一错误响应格式
     *
     * @apiPrivate
     * @apiDescription 内部方法：生成统一格式的错误响应
     *
     * @param string $message 错误消息
     * @param int $code 错误状态码，默认为400
     * @return \Illuminate\Http\JsonResponse JSON响应对象
     *
     * @apiNote 响应格式：{code: number, message: string, timestamp: number}
     * @apiNote HTTP状态码与响应体中的code保持一致
     */
    private function errorResponse($message, $code = 400)
    {
        return response()->json([
            'code' => $code,
            'message' => $message,
            'timestamp' => time()
        ], $code);
    }
}
