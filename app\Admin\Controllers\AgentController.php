<?php

namespace App\Admin\Controllers;

use App\Models\Agent;
use App\Services\PermissionService;
use App\Models\AgentRegion;
use App\Models\Area;
use App\Models\Store;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;
use Encore\Admin\Layout\Content;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Encore\Admin\Admin;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * 代理商管理控制器
 * 
 * 负责代理商的CRUD操作和数据展示
 * 
 * <AUTHOR>
 */
class AgentController extends AdminController
{
    /**
     * 页面标题
     *
     * @var string
     */
    protected $title = '代理商管理';

    /**
     * 列表页面
     *
     * @param Content $content
     * @return Content
     */
    public function index(Content $content)
    {
        // 检查权限
        if (!PermissionService::hasPermission('agent.list')) {
            abort(403, '您没有权限访问代理商列表');
        }

        return $content
            ->header('代理商列表')
            ->description('查看和管理所有代理商')
            ->body($this->grid());
    }

    /**
     * 详情页面
     *
     * @param mixed $id
     * @param Content $content
     * @return Content
     */
    public function show($id, Content $content)
    {
        // 检查权限
        if (!PermissionService::hasPermission('agent.show')) {
            abort(403, '您没有权限查看代理商详情');
        }

        return $content
            ->header('代理商详情')
            ->description('查看代理商详细信息')
            ->body($this->detail($id));
    }

    /**
     * 创建页面
     *
     * @param Content $content
     * @return Content
     */
    public function create(Content $content)
    {
        // 检查权限
        if (!PermissionService::hasPermission('agent.create')) {
            abort(403, '您没有权限创建代理商');
        }

        return $content
            ->header('新增代理商')
            ->description('创建新的代理商')
            ->body($this->form());
    }

    /**
     * 编辑页面
     *
     * @param mixed $id
     * @param Content $content
     * @return Content
     */
    public function edit($id, Content $content)
    {
        // 检查权限
        if (!PermissionService::hasPermission('agent.edit')) {
            abort(403, '您没有权限编辑代理商');
        }

        return $content
            ->header('编辑代理商')
            ->description('修改代理商信息')
            ->body($this->form()->edit($id));
    }

    /**
     * 自定义存储方法，用于创建代理商
     */
    public function store()
    {
        // 检查权限
        if (!PermissionService::hasPermission('agent.create')) {
            abort(403, '您没有权限创建代理商');
        }

        return $this->saveAgent(request());
    }

    /**
     * 自定义更新方法，用于更新代理商
     */
    public function update($id)
    {
        // 检查权限
        if (!PermissionService::hasPermission('agent.edit')) {
            abort(403, '您没有权限编辑代理商');
        }

        return $this->saveAgent(request(), $id);
    }

    /**
     * 保存代理商数据
     */
    protected function saveAgent(Request $request, $id = null)
    {
        // 获取表单数据
        $data = $request->all();
        // 处理地区信息
        if (!empty($data['district_id'])) {
            $area = Area::find($data['district_id']);
            if ($area) {
                $data['region_code'] = $area->id;
                $data['region_name'] = $area->mername;
            }
        } elseif (!empty($data['city_id'])) {
            $area = Area::find($data['city_id']);
            if ($area) {
                $data['region_code'] = $area->id;
                $data['region_name'] = $area->mername;
            }
        } elseif (!empty($data['province_id'])) {
            $area = Area::find($data['province_id']);
            if ($area) {
                $data['region_code'] = $area->id;
                $data['region_name'] = $area->mername;
            }
        }

        // 确保结算周期和结算日只能有一个有值
        if (!empty($data['settlement_days'])) {
            // 如果设置了结算日，则清空结算周期
            $data['settlement_cycle'] = null;

            // 使用结算日计算下次结算日期
            $data['next_settlement_at'] = \Carbon\Carbon::now()->addDays($data['settlement_days'])->format('Y-m-d');
        } elseif (!empty($data['settlement_cycle'])) {
            // 如果设置了结算周期，则清空结算日
            $data['settlement_days'] = null;

            // 使用结算周期和签约日期计算下次结算日期
            if (!empty($data['signed_at']) && empty($data['next_settlement_at'])) {
                $signedAt = \Carbon\Carbon::parse($data['signed_at']);
                $nextSettlementAt = null;

                switch ($data['settlement_cycle']) {
                    case 'weekly':
                        // 每周：下一个与签约日期同一天的日期
                        $nextSettlementAt = $signedAt->copy()->addWeek();
                        break;

                    case 'monthly':
                        // 每月：下一个与签约日期同一天的日期
                        $nextSettlementAt = $signedAt->copy()->addMonth();
                        break;

                    case 'quarterly':
                        // 每季度：下一个与签约日期同一天的日期，但间隔3个月
                        $nextSettlementAt = $signedAt->copy()->addMonths(3);
                        break;
                }

                if ($nextSettlementAt) {
                    $data['next_settlement_at'] = $nextSettlementAt->format('Y-m-d');
                }
            }
        } else {
            // 如果两者都为空，则清空下次结算日期
            $data['next_settlement_at'] = null;
        }

        // 处理合同文件上传
        if ($request->hasFile('contract_file') && $request->file('contract_file')->isValid()) {
            $file = $request->file('contract_file');
            $path = $file->store('contracts', 'admin');
            $data['contract_file'] = $path;
        } else {
            // 如果没有上传新文件，在编辑模式下保持原有文件
            if ($id) {
                $existingAgent = Agent::find($id);
                if ($existingAgent && $existingAgent->contract_file) {
                    $data['contract_file'] = $existingAgent->contract_file;
                }
            }
        }

        // 如果没有设置推广码，自动生成（仅在创建时）
        if (empty($data['promotion_code']) && !$id) {
            $data['promotion_code'] = 'AGENT' . strtoupper(substr(md5(uniqid()), 0, 8));
        } elseif (empty($data['promotion_code']) && $id) {
            // 编辑时如果推广码为空，保持原值
            $existingAgent = Agent::find($id);
            if ($existingAgent && $existingAgent->promotion_code) {
                $data['promotion_code'] = $existingAgent->promotion_code;
            } else {
                // 如果原来也没有推广码，则生成一个
                $data['promotion_code'] = 'AGENT' . strtoupper(substr(md5(uniqid()), 0, 8));
            }
        }

        // 处理图片字段的null值问题
        if (isset($data['qr_code_url']) && $data['qr_code_url'] === null) {
            unset($data['qr_code_url']);
        }

        if (isset($data['contract_file']) && $data['contract_file'] === null) {
            unset($data['contract_file']);
        }

        // 移除临时字段
        unset($data['province_id']);
        unset($data['city_id']);
        unset($data['district_id']);
        unset($data['_token']);
        unset($data['_method']);
        unset($data['_previous_']);
        unset($data['settlement_method']);

        try {
            // 记录最终要保存的数据
            Log::info('准备保存的数据', [
                'agent_id' => $id,
                'data_keys' => array_keys($data),
                'file_fields' => [
                    'contract_file' => $data['contract_file'] ?? 'NULL',
                    'qr_code_url' => $data['qr_code_url'] ?? 'NULL'
                ]
            ]);

            if ($id) {
                // 更新
                $agent = Agent::findOrFail($id);
                $agent->update($data);
                admin_toastr('代理商信息更新成功', 'success');
            } else {
                // 创建
                $agent = Agent::create($data);
                admin_toastr('代理商创建成功', 'success');
            }

            return redirect(admin_url('agents'));
        } catch (\Exception $e) {
            Log::error('保存代理商数据失败', [
                'error_message' => $e->getMessage(),
                'error_trace' => $e->getTraceAsString(),
                'data' => $data
            ]);

            admin_toastr('操作失败: ' . addslashes($e->getMessage()), 'error');
            return back()->withInput();
        }
    }

    /**
     * 列表页面配置
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new Agent());

        // 🔥 第一步：获取当前用户权限
        $canView = PermissionService::hasPermission('agent.show');
        $canEdit = PermissionService::hasPermission('agent.edit');
        $canDelete = PermissionService::hasPermission('agent.delete');
        $canCreate = PermissionService::hasPermission('agent.create');
        $canManageHierarchy = PermissionService::hasPermission('agent.hierarchy');
        $canManageRegion = PermissionService::hasPermission('agent.region');

        // 🔥 根据用户角色过滤代理商数据
        $grid->model()->whereIn('id', PermissionService::getAccessibleAgentIds());

        $grid->column('id', 'ID')->sortable();
        $grid->column('name', '代理商名称');
        $grid->column('contact_person', '联系人');
        $grid->column('phone', '联系电话');
        $grid->column('email', '邮箱');

        // 🆕 层级管理信息
        $grid->column('level', '等级')->display(function ($level) {
            $labels = [
                1 => '<span class="label label-primary">一级代理商</span>',
                2 => '<span class="label label-info">二级代理商</span>'
            ];
            return $labels[$level] ?? '<span class="label label-default">未设置</span>';
        });

        $grid->column('parent_agent_id', '上级代理商')->display(function ($parentId) {
            if ($parentId) {
                $parent = Agent::find($parentId);
                return $parent ? "<small>{$parent->name}</small>" : '<span class="text-muted">已删除</span>';
            }
            return '<span class="text-muted">-</span>';
        });

        $grid->column('hierarchy_stats', '层级统计')->display(function () {
            $subAgents = $this->current_sub_agents ?? 0;
            $maxSubAgents = $this->max_sub_agents ?? 0;
            $directStores = $this->current_direct_stores ?? 0;
            $maxDirectStores = $this->max_direct_stores ?? 0;

            $html = "<div style='font-size: 12px;'>";
            $html .= "<div><strong>下级代理:</strong> {$subAgents}/{$maxSubAgents}</div>";
            $html .= "<div><strong>直推商铺:</strong> {$directStores}/{$maxDirectStores}</div>";
            $html .= "</div>";

            return $html;
        });
        $grid->column('region_info', '负责区域')->display(function () {
            /** @var Agent $agent */
            $agent = $this;
            $regions = AgentRegion::where('agent_id', $agent->id)->get();

            if ($regions->isEmpty()) {
                return $agent->region_name ?: '<span class="text-muted">未配置</span>';
            }

            $regionCount = $regions->count();
            $firstRegion = $regions->first();
            $exclusiveText = $firstRegion->is_exclusive ? '[独占]' : '[共享]';

            if ($regionCount === 1) {
                return $firstRegion->full_region . ' ' . $exclusiveText;
            } else {
                return $firstRegion->full_region . ' ' . $exclusiveText . " <small class=\"text-info\">等{$regionCount}个区域</small>";
            }
        });
        $grid->column('commission_rate', '返佣比例')->display(function ($value) {
            return $value ? $value . '%' : '<span class="text-muted">未设置</span>';
        });
        $grid->column('settlement_cycle', '结算周期')->display(function ($value) {
            $cycles = [
                'weekly' => '每周',
                'monthly' => '每月',
                'quarterly' => '每季度'
            ];
            return $cycles[$value] ?? $value;
        });
        $grid->column('signed_at', '签约日期')->display(function ($value) {
            return $value ?: '<span class="text-muted">未设置</span>';
        });
        $grid->column('next_settlement_at', '下次结算日期')->display(function ($value) {
            return $value ?: '<span class="text-muted">未设置</span>';
        });
        $grid->column('contract_file', '合同文件')->display(function ($value) {
            return $value ? '<a href="' . asset('storage/admin/' . $value) . '" target="_blank"><i class="fa fa-file-pdf-o"></i> 查看合同</a>' : '';
        });
        $grid->column('status', '状态')->display(function ($value) {
            $statusMap = [
                'active' => '<span class="label label-success">启用</span>',
                'inactive' => '<span class="label label-default">禁用</span>',
                'suspended' => '<span class="label label-danger">暂停</span>'
            ];
            return $statusMap[$value] ?? $value;
        });
        $grid->column('created_at', '创建时间')->sortable();

        // 搜索过滤
        $grid->filter(function ($filter) {
            $filter->like('name', '代理商名称');
            $filter->like('contact_person', '联系人');
            $filter->like('phone', '联系电话');
            $filter->like('region_name', '主要区域');

            // 🆕 层级管理筛选
            $filter->equal('level', '代理商等级')->select(Agent::getLevelOptions());
            $filter->equal('parent_agent_id', '上级代理商')->select(Agent::pluck('name', 'id'));

            // 按区域配置搜索
            $filter->where(function ($query) {
                $input = request('region_search');
                if ($input) {
                    $query->whereHas('agentRegions', function ($q) use ($input) {
                        $q->where('province', 'like', "%{$input}%")
                            ->orWhere('city', 'like', "%{$input}%")
                            ->orWhere('district', 'like', "%{$input}%");
                    });
                }
            }, '配置区域', 'region_search');

            $filter->equal('status', '状态')->select([
                'active' => '启用',
                'inactive' => '禁用',
                'suspended' => '暂停'
            ]);

            // 区域配置状态筛选
            $filter->where(function ($query) {
                $query->whereHas('agentRegions');
            }, '有区域配置', 'has_regions')->checkbox(['1' => '是']);

            // 🆕 层级管理状态筛选
            $filter->where(function ($query) {
                $query->whereNull('parent_agent_id');
            }, '顶级代理商', 'top_level')->checkbox(['1' => '是']);

            $filter->where(function ($query) {
                $query->whereNotNull('parent_agent_id');
            }, '有上级代理商', 'has_parent')->checkbox(['1' => '是']);

            $filter->between('signed_at', '签约日期')->date();
            $filter->between('next_settlement_at', '下次结算日期')->date();
            $filter->between('created_at', '创建时间')->datetime();
        });

        // 操作按钮 - 根据权限动态显示
        $grid->actions(function ($actions) use ($canView, $canEdit, $canDelete, $canManageHierarchy, $canManageRegion, $canCreate) {
            /** @var Agent $agent */
            $agent = $actions->row;

            // 🔒 权限控制：配置区域按钮
            if ($canManageRegion) {
                $actions->append('<a href="' . admin_url('agent-regions/create?agent_id=' . $agent->id) . '" class="btn btn-xs btn-info">配置区域</a>');
            }

            // 🔒 权限控制：查看区域按钮
            if ($canView) {
                $regionCount = AgentRegion::where('agent_id', $agent->id)->count();
                if ($regionCount > 0) {
                    $actions->append('<a href="' . admin_url('agent-regions?agent_id=' . $agent->id) . '" class="btn btn-xs btn-warning">查看区域(' . $regionCount . ')</a>');
                }
            }

            // 🔒 权限控制：层级详情按钮
            if ($canManageHierarchy) {
                $actions->append('<a href="' . admin_url('agents/' . $agent->id . '/hierarchy-detail') . '" class="btn btn-xs btn-primary"><i class="fa fa-sitemap"></i> 层级详情</a>');
            }

            // 🔒 权限控制：添加下级代理商按钮 - 只有平台管理员才能创建
            if ($canCreate && $agent->level == Agent::LEVEL_PRIMARY && $agent->canAddSubAgent()) {
                $actions->append('<a href="' . admin_url('agents/' . $agent->id . '/create-sub-agent') . '" class="btn btn-xs btn-success"><i class="fa fa-plus"></i> 添加下级</a>');
            }

            // 🔒 权限控制：编辑和删除按钮 - 只有平台管理员才能编辑删除
            if (!$canEdit) {
                $actions->disableEdit();
            }
            if (!$canDelete) {
                $actions->disableDelete();
            }
        });

        // 🔒 权限控制：工具栏
        $grid->tools(function ($tools) use ($canCreate, $canDelete, $canManageHierarchy) {
            $tools->batch(function ($batch) use ($canDelete) {
                // 根据权限控制批量删除
                if (!$canDelete) {
                    $batch->disableDelete();
                } else {
                    $batch->disableDelete(); // 代理商数据重要，禁用批量删除
                }
            });

            // 🔒 权限控制：层级管理入口
            if ($canManageHierarchy) {
                $tools->append('<a href="' . admin_url('agents/hierarchy') . '" class="btn btn-sm btn-info"><i class="fa fa-sitemap"></i> 层级管理</a>');
            }
        });

        // 🔒 权限控制：根据权限显示/隐藏创建按钮 - 只有平台管理员才能创建
        if (!$canCreate) {
            $grid->disableCreateButton();
        }

        return $grid;
    }

    /**
     * 详情页面配置
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(Agent::findOrFail($id));

        $show->field('id', 'ID');
        $show->field('name', '代理商名称');
        $show->field('contact_person', '联系人');
        $show->field('phone', '联系电话');
        $show->field('email', '邮箱');
        $show->field('region_name', '主要区域');

        // 🆕 层级管理信息
        $show->divider('层级管理信息');

        $show->field('level', '代理商等级')->as(function ($level) {
            $labels = [
                1 => '一级代理商',
                2 => '二级代理商'
            ];
            return $labels[$level] ?? '未设置';
        });

        $show->field('parent_agent_id', '上级代理商')->as(function ($parentId) {
            if ($parentId) {
                $parent = Agent::find($parentId);
                return $parent ? $parent->name : '已删除';
            }
            return '无（顶级代理商）';
        });

        $show->field('hierarchy_path', '层级路径')->as(function () {
            $agent = $this;
            $path = $agent->getHierarchyPath();
            $pathTexts = [];

            foreach ($path as $pathAgent) {
                $pathTexts[] = $pathAgent['level_label'] . ': ' . $pathAgent['name'];
            }

            return implode(' → ', $pathTexts);
        });

        $show->field('sub_agents_count', '下级代理商数量')->as(function () {
            $agent = $this;
            $current = $agent->current_sub_agents ?? 0;
            $max = $agent->max_sub_agents ?? 0;
            $percentage = $max > 0 ? round(($current / $max) * 100, 1) : 0;

            return "{$current}/{$max} ({$percentage}%)";
        });

        $show->field('direct_stores_count', '直推商铺数量')->as(function () {
            $agent = $this;
            $current = $agent->current_direct_stores ?? 0;
            $max = $agent->max_direct_stores ?? 0;
            $percentage = $max > 0 ? round(($current / $max) * 100, 1) : 0;

            return "{$current}/{$max} ({$percentage}%)";
        });

        $show->field('sub_agents_list', '下级代理商列表')->as(function () {
            $agent = $this;
            $subAgents = $agent->subAgents()->get();

            if ($subAgents->isEmpty()) {
                return '暂无下级代理商';
            }

            $html = '<div class="table-responsive"><table class="table table-bordered table-sm">';
            $html .= '<thead><tr><th>姓名</th><th>电话</th><th>状态</th><th>直推商铺</th></tr></thead>';
            $html .= '<tbody>';

            foreach ($subAgents as $subAgent) {
                $status = $subAgent->status == 'active' ? '正常' : '停用';
                $stores = $subAgent->current_direct_stores . '/' . $subAgent->max_direct_stores;

                $html .= "<tr>";
                $html .= "<td>{$subAgent->name}</td>";
                $html .= "<td>{$subAgent->phone}</td>";
                $html .= "<td>{$status}</td>";
                $html .= "<td>{$stores}</td>";
                $html .= "</tr>";
            }

            $html .= '</tbody></table></div>';

            return $html;
        });

        $show->divider('业务信息');

        // 显示所有关联的区域配置
        $show->field('all_regions', '所有负责区域')->as(function ($value) {
            /** @var Agent $agent */
            $agent = $this;
            $regions = AgentRegion::where('agent_id', $agent->id)->get();

            if ($regions->isEmpty()) {
                return '暂未配置区域';
            }

            $regionTexts = [];
            foreach ($regions as $region) {
                $exclusiveText = $region->is_exclusive ? '[独占]' : '[共享]';
                $regionTexts[] = $region->full_region . ' ' . $exclusiveText;
            }

            return implode('<br>', $regionTexts);
        });

        $show->field('address', '地址');
        $show->field('commission_rate', '返佣比例')->as(function ($value) {
            return $value ? $value . '%' : '未设置（使用区域配置）';
        });
        $show->field('settlement_cycle', '结算周期')->as(function ($value) {
            $cycles = [
                'weekly' => '每周',
                'monthly' => '每月',
                'quarterly' => '每季度'
            ];
            return $cycles[$value] ?? $value;
        });
        $show->field('settlement_days', '结算天数')->as(function ($value) {
            return $value ? "{$value}天" : '';
        });
        $show->field('signed_at', '签约日期')->as(function ($value) {
            return $value ?: '未设置（在区域配置中单独设置）';
        });
        $show->field('next_settlement_at', '下次结算日期')->as(function ($value) {
            return $value ?: '未设置';
        });
        $show->field('promotion_code', '专属推广码')->as(function ($value) {
            return $value ?: '未设置';
        });
        $show->field('qr_code_url', '二维码链接')->as(function ($value) {
            if ($value) {
                return "<img src='" . asset("storage/$value") . "' style='max-width: 200px; max-height: 200px;' />";
            }
            return '暂无二维码';
        });

        $show->field('contract_file', '合同文件')->as(function ($value) {
            if ($value) {
                return "<a href='" . asset("storage/$value") . "' target='_blank'><i class='fa fa-download'></i> 下载合同</a>";
            }
            return '暂无合同文件';
        });
        $show->field('status', '状态')->as(function ($value) {
            $statusMap = [
                'active' => '启用',
                'inactive' => '禁用',
                'suspended' => '暂停'
            ];
            return $statusMap[$value] ?? $value;
        });
        $show->field('remark', '备注');
        $show->field('created_at', '创建时间');
        $show->field('updated_at', '更新时间');

        return $show;
    }

    /**
     * 表单配置
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new Agent());

        $form->text('name', '代理商名称')->required()->rules('required|max:100');
        $form->text('contact_person', '联系人')->required()->rules('required|max:50');
        $form->mobile('phone', '联系电话')->required()->rules('required|max:20');
        $form->email('email', '邮箱')->rules('nullable|email|max:100');

        // 预先获取编辑数据以构建地区help信息
        $regionHelpText = '';
        $settlementHelpText = '';
        $editAgent = null;

        // 直接从URL判断是否为编辑模式并获取ID
        $currentPath = request()->getPathInfo();
        $isEditMode = false;
        $finalEditId = null;

        // 检查URL模式：/admin/agents/{id}/edit
        if (preg_match('/\/admin\/agents\/(\d+)\/edit/', $currentPath, $matches)) {
            $isEditMode = true;
            $finalEditId = $matches[1];
        }

        Log::info('表单模式检查', [
            'current_path' => $currentPath,
            'is_edit_mode' => $isEditMode,
            'extracted_id' => $finalEditId,
            'form_isEditing' => $form->isEditing()
        ]);

        if ($isEditMode && $finalEditId) {
            $editAgent = Agent::find($finalEditId);
            Log::info('查询代理商数据', [
                'final_edit_id' => $finalEditId,
                'agent_found' => $editAgent ? true : false,
                'region_code' => $editAgent ? $editAgent->region_code : null
            ]);

            if ($editAgent && $editAgent->region_code) {
                $currentArea = Area::find($editAgent->region_code);
                Log::info('查询地区数据', [
                    'area_found' => $currentArea ? true : false,
                    'area_data' => $currentArea ? $currentArea->toArray() : null
                ]);

                if ($currentArea) {
                    $regionParts = [];

                    if ($currentArea->level == 3) {
                        // 区县级，需要查找市和省
                        $regionParts[] = $currentArea->mername;
                        $city = Area::find($currentArea->pid);
                        if ($city) {
                            $regionParts[] = $city->mername;
                            $province = Area::find($city->pid);
                            if ($province) {
                                $regionParts[] = $province->mername;
                            }
                        }
                        $regionParts = array_reverse($regionParts);
                    } elseif ($currentArea->level == 2) {
                        // 城市级，需要查找省
                        $regionParts[] = $currentArea->mername;
                        $province = Area::find($currentArea->pid);
                        if ($province) {
                            $regionParts[] = $province->mername;
                        }
                        $regionParts = array_reverse($regionParts);
                    } elseif ($currentArea->level == 1) {
                        // 省份级
                        $regionParts[] = $currentArea->mername;
                    }

                    if (!empty($regionParts)) {
                        $regionHelpText = '🏠 当前存储的地区：' . implode(' → ', $regionParts) . ' (ID: ' . $editAgent->region_code . ')';
                    }

                    Log::info('构建地区帮助文本', [
                        'region_parts' => $regionParts,
                        'help_text' => $regionHelpText
                    ]);
                } else {
                    $regionHelpText = '⚠️ 当前region_code (' . $editAgent->region_code . ') 在数据库中未找到对应地区信息';
                }
            } else {
                // 如果没有region_code，也显示一个提示
                if ($editAgent) {
                    $regionHelpText = '⚠️ 当前代理商未设置地区信息';
                }
            }

            // 构建结算方式help信息
            if ($editAgent) {
                $settlementParts = [];
                if ($editAgent->settlement_cycle) {
                    $cycles = ['weekly' => '每周', 'monthly' => '每月', 'quarterly' => '每季度'];
                    $settlementParts[] = '结算周期：' . ($cycles[$editAgent->settlement_cycle] ?? $editAgent->settlement_cycle);
                }
                if ($editAgent->settlement_days) {
                    $settlementParts[] = '结算天数：' . $editAgent->settlement_days . '天';
                }
                if (!empty($settlementParts)) {
                    $settlementHelpText = '💰 当前结算方式：' . implode('，', $settlementParts);
                } else {
                    $settlementHelpText = '⚠️ 当前未设置结算方式';
                }
            }
        }

        Log::info('最终帮助文本', [
            'region_help' => $regionHelpText,
            'settlement_help' => $settlementHelpText
        ]);

        // 添加地区选择 - 简化版本，移除作用域问题
        $provinceSelect = $form->select('province_id', '所属省份')
            ->options(function ($id) {
                return Area::where('level', 1)->pluck('name', 'id');
            })
            ->placeholder('搜索或选择省份...')
            ->config('allowClear', true)
            ->load('city_id', '/admin/api/areas');

        if ($regionHelpText) {
            $provinceSelect->help($regionHelpText);
        } else {
            $provinceSelect->help('选择代理商所在的省份 [调试：regionHelpText为空，isEditMode=' . ($isEditMode ? 'true' : 'false') . ', finalEditId=' . ($finalEditId ?? 'null') . ']');
        }

        // 构建城市和区县help信息（重用之前查询的数据）
        $cityHelpText = '';
        $districtHelpText = '';

        if ($isEditMode && $finalEditId && isset($editAgent) && $editAgent && $editAgent->region_code) {
            $currentArea = Area::find($editAgent->region_code);
            if ($currentArea) {
                if ($currentArea->level == 3) {
                    // 区县级别
                    $districtHelpText = '🏘️ 当前存储的区县：' . $currentArea->mername . ' (ID: ' . $currentArea->id . ')';

                    // 查找所属城市
                    $city = Area::find($currentArea->pid);
                    if ($city) {
                        $cityHelpText = '🏙️ 当前存储的城市：' . $city->mername . ' (ID: ' . $city->id . ')';
                    }
                } elseif ($currentArea->level == 2) {
                    // 城市级别
                    $cityHelpText = '🏙️ 当前存储的城市：' . $currentArea->mername . ' (ID: ' . $currentArea->id . ')';
                }
            }
        }

        $citySelect = $form->select('city_id', '所属城市')
            ->options(function ($id) {
                if (!$id) return [];
                return Area::where('pid', $id)->pluck('name', 'id');
            })
            ->placeholder('请先选择省份后再选择城市')
            ->config('allowClear', true)
            ->load('district_id', '/admin/api/areas');

        if ($cityHelpText) {
            $citySelect->help($cityHelpText);
        } else {
            $citySelect->help('选择代理商所在的城市');
        }

        $districtSelect = $form->select('district_id', '所属区县')
            ->options(function ($id) {
                if (!$id) return [];
                return Area::where('pid', $id)->pluck('name', 'id');
            })
            ->placeholder('请先选择城市后再选择区县')
            ->config('allowClear', true);

        if ($districtHelpText) {
            $districtSelect->help($districtHelpText);
        } else {
            $districtSelect->help('选择代理商所在的区县');
        }

        // 将区域代码设置为隐藏字段
        $form->hidden('region_code', '区域代码');
        $form->hidden('region_name', '区域名称');

        $form->textarea('address', '地址')->rules('nullable|max:500');
        $form->decimal('commission_rate', '返佣比例(%)')->default(0)->rules('nullable|numeric|min:0|max:100')->help('可选填写，如不填写则使用区域配置中的返佣比例');

        // 预先获取编辑数据，确定默认值
        $agent = null;
        $defaultSettlementMethod = null;

        if ($form->isEditing()) {
            $id = request()->route()->parameter('agent');
            $agent = Agent::find($id);

            // 日志调试：记录获取到的数据
            Log::info('编辑模式获取数据', [
                'agent_id' => $id,
                'agent_data' => $agent ? $agent->toArray() : null,
                'region_code' => $agent ? $agent->region_code : null,
                'settlement_cycle' => $agent ? $agent->settlement_cycle : null,
                'settlement_days' => $agent ? $agent->settlement_days : null
            ]);
            if ($agent) {
                // 根据用户需求设置默认值逻辑
                if (empty($agent->settlement_cycle) && !empty($agent->settlement_days)) {
                    $defaultSettlementMethod = 'days';
                } elseif (empty($agent->settlement_days) && !empty($agent->settlement_cycle)) {
                    $defaultSettlementMethod = 'cycle';
                } else {
                    // 两个都有值或都为空时，默认选择days
                    $defaultSettlementMethod = 'days';
                }
            }
        }

        // 结算方式选择
        $form->divider('结算方式（结算周期和结算日二选一）');

        $settlementMethodRadio = $form->radio('settlement_method', '结算方式')
            ->options([
                'cycle' => '按周期结算',
                'days' => '按天数结算'
            ]);

        if ($settlementHelpText) {
            $settlementMethodRadio->help($settlementHelpText);
        }

        // 默认值将通过JavaScript设置，这里不设置PHP默认值

        $settlementMethodRadio
            ->when('cycle', function (Form $form) {
                // 结算周期选项
                $form->select('settlement_cycle', '结算周期')->options([
                    'weekly' => '每周',
                    'monthly' => '每月',
                    'quarterly' => '每季度'
                ])->default('monthly');
            })
            ->when('days', function (Form $form) {
                // 结算天数选项
                $form->number('settlement_days', '结算天数')
                    ->min(1)
                    ->max(365)
                    ->help('距离下一次结算的天数');
            });

        // 添加签约日期字段
        $form->date('signed_at', '签约日期')->rules('nullable|date')->help('可选填写，如不填写则在区域配置中单独设置');

        // 下次结算日期字段
        $form->date('next_settlement_at', '下次结算日期')->help('如不填写，系统将根据签约日期和结算方式自动计算');

        // 添加合同文件上传
        $form->file('contract_file', '合同文件')
            ->disk('admin')
            ->move('contracts')
            ->rules('nullable|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:10240')
            ->help('支持PDF、Word文档和图片格式，最大10MB');

        $form->text('promotion_code', '专属推广码')->rules('nullable|max:50|unique:agents,promotion_code,{{id}}')->help('可选填写，如不填写系统将自动生成');
        $form->image('qr_code_url', '二维码')->disk('admin')->uniqueName()->rules('nullable|image|max:2048');
        $form->select('status', '状态')->options([
            'active' => '启用',
            'inactive' => '禁用',
            'suspended' => '暂停'
        ])->default('active')->required();

        // 🆕 层级管理字段
        $form->divider('层级管理配置');

        $form->select('parent_agent_id', '上级代理商')
            ->options(Agent::where('level', Agent::LEVEL_PRIMARY)
                ->where('status', 'active')
                ->pluck('name', 'id'))
            ->placeholder('选择上级代理商（留空为一级代理商）')
            ->help('如果选择了上级代理商，则当前代理商将成为二级代理商');

        $form->select('level', '代理商等级')
            ->options(Agent::getLevelOptions())
            ->default(Agent::LEVEL_PRIMARY)
            ->help('一级代理商可以管理二级代理商和商铺，二级代理商只能管理商铺');

        $form->number('max_sub_agents', '最大下级代理数')
            ->default(10)
            ->min(0)
            ->help('一级代理商可以管理的最大下级代理商数量，二级代理商请设置为0');

        $form->number('max_direct_stores', '最大直推商铺数')
            ->default(100)
            ->min(1)
            ->help('代理商可以直接推广的最大商铺数量');

        $form->number('current_sub_agents', '当前下级代理数')
            ->default(0)
            ->readonly()
            ->help('系统自动统计，无需手动修改');

        $form->number('current_direct_stores', '当前直推商铺数')
            ->default(0)
            ->readonly()
            ->help('系统自动统计，无需手动修改');

        $form->textarea('remark', '备注')->rules('nullable|max:1000');

        // 处理编辑模式的其他数据设置
        if ($form->isEditing() && $agent) {

            // 设置地区信息 - 通过region_code反向查找省市区并设置到Agent模型
            if ($agent->region_code) {
                $currentArea = Area::find($agent->region_code);
                if ($currentArea) {
                    // 根据区域级别设置对应的ID到Agent模型中
                    if ($currentArea->level == 3) {
                        // 区县级别，需要查找省市
                        $agent->district_id = $currentArea->id;
                        $cityArea = Area::find($currentArea->pid);
                        if ($cityArea) {
                            $agent->city_id = $cityArea->id;
                            $provinceArea = Area::find($cityArea->pid);
                            if ($provinceArea) {
                                $agent->province_id = $provinceArea->id;
                            }
                        }
                    } elseif ($currentArea->level == 2) {
                        // 城市级别，需要查找省份
                        $agent->city_id = $currentArea->id;
                        $provinceArea = Area::find($currentArea->pid);
                        if ($provinceArea) {
                            $agent->province_id = $provinceArea->id;
                        }
                    } elseif ($currentArea->level == 1) {
                        // 省份级别
                        $agent->province_id = $currentArea->id;
                    }
                }
            }

            // 如果是编辑模式且没有下次结算日期，但有签约日期和结算周期，则自动计算
            if ($agent->signed_at && $agent->settlement_cycle && !$agent->next_settlement_at && !$agent->settlement_days) {
                $signedAt = \Carbon\Carbon::parse($agent->signed_at);
                $nextSettlementAt = null;

                switch ($agent->settlement_cycle) {
                    case 'weekly':
                        $nextSettlementAt = $signedAt->copy()->addWeek();
                        break;
                    case 'monthly':
                        $nextSettlementAt = $signedAt->copy()->addMonth();
                        break;
                    case 'quarterly':
                        $nextSettlementAt = $signedAt->copy()->addMonths(3);
                        break;
                }

                if ($nextSettlementAt) {
                    $agent->next_settlement_at = $nextSettlementAt->format('Y-m-d');
                    $agent->save();
                }
            }

            // 如果有地区代码，设置隐藏字段的值
            if ($agent->region_code) {
                $form->region_code = $agent->region_code;
            }
        }

        // 表单提交前的处理
        $form->submitted(function (Form $form) {
            // 根据结算方式设置相应的字段
            if ($form->settlement_method == 'cycle') {
                $form->settlement_days = null;
            } else {
                $form->settlement_cycle = null;
            }

            return $form;
        });

        // 在编辑模式下设置表单的初始值
        if ($form->isEditing() && $agent) {
            // 使用saving事件来处理数据，而不是在这里设置
            $form->saving(function (Form $form) use ($agent) {
                // 这里可以添加保存前的处理逻辑
            });
        }

        // 添加JavaScript来处理编辑时的初始化
        if ($form->isEditing() && $agent) {
            Admin::script('
                $(document).ready(function() {
                    console.log("开始初始化编辑数据...");
                    
                    // 设置结算方式
                    var settlementCycle = "' . ($agent->settlement_cycle ?? '') . '";
                    var settlementDays = "' . ($agent->settlement_days ?? '') . '";
                    
                    console.log("结算数据:", {cycle: settlementCycle, days: settlementDays});
                    
                    if (!settlementCycle && settlementDays) {
                        console.log("设置按天数结算");
                        $(\'input[name="settlement_method"][value="days"]\').prop(\'checked\', true).trigger(\'change\');
                        
                        // 延迟设置天数值，确保when条件生效后再设置
                        setTimeout(function() {
                            $(\'input[name="settlement_days"]\').val("' . ($agent->settlement_days ?? '') . '");
                            console.log("设置settlement_days值:", "' . ($agent->settlement_days ?? '') . '");
                        }, 200);
                    } else if (settlementCycle && !settlementDays) {
                        console.log("设置按周期结算");
                        $(\'input[name="settlement_method"][value="cycle"]\').prop(\'checked\', true).trigger(\'change\');
                        
                        // 延迟设置周期值，确保when条件生效后再设置
                        setTimeout(function() {
                            $(\'select[name="settlement_cycle"]\').val("' . ($agent->settlement_cycle ?? '') . '");
                            console.log("设置settlement_cycle值:", "' . ($agent->settlement_cycle ?? '') . '");
                        }, 200);
                    } else {
                        // 默认情况：如果两个都有值或都为空，根据业务逻辑优先选择days
                        console.log("设置默认按天数结算");
                        $(\'input[name="settlement_method"][value="days"]\').prop(\'checked\', true).trigger(\'change\');
                        
                        if (settlementDays) {
                            setTimeout(function() {
                                $(\'input[name="settlement_days"]\').val("' . ($agent->settlement_days ?? '') . '");
                                console.log("默认情况设置settlement_days值:", "' . ($agent->settlement_days ?? '') . '");
                            }, 200);
                        }
                    }
                    
                    // 添加表单提交前的检查
                    $(\'form\').on(\'submit\', function() {
                        var selectedMethod = $(\'input[name="settlement_method"]:checked\').val();
                        console.log("表单提交时的结算方式:", selectedMethod);
                        
                        if (selectedMethod === \'days\') {
                            var daysValue = $(\'input[name="settlement_days"]\').val();
                            console.log("提交的settlement_days值:", daysValue);
                        } else if (selectedMethod === \'cycle\') {
                            var cycleValue = $(\'select[name="settlement_cycle"]\').val();
                            console.log("提交的settlement_cycle值:", cycleValue);
                        }
                    });
                    
                    // 设置地区联动
                    var regionCode = "' . ($agent->region_code ?? '') . '";
                    if (regionCode) {
                        console.log("设置地区信息, region_code:", regionCode);
                        
                        // 根据计算出的ID设置地区
                        var provinceId = "' . ($agent->province_id ?? '') . '";
                        var cityId = "' . ($agent->city_id ?? '') . '";
                        var districtId = "' . ($agent->district_id ?? '') . '";
                        
                        console.log("地区ID:", {province: provinceId, city: cityId, district: districtId});
                        
                        if (provinceId) {
                            $(\'select[name="province_id"]\').val(provinceId).trigger(\'change\');
                            
                            setTimeout(function() {
                                if (cityId) {
                                    $(\'select[name="city_id"]\').val(cityId).trigger(\'change\');
                                    
                                    setTimeout(function() {
                                        if (districtId) {
                                            $(\'select[name="district_id"]\').val(districtId).trigger(\'change\');
                                        }
                                    }, 300);
                                }
                            }, 300);
                        }
                    }
                });
            ');
        }

        return $form;
    }

    // ==================== 🆕 层级管理功能 ====================

    /**
     * 层级管理页面
     *
     * @param Content $content
     * @return Content
     */
    public function hierarchy(Content $content)
    {
        // 检查权限
        if (!PermissionService::hasPermission('agent.hierarchy')) {
            abort(403, '您没有权限访问代理商层级管理');
        }

        return $content
            ->header('代理商层级管理')
            ->description('查看和管理代理商层级关系')
            ->body($this->hierarchyGrid());
    }

    /**
     * 层级管理数据表格
     */
    protected function hierarchyGrid()
    {
        $grid = new Grid(new Agent());

        // 🔥 根据用户角色过滤代理商数据
        $grid->model()->whereIn('id', PermissionService::getAccessibleAgentIds());

        $grid->column('id', 'ID')->sortable();
        $grid->column('name', '代理商名称')->display(function ($name) {
            return "<strong>{$name}</strong>";
        });

        $grid->column('level', '等级')->display(function ($level) {
            $labels = [
                1 => '<span class="label label-primary">一级代理商</span>',
                2 => '<span class="label label-info">二级代理商</span>'
            ];
            return $labels[$level] ?? '<span class="label label-default">未设置</span>';
        });

        $grid->column('parent_agent_id', '上级代理商')->display(function ($parentId) {
            if ($parentId) {
                $parent = Agent::find($parentId);
                return $parent ? $parent->name : '已删除';
            }
            return '<span class="text-muted">无</span>';
        });

        $grid->column('sub_agents_info', '下级代理商')->display(function () {
            $current = $this->current_sub_agents ?? 0;
            $max = $this->max_sub_agents ?? 0;
            $percentage = $max > 0 ? round(($current / $max) * 100, 1) : 0;

            $color = $percentage >= 80 ? 'danger' : ($percentage >= 60 ? 'warning' : 'success');

            return "
                <div>
                    <small class='text-muted'>当前/最大</small><br>
                    <span class='label label-{$color}'>{$current}/{$max}</span>
                    <small class='text-muted'>({$percentage}%)</small>
                </div>
            ";
        });

        $grid->column('direct_stores_info', '直推商铺')->display(function () {
            $current = $this->current_direct_stores ?? 0;
            $max = $this->max_direct_stores ?? 0;
            $percentage = $max > 0 ? round(($current / $max) * 100, 1) : 0;

            $color = $percentage >= 80 ? 'danger' : ($percentage >= 60 ? 'warning' : 'success');

            return "
                <div>
                    <small class='text-muted'>当前/最大</small><br>
                    <span class='label label-{$color}'>{$current}/{$max}</span>
                    <small class='text-muted'>({$percentage}%)</small>
                </div>
            ";
        });

        $grid->column('status', '状态')->display(function ($status) {
            $labels = [
                'active' => '<span class="label label-success">正常</span>',
                'inactive' => '<span class="label label-default">停用</span>',
                'suspended' => '<span class="label label-warning">暂停</span>'
            ];
            return $labels[$status] ?? '<span class="label label-default">未知</span>';
        });

        // 操作列
        $grid->actions(function ($actions) {
            $actions->disableView();
            $actions->disableEdit();
            $actions->disableDelete();

            // 添加层级管理操作
            $actions->append('<a href="' . admin_url('agents/' . $actions->getKey() . '/hierarchy-detail') . '" class="btn btn-xs btn-info"><i class="fa fa-sitemap"></i> 层级详情</a>');

            // 如果是一级代理商，添加创建下级代理商按钮
            if ($actions->row->level == Agent::LEVEL_PRIMARY && $actions->row->canAddSubAgent()) {
                $actions->append('<a href="' . admin_url('agents/' . $actions->getKey() . '/create-sub-agent') . '" class="btn btn-xs btn-success"><i class="fa fa-plus"></i> 添加下级</a>');
            }

            // 添加编辑限制按钮
            $actions->append('<a href="' . admin_url('agents/' . $actions->getKey() . '/edit-limits') . '" class="btn btn-xs btn-warning"><i class="fa fa-edit"></i> 编辑限制</a>');
        });

        $grid->filter(function ($filter) {
            $filter->equal('level', '等级')->select(Agent::getLevelOptions());
            $filter->equal('parent_agent_id', '上级代理商')->select(Agent::pluck('name', 'id'));
            $filter->equal('status', '状态')->select(Agent::getStatusOptions());
        });

        $grid->disableCreateButton();
        $grid->disableExport();
        $grid->disableBatchActions();

        return $grid;
    }

    /**
     * 层级详情页面
     *
     * @param mixed $id
     * @param Content $content
     * @return Content
     */
    public function hierarchyDetail($id, Content $content)
    {
        // 检查权限
        if (!PermissionService::hasPermission('agent.hierarchy')) {
            abort(403, '您没有权限查看代理商层级详情');
        }

        // 查找代理商
        $agent = Agent::findOrFail($id);

        return $content
            ->header('代理商层级详情')
            ->description('查看代理商层级结构')
            ->body($this->buildHierarchyDetail($agent));
    }

    /**
     * 构建层级详情视图
     */
    protected function buildHierarchyDetail($agent)
    {
        $stats = $agent->getHierarchyStats();
        $hierarchyPath = $agent->getHierarchyPath();
        $subAgents = $agent->subAgents()->with('subAgents')->get();

        $html = "
        <div class='row'>
            <div class='col-md-12'>
                <div class='box box-primary'>
                    <div class='box-header with-border'>
                        <h3 class='box-title'><i class='fa fa-sitemap'></i> 层级路径</h3>
                    </div>
                    <div class='box-body'>
                        <div class='hierarchy-path' style='padding: 15px;'>
        ";

        foreach ($hierarchyPath as $index => $pathAgent) {
            if ($index > 0) {
                $html .= " <i class='fa fa-angle-right text-muted'></i> ";
            }

            $isCurrent = $pathAgent['id'] == $agent->id;
            $class = $isCurrent ? 'label-primary' : 'label-default';

            $html .= "<span class='label {$class}'>{$pathAgent['level_label']}: {$pathAgent['name']}</span>";
        }

        $html .= "
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class='row'>
            <div class='col-md-6'>
                <div class='box box-info'>
                    <div class='box-header with-border'>
                        <h3 class='box-title'><i class='fa fa-users'></i> 下级代理商统计</h3>
                    </div>
                    <div class='box-body'>
                        <div class='progress-group'>
                            <span class='progress-text'>当前数量</span>
                            <span class='float-right'><b>{$stats['sub_agents']['current']}</b>/{$stats['sub_agents']['max']}</span>
                            <div class='progress progress-sm'>
                                <div class='progress-bar progress-bar-primary' style='width: {$stats['sub_agents']['percentage']}%'></div>
                            </div>
                        </div>
                        <p class='text-muted'>
                            <i class='fa fa-info-circle'></i> 
                            还可以添加 <strong>{$stats['sub_agents']['available']}</strong> 个下级代理商
                        </p>
                    </div>
                </div>
            </div>
            
            <div class='col-md-6'>
                <div class='box box-success'>
                    <div class='box-header with-border'>
                        <h3 class='box-title'><i class='fa fa-store'></i> 直推商铺统计</h3>
                    </div>
                    <div class='box-body'>
                        <div class='progress-group'>
                            <span class='progress-text'>当前数量</span>
                            <span class='float-right'><b>{$stats['direct_stores']['current']}</b>/{$stats['direct_stores']['max']}</span>
                            <div class='progress progress-sm'>
                                <div class='progress-bar progress-bar-success' style='width: {$stats['direct_stores']['percentage']}%'></div>
                            </div>
                        </div>
                        <p class='text-muted'>
                            <i class='fa fa-info-circle'></i> 
                            还可以添加 <strong>{$stats['direct_stores']['available']}</strong> 个直推商铺
                        </p>
                    </div>
                </div>
            </div>
        </div>
        ";

        // 如果有下级代理商，显示下级代理商列表
        if ($subAgents->isNotEmpty()) {
            $html .= "
            <div class='row'>
                <div class='col-md-12'>
                    <div class='box box-warning'>
                        <div class='box-header with-border'>
                            <h3 class='box-title'><i class='fa fa-users'></i> 下级代理商列表</h3>
                        </div>
                        <div class='box-body'>
                            <div class='table-responsive'>
                                <table class='table table-bordered table-hover'>
                                    <thead>
                                        <tr>
                                            <th>代理商名称</th>
                                            <th>联系方式</th>
                                            <th>状态</th>
                                            <th>直推商铺</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
            ";

            foreach ($subAgents as $subAgent) {
                $statusLabel = $subAgent->status == 'active' ?
                    '<span class="label label-success">正常</span>' :
                    '<span class="label label-default">停用</span>';

                $html .= "
                    <tr>
                        <td><strong>{$subAgent->name}</strong></td>
                        <td>{$subAgent->phone}</td>
                        <td>{$statusLabel}</td>
                        <td>{$subAgent->current_direct_stores}/{$subAgent->max_direct_stores}</td>
                        <td>
                            <a href='" . admin_url('agents/' . $subAgent->id . '/hierarchy-detail') . "' class='btn btn-xs btn-info'>
                                <i class='fa fa-eye'></i> 查看
                            </a>
                            <a href='" . admin_url('agents/' . $subAgent->id . '/edit') . "' class='btn btn-xs btn-warning'>
                                <i class='fa fa-edit'></i> 编辑
                            </a>
                        </td>
                    </tr>
                ";
            }

            $html .= "
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            ";
        }

        return $html;
    }

    /**
     * 创建下级代理商页面
     *
     * @param mixed $id
     * @param Content $content
     * @return Content
     */
    public function createSubAgent($id, Content $content)
    {
        // 检查权限
        if (!PermissionService::hasPermission('agent.create')) {
            abort(403, '您没有权限创建下级代理商');
        }

        $parentAgent = Agent::findOrFail($id);
        return $content
            ->header('创建下级代理商')
            ->description("为代理商 [{$parentAgent->name}] 创建下级代理商")
            ->body($this->subAgentForm($parentAgent));
    }

    /**
     * 下级代理商表单
     */
    protected function subAgentForm($parentAgent)
    {
        $form = new Form(new Agent());

        // 显示上级代理商信息
        $form->display('parent_info', '上级代理商')->with(function () use ($parentAgent) {
            return "<strong>{$parentAgent->name}</strong> (ID: {$parentAgent->id})";
        });

        // 基本信息
        $form->text('name', '代理商姓名')->rules('required|max:100');
        $form->text('contact_person', '联系人')->rules('required|max:100');
        $form->mobile('phone', '联系电话')->rules('required|regex:/^1[3-9]\d{9}$/');
        $form->email('email', '邮箱地址')->rules('nullable|email|max:100');
        $form->text('id_card', '身份证号')->rules('nullable|regex:/^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/');
        $form->textarea('address', '通讯地址')->rules('nullable|max:255');

        // 层级管理字段（自动设置）
        $form->hidden('parent_agent_id')->value($parentAgent->id);
        $form->hidden('level')->value(Agent::LEVEL_SECONDARY);
        $form->hidden('current_sub_agents')->value(0);
        $form->hidden('current_direct_stores')->value(0);

        // 限制配置
        $form->number('max_sub_agents', '最大下级代理数')->default(0)->min(0)->help('二级代理商不能有下级代理商');
        $form->number('max_direct_stores', '最大直推商铺数')->default(50)->min(1)->max(200)->help('建议设置为50-200个');

        // 其他字段
        $form->select('status', '状态')->options(Agent::getStatusOptions())->default('active');
        $form->textarea('remark', '备注')->rules('nullable|max:1000');

        // 表单提交处理
        $form->saving(function (Form $form) use ($parentAgent) {
            // 再次检查上级代理商是否可以添加下级
            if (!$parentAgent->canAddSubAgent()) {
                throw new \InvalidArgumentException('上级代理商已达到最大下级代理商数量限制');
            }

            // 确保二级代理商不能有下级代理商
            $form->max_sub_agents = 0;
        });

        $form->saved(function (Form $form, $result) use ($parentAgent) {
            // 更新上级代理商的下级数量
            $parentAgent->updateSubAgentCount();

            admin_success('成功', '下级代理商创建成功');
            return redirect(admin_url('agents/' . $parentAgent->id . '/hierarchy-detail'));
        });

        return $form;
    }

    /**
     * 编辑代理商配额页面
     *
     * @param mixed $id
     * @param Content $content
     * @return Content
     */
    public function editLimits($id, Content $content)
    {
        // 检查权限  
        if (!PermissionService::hasPermission('agent.edit')) {
            abort(403, '您没有权限编辑代理商配额');
        }

        $agent = Agent::findOrFail($id);
        return $content
            ->header('编辑代理商配额')
            ->description("编辑代理商 [{$agent->name}] 的配额限制")
            ->body($this->limitsForm($agent));
    }

    /**
     * 限制编辑表单
     */
    protected function limitsForm($agent)
    {
        $form = new Form($agent);

        // 显示当前状态
        $form->display('current_status', '当前状态')->with(function () use ($agent) {
            $stats = $agent->getHierarchyStats();
            return "
                <div class='row'>
                    <div class='col-md-6'>
                        <strong>下级代理商：</strong> {$stats['sub_agents']['current']}/{$stats['sub_agents']['max']} ({$stats['sub_agents']['percentage']}%)
                    </div>
                    <div class='col-md-6'>
                        <strong>直推商铺：</strong> {$stats['direct_stores']['current']}/{$stats['direct_stores']['max']} ({$stats['direct_stores']['percentage']}%)
                    </div>
                </div>
            ";
        });

        $form->divider();

        // 限制设置
        if ($agent->level == Agent::LEVEL_PRIMARY) {
            $form->number('max_sub_agents', '最大下级代理数')
                ->min($agent->current_sub_agents)
                ->help("当前已有 {$agent->current_sub_agents} 个下级代理商，不能设置小于此数量");
        } else {
            $form->display('max_sub_agents', '最大下级代理数')->with(function () {
                return '<span class="text-muted">二级代理商不能有下级代理商</span>';
            });
        }

        $form->number('max_direct_stores', '最大直推商铺数')
            ->min($agent->current_direct_stores)
            ->help("当前已有 {$agent->current_direct_stores} 个直推商铺，不能设置小于此数量");

        $form->saved(function (Form $form, $result) use ($agent) {
            admin_success('成功', '数量限制更新成功');
            return redirect(admin_url('agents/' . $agent->id . '/hierarchy-detail'));
        });

        return $form;
    }

    /**
     * 更新代理商统计数据
     */
    public function updateCounts($id)
    {
        // 检查权限
        if (!PermissionService::hasPermission('agent.edit')) {
            abort(403, '您没有权限更新代理商统计数据');
        }

        try {
            $agent = Agent::findOrFail($id);

            // 更新下级代理商数量
            $agent->sub_agent_count = Agent::where('parent_agent_id', $id)->count();

            // 更新商铺数量
            $agent->store_count = Store::where('agent_id', $id)->count();

            $agent->save();

            return response()->json([
                'status' => true,
                'message' => '统计数据更新成功',
                'data' => [
                    'sub_agent_count' => $agent->sub_agent_count,
                    'store_count' => $agent->store_count
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => '更新失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 存储下级代理商
     */
    public function storeSubAgent($id, Request $request)
    {
        // 检查权限
        if (!PermissionService::hasPermission('agent.create')) {
            abort(403, '您没有权限创建下级代理商');
        }

        return $this->saveAgent($request, null, $id);
    }

    /**
     * 更新代理商配额
     */
    public function updateLimits($id, Request $request)
    {
        // 检查权限
        if (!PermissionService::hasPermission('agent.edit')) {
            abort(403, '您没有权限更新代理商配额');
        }

        try {
            $agent = Agent::findOrFail($id);
            $agent->update([
                'sub_agent_limit' => $request->input('sub_agent_limit'),
                'store_limit' => $request->input('store_limit'),
            ]);

            admin_toastr('配额更新成功', 'success');
            return redirect(admin_url('agents/hierarchy'));
        } catch (\Exception $e) {
            admin_toastr('配额更新失败：' . $e->getMessage(), 'error');
            return back();
        }
    }

    /**
     * 删除代理商
     */
    public function destroy($id)
    {
        // 检查权限
        if (!PermissionService::hasPermission('agent.delete')) {
            abort(403, '您没有权限删除代理商');
        }

        try {
            $agent = Agent::findOrFail($id);

            // 检查是否有下级代理商
            if ($agent->children()->count() > 0) {
                return response()->json([
                    'status' => false,
                    'message' => '该代理商还有下级代理商，无法删除'
                ]);
            }

            // 检查是否有关联的商铺
            if ($agent->stores()->count() > 0) {
                return response()->json([
                    'status' => false,
                    'message' => '该代理商还有关联的商铺，无法删除'
                ]);
            }

            $agent->delete();

            return response()->json([
                'status' => true,
                'message' => '代理商删除成功'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => '删除失败：' . $e->getMessage()
            ]);
        }
    }
}
