<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('stores', function (Blueprint $table) {
            // 添加商铺商品金额字段
            $table->decimal('product_amount', 10, 2)->default(0.00)->comment('商铺商品金额/价格');

            // 添加结算状态字段
            $table->tinyInteger('is_settled')->default(0)->comment('是否已结算（0未结算/1已结算）');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('stores', function (Blueprint $table) {
            // 删除添加的字段
            $table->dropColumn(['product_amount', 'is_settled']);
        });
    }
};
