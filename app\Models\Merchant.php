<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * 商铺模型
 * 
 * @property int $id
 * @property string $name 商铺名称
 * @property string $contact_person 联系人
 * @property string $contact_phone 联系电话
 * @property string $address 商铺地址
 * @property float|null $latitude 纬度
 * @property float|null $longitude 经度
 * @property int|null $agent_id 关联代理商ID
 * @property string|null $opening_time 营业开始时间
 * @property string|null $closing_time 营业结束时间
 * @property string|null $nfc_chip_id NFC芯片ID
 * @property string $status 状态
 * @property bool $can_promote 是否允许推广
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 */
class Merchant extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'contact_person',
        'contact_phone',
        'address',
        'latitude',
        'longitude',
        'agent_id',
        'opening_time',
        'closing_time',
        'nfc_chip_id',
        'status',
        'can_promote',
    ];

    protected $casts = [
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'can_promote' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 状态常量
     */
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';
    const STATUS_PENDING = 'pending';
    const STATUS_REJECTED = 'rejected';

    /**
     * 获取状态选项
     */
    public static function getStatusOptions()
    {
        return [
            self::STATUS_ACTIVE => '营业中',
            self::STATUS_INACTIVE => '已关闭',
            self::STATUS_PENDING => '待审核',
            self::STATUS_REJECTED => '已驳回',
        ];
    }

    /**
     * 获取状态标签
     */
    public function getStatusLabelAttribute()
    {
        return self::getStatusOptions()[$this->status] ?? $this->status;
    }

    /**
     * 获取状态颜色
     */
    public function getStatusColorAttribute()
    {
        $colors = [
            self::STATUS_ACTIVE => 'success',
            self::STATUS_INACTIVE => 'danger',
            self::STATUS_PENDING => 'warning',
            self::STATUS_REJECTED => 'danger',
        ];
        return $colors[$this->status] ?? 'default';
    }

    /**
     * 关联代理商
     */
    public function agent()
    {
        return $this->belongsTo(Agent::class);
    }

    /**
     * 作用域：正常状态
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    /**
     * 作用域：按状态筛选
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 作用域：按代理商筛选
     */
    public function scopeByAgent($query, $agentId)
    {
        return $query->where('agent_id', $agentId);
    }

    /**
     * 作用域：允许推广
     */
    public function scopeCanPromote($query)
    {
        return $query->where('can_promote', true);
    }

    /**
     * 获取营业时间显示
     */
    public function getBusinessHoursAttribute()
    {
        if ($this->opening_time && $this->closing_time) {
            return $this->opening_time . ' - ' . $this->closing_time;
        }
        return '未设置';
    }

    /**
     * 获取代理商名称
     */
    public function getAgentNameAttribute()
    {
        return $this->agent ? $this->agent->name : '无';
    }

    /**
     * 获取完整地址（包含坐标）
     */
    public function getFullAddressAttribute()
    {
        $address = $this->address;
        if ($this->latitude && $this->longitude) {
            $address .= ' (' . $this->latitude . ', ' . $this->longitude . ')';
        }
        return $address;
    }

    /**
     * 检查是否在营业时间内
     */
    public function isOpenNow()
    {
        if (!$this->opening_time || !$this->closing_time) {
            return null; // 未设置营业时间
        }

        $now = now()->format('H:i:s');
        return $now >= $this->opening_time && $now <= $this->closing_time;
    }

    /**
     * 获取营业状态
     */
    public function getBusinessStatusAttribute()
    {
        if ($this->status !== self::STATUS_ACTIVE) {
            return $this->status_label;
        }

        $isOpen = $this->isOpenNow();
        if ($isOpen === null) {
            return '营业中';
        }
        return $isOpen ? '营业中' : '休息中';
    }

    /**
     * 生成NFC芯片ID
     */
    public static function generateNfcChipId()
    {
        do {
            $chipId = 'NFC' . strtoupper(uniqid());
        } while (self::where('nfc_chip_id', $chipId)->exists());
        
        return $chipId;
    }

    /**
     * 检查NFC芯片ID是否可用
     */
    public static function isNfcChipIdAvailable($chipId)
    {
        return !self::where('nfc_chip_id', $chipId)->exists();
    }

    /**
     * 获取距离（需要传入目标坐标）
     */
    public function getDistanceTo($latitude, $longitude)
    {
        if (!$this->latitude || !$this->longitude) {
            return null;
        }

        $earthRadius = 6371; // 地球半径（公里）
        
        $latFrom = deg2rad($this->latitude);
        $lonFrom = deg2rad($this->longitude);
        $latTo = deg2rad($latitude);
        $lonTo = deg2rad($longitude);
        
        $latDelta = $latTo - $latFrom;
        $lonDelta = $lonTo - $lonFrom;
        
        $a = sin($latDelta / 2) * sin($latDelta / 2) +
             cos($latFrom) * cos($latTo) *
             sin($lonDelta / 2) * sin($lonDelta / 2);
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
        
        return $earthRadius * $c;
    }

    /**
     * 获取推广权限状态
     */
    public function getPromoteStatusAttribute()
    {
        return $this->can_promote ? '允许' : '禁止';
    }

    /**
     * 获取推广权限颜色
     */
    public function getPromoteColorAttribute()
    {
        return $this->can_promote ? 'success' : 'danger';
    }
}