<?php

namespace App\Admin\Controllers;

use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;
use App\Models\Recruitment;
use App\Models\Agent;

class RecruitmentController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = '招募管理';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new Recruitment());

        $grid->column('id', __('ID'));
        $grid->column('applicant_name', __('申请人姓名'));
        $grid->column('phone', __('联系电话'));
        $grid->column('email', __('邮箱'));
        $grid->column('application_type', __('申请类型'))->using([
            'self_apply' => '用户自助申请',
            'platform_input' => '平台录入'
        ])->label([
            'self_apply' => 'info',
            'platform_input' => 'primary'
        ]);
        $grid->column('recruitment_channel', __('招募渠道'));
        $grid->column('intended_agent.name', __('意向代理商'));
        $grid->column('status', __('申请状态'))->using([
            'pending' => '待审核',
            'approved' => '已通过',
            'rejected' => '已驳回'
        ])->label([
            'pending' => 'warning',
            'approved' => 'success',
            'rejected' => 'danger'
        ]);
        $grid->column('created_at', __('申请时间'));
        $grid->column('updated_at', __('更新时间'));

        $grid->actions(function ($actions) {
            /** @var Recruitment $this */
            if ($this->status == 'pending') {
                $actions->add(new \App\Admin\Actions\ApproveApplication);
                $actions->add(new \App\Admin\Actions\RejectApplication);
            }
        });

        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(Recruitment::findOrFail($id));

        $show->field('id', __('ID'));
        $show->field('applicant_name', __('申请人姓名'));
        $show->field('phone', __('联系电话'));
        $show->field('email', __('邮箱'));
        $show->field('id_card', __('身份证号'));
        $show->field('address', __('地址'));
        $show->field('application_type', __('申请类型'));
        $show->field('recruitment_channel', __('招募渠道'));
        $show->field('intended_agent.name', __('意向代理商'));
        $show->field('work_experience', __('工作经验'));
        $show->field('self_introduction', __('自我介绍'));
        $show->field('status', __('申请状态'));
        $show->field('audit_remark', __('审核备注'));
        $show->field('auditor', __('审核人'));
        $show->field('audit_time', __('审核时间'));
        $show->field('created_at', __('申请时间'));
        $show->field('updated_at', __('更新时间'));

        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new Recruitment());

        $form->text('applicant_name', __('申请人姓名'))->required();
        $form->mobile('phone', __('联系电话'))->required();
        $form->email('email', __('邮箱'))->required();
        $form->text('id_card', __('身份证号'));
        $form->text('address', __('地址'));
        $form->select('application_type', __('申请类型'))->options([
            'self_apply' => '用户自助申请',
            'platform_input' => '平台录入'
        ])->default('platform_input');
        $form->select('recruitment_channel', __('招募渠道'))->options([
            'online_promotion' => '线上推广',
            'offline_activity' => '线下活动',
            'referral' => '推荐入职',
            'direct_contact' => '直接联系'
        ]);
        $form->select('intended_agent_id', __('意向代理商'))->options(Agent::all()->pluck('name', 'id'));
        $form->textarea('work_experience', __('工作经验'));
        $form->textarea('self_introduction', __('自我介绍'));
        $form->select('status', __('申请状态'))->options([
            'pending' => '待审核',
            'approved' => '已通过',
            'rejected' => '已驳回'
        ])->default('pending');
        $form->textarea('audit_remark', __('审核备注'));
        $form->text('auditor', __('审核人'));
        $form->datetime('audit_time', __('审核时间'));

        return $form;
    }
}