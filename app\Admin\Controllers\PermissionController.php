<?php

namespace App\Admin\Controllers;

use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;
use Encore\Admin\Auth\Database\Role;
use Encore\Admin\Auth\Database\Permission;
use Encore\Admin\Auth\Database\Administrator;

class PermissionController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = '权限管理';

    /**
     * 角色管理
     */
    public function roles()
    {
        $grid = new Grid(new Role());

        $grid->column('id', __('ID'));
        $grid->column('slug', __('角色标识'));
        $grid->column('name', __('角色名称'));
        $grid->column('permissions', __('权限'))->pluck('name')->label();
        $grid->column('created_at', __('创建时间'));
        $grid->column('updated_at', __('更新时间'));

        return $grid;
    }

    /**
     * 权限管理
     */
    public function permissions()
    {
        $grid = new Grid(new Permission());

        $grid->column('id', __('ID'));
        $grid->column('slug', __('权限标识'));
        $grid->column('name', __('权限名称'));
        $grid->column('http_method', __('HTTP方法'))->label();
        $grid->column('http_path', __('HTTP路径'));
        $grid->column('created_at', __('创建时间'));
        $grid->column('updated_at', __('更新时间'));

        return $grid;
    }

    /**
     * 用户管理
     */
    public function users()
    {
        $grid = new Grid(new Administrator());

        $grid->column('id', __('ID'));
        $grid->column('username', __('用户名'));
        $grid->column('name', __('姓名'));
        $grid->column('roles', __('角色'))->pluck('name')->label();
        $grid->column('created_at', __('创建时间'));
        $grid->column('updated_at', __('更新时间'));

        return $grid;
    }

    /**
     * 角色表单
     */
    public function roleForm()
    {
        $form = new Form(new Role());

        $form->text('slug', __('角色标识'))->required();
        $form->text('name', __('角色名称'))->required();
        $form->listbox('permissions', __('权限'))->options(Permission::all()->pluck('name', 'id'));

        return $form;
    }

    /**
     * 权限表单
     */
    public function permissionForm()
    {
        $form = new Form(new Permission());

        $form->text('slug', __('权限标识'))->required();
        $form->text('name', __('权限名称'))->required();
        $form->multipleSelect('http_method', __('HTTP方法'))->options([
            'GET' => 'GET',
            'POST' => 'POST',
            'PUT' => 'PUT',
            'DELETE' => 'DELETE',
            'PATCH' => 'PATCH',
            'OPTIONS' => 'OPTIONS',
            'HEAD' => 'HEAD'
        ]);
        $form->textarea('http_path', __('HTTP路径'))->placeholder('每行一个路径，支持通配符');

        return $form;
    }

    /**
     * 用户表单
     */
    public function userForm()
    {
        $form = new Form(new Administrator());

        $form->text('username', __('用户名'))->required();
        $form->text('name', __('姓名'))->required();
        $form->image('avatar', __('头像'));
        $form->password('password', __('密码'))->required();
        $form->password('password_confirmation', __('确认密码'))->required();
        $form->listbox('roles', __('角色'))->options(Role::all()->pluck('name', 'id'));
        $form->listbox('permissions', __('权限'))->options(Permission::all()->pluck('name', 'id'));

        $form->saving(function (Form $form) {
            if ($form->password && $form->model()->password != $form->password) {
                $form->password = bcrypt($form->password);
            }
        });

        return $form;
    }

    /**
     * 操作日志
     */
    public function operationLogs()
    {
        $grid = new Grid(new \Encore\Admin\Auth\Database\OperationLog());

        $grid->column('id', __('ID'));
        $grid->column('user.name', __('操作用户'));
        $grid->column('method', __('请求方法'))->label([
            'GET' => 'info',
            'POST' => 'success',
            'PUT' => 'warning',
            'DELETE' => 'danger'
        ]);
        $grid->column('path', __('请求路径'));
        $grid->column('ip', __('IP地址'));
        $grid->column('input', __('请求参数'))->display(function ($input) {
            $input = json_decode($input, true);
            return empty($input) ? '-' : json_encode($input, JSON_UNESCAPED_UNICODE);
        })->limit(50);
        $grid->column('created_at', __('操作时间'));

        $grid->model()->orderBy('id', 'desc');
        $grid->disableCreateButton();
        $grid->disableActions();

        $grid->filter(function ($filter) {
            $filter->like('path', '请求路径');
            $filter->equal('method', '请求方法')->select([
                'GET' => 'GET',
                'POST' => 'POST',
                'PUT' => 'PUT',
                'DELETE' => 'DELETE'
            ]);
            $filter->like('ip', 'IP地址');
            $filter->between('created_at', '操作时间')->datetime();
        });

        return $grid;
    }

    /**
     * 初始化权限数据
     */
    public function initPermissions()
    {
        $permissions = [
            // 代理商管理权限
            ['slug' => 'agent.list', 'name' => '代理商列表', 'http_method' => ['GET'], 'http_path' => '/agents*'],
            ['slug' => 'agent.create', 'name' => '创建代理商', 'http_method' => ['GET', 'POST'], 'http_path' => '/agents/create*'],
            ['slug' => 'agent.edit', 'name' => '编辑代理商', 'http_method' => ['GET', 'PUT'], 'http_path' => '/agents/*/edit*'],
            ['slug' => 'agent.delete', 'name' => '删除代理商', 'http_method' => ['DELETE'], 'http_path' => '/agents/*'],
            
            // 商铺管理权限
            ['slug' => 'merchant.list', 'name' => '商铺列表', 'http_method' => ['GET'], 'http_path' => '/merchants*'],
            ['slug' => 'merchant.create', 'name' => '创建商铺', 'http_method' => ['GET', 'POST'], 'http_path' => '/merchants/create*'],
            ['slug' => 'merchant.edit', 'name' => '编辑商铺', 'http_method' => ['GET', 'PUT'], 'http_path' => '/merchants/*/edit*'],
            ['slug' => 'merchant.delete', 'name' => '删除商铺', 'http_method' => ['DELETE'], 'http_path' => '/merchants/*'],
            
            // 素材管理权限
            ['slug' => 'material.list', 'name' => '素材列表', 'http_method' => ['GET'], 'http_path' => '/materials*'],
            ['slug' => 'material.create', 'name' => '创建素材', 'http_method' => ['GET', 'POST'], 'http_path' => '/materials/create*'],
            ['slug' => 'material.edit', 'name' => '编辑素材', 'http_method' => ['GET', 'PUT'], 'http_path' => '/materials/*/edit*'],
            ['slug' => 'material.delete', 'name' => '删除素材', 'http_method' => ['DELETE'], 'http_path' => '/materials/*'],
            
            // 业务员管理权限
            ['slug' => 'salesperson.list', 'name' => '业务员列表', 'http_method' => ['GET'], 'http_path' => '/salespersons*'],
            ['slug' => 'salesperson.create', 'name' => '创建业务员', 'http_method' => ['GET', 'POST'], 'http_path' => '/salespersons/create*'],
            ['slug' => 'salesperson.edit', 'name' => '编辑业务员', 'http_method' => ['GET', 'PUT'], 'http_path' => '/salespersons/*/edit*'],
            
            // 数据统计权限
            ['slug' => 'statistics.view', 'name' => '查看统计', 'http_method' => ['GET'], 'http_path' => '/statistics*'],
            ['slug' => 'statistics.export', 'name' => '导出数据', 'http_method' => ['GET', 'POST'], 'http_path' => '/statistics/export*'],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(
                ['slug' => $permission['slug']],
                $permission
            );
        }

        return response()->json(['message' => '权限初始化完成']);
    }
}