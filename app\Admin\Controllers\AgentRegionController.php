<?php

namespace App\Admin\Controllers;

use App\Models\Agent;
use App\Models\AgentRegion;
use App\Models\Area;
use App\Services\PermissionService;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;
use Encore\Admin\Layout\Content;
use Encore\Admin\Admin;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;
use Throwable;


/**
 * 代理商区域配置控制器
 * 
 * 基于agent_regions表进行独立的区域配置管理
 * 支持一个代理商配置多个区域
 * 
 * <AUTHOR>
 */
class AgentRegionController extends AdminController
{
    /**
     * 页面标题
     *
     * @var string
     */
    protected $title = '代理商区域配置';

    /**
     * 列表页面
     * 🔄 开发阶段：显示代理商的区域配置信息
     *
     * @param Content $content
     * @return Content
     */
    public function index(Content $content)
    {
        // 权限检查
        if (!PermissionService::hasPermission('agent.region')) {
            abort(403, '没有权限访问此页面');
        }

        return $content
            ->header('区域配置')
            ->description('管理代理商负责的区域')
            ->body($this->grid());
    }

    /**
     * 编辑代理商区域配置
     */
    public function edit($id, Content $content)
    {
        // 权限检查 - 一级代理商和二级代理商不能编辑
        $userType = PermissionService::getCurrentUserType();
        if ($userType == 2 || $userType == 3) {
            abort(403, '没有权限编辑区域配置');
        }

        if (!PermissionService::hasPermission('agent.region')) {
            abort(403, '没有权限访问此页面');
        }

        // 检查是否是Ajax请求获取特定步骤
        if (request()->ajax() && request()->has('step')) {
            return $this->getStepContent(request('step'), $id);
        }

        return $content
            ->header('编辑代理商区域配置')
            ->description('修改代理商负责的区域配置')
            ->body($this->createStepWizardContent($id));
    }

    /**
     * 创建页面 - 分步表单入口
     *
     * @param Content $content
     * @return Content
     */
    public function create(Content $content)
    {
        // 权限检查 - 一级代理商和二级代理商不能创建
        $userType = PermissionService::getCurrentUserType();
        if ($userType == 2 || $userType == 3) {
            abort(403, '没有权限创建区域配置');
        }

        if (!PermissionService::hasPermission('agent.region')) {
            abort(403, '没有权限访问此页面');
        }

        // 检查是否是Ajax请求获取特定步骤
        if (request()->ajax() && request()->has('step')) {
            return $this->getStepContent(request('step'));
        }

        return $content
            ->header('配置代理商区域')
            ->description('为代理商配置负责的区域')
            ->body($this->createStepWizardContent());
    }

    /**
     * 创建分步向导的主要内容（使用Blade视图）
     */
    protected function createStepWizardContent($id = null)
    {
        try {
            // 正确处理agentId和编辑状态
            $agentId = request('agent_id'); // 从请求中获取agent_id
            $steps = $this->getStepDefinitions();

            // 如果是编辑模式，获取完整的编辑数据
            $agentRegion = null;
            $editData = null;
            $isEditing = !is_null($id); // $id是agent_region的记录ID

            if ($id) {
                // 编辑模式：获取完整的编辑数据
                $editData = $this->getCompleteEditData($id);
                if ($editData) {
                    $agentRegion = $editData['agent_region'];
                    $agentId = $agentRegion->agent_id; // 从记录中获取代理商ID
                }
            }

            // 视图层架构：数据层与视图层严格分离
            $html = view('admin.agent-region.step-wizard', [
                'steps' => $steps,
                'agent_id' => $agentId,
                'edit_id' => $id,
                'agent_region' => $agentRegion,
                'edit_data' => $editData, // 传递完整的编辑数据
                'is_editing' => $isEditing
            ])->render();

            return $html;
        } catch (\Exception $e) {
            \Log::error('AgentRegion Step Wizard render failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 返回错误提示HTML
            return '<div class="alert alert-danger">
                <h4><i class="fa fa-exclamation-triangle"></i> 页面加载失败</h4>
                <p>错误信息：' . e($e->getMessage()) . '</p>
                <p>请检查视图文件和相关配置。</p>
                <div style="margin-top: 15px;">
                    <button class="btn btn-default" onclick="window.location.reload()">
                        <i class="fa fa-refresh"></i> 重新加载
                    </button>
                </div>
            </div>';
        }
    }

    /**
     * 获取完整的编辑数据（包括省市区完整解析）
     * 一次性处理所有数据转换逻辑
     */
    protected function getCompleteEditData($id)
    {
        try {
            // 第一步：通过id在agent_regions数据表中查询所有数据
            $agentRegion = AgentRegion::with(['agent'])->find($id);

            if (!$agentRegion) {
                return null;
            }

            // 初始化完整数据结构
            $editData = [
                'agent_region' => $agentRegion,
                'province_id' => null,
                'province_name' => null,
                'city_id' => null,
                'city_name' => null,
                'district_id' => null,
                'district_name' => null,
                'area_level' => null,
                'area_id' => $agentRegion->area_id,
                'region_complete' => false
            ];

            // 第二步：判断province_id、city_id、district_id是否都不为空
            if (!empty($agentRegion->province_id) && !empty($agentRegion->city_id) && !empty($agentRegion->district_id)) {
                // 如果三个ID都不为空，直接使用并查询名称
                $editData['province_id'] = $agentRegion->province_id;
                $editData['city_id'] = $agentRegion->city_id;
                $editData['district_id'] = $agentRegion->district_id;

                // 查询对应的区域名称
                $province = Area::find($agentRegion->province_id);
                $city = Area::find($agentRegion->city_id);
                $district = Area::find($agentRegion->district_id);

                $editData['province_name'] = $province ? $province->name : null;
                $editData['city_name'] = $city ? $city->name : null;
                $editData['district_name'] = $district ? $district->name : null;
                $editData['region_complete'] = true;
            } else {
                // 第三步：通过area_id解析完整的省市区信息
                if (!empty($agentRegion->area_id)) {
                    $currentArea = Area::find($agentRegion->area_id);

                    if ($currentArea) {
                        $editData['area_level'] = $currentArea->level;

                        switch ($currentArea->level) {
                            case 1: // 省级
                                $editData['province_id'] = $currentArea->id;
                                $editData['province_name'] = $currentArea->name;
                                break;

                            case 2: // 市级  
                                $editData['city_id'] = $currentArea->id;
                                $editData['city_name'] = $currentArea->name;

                                // 通过市的pid获取省信息
                                if ($currentArea->pid) {
                                    $provinceArea = Area::find($currentArea->pid);
                                    if ($provinceArea && $provinceArea->level == 1) {
                                        $editData['province_id'] = $provinceArea->id;
                                        $editData['province_name'] = $provinceArea->name;
                                    }
                                }
                                break;

                            case 3: // 区县级
                                $editData['district_id'] = $currentArea->id;
                                $editData['district_name'] = $currentArea->name;

                                // 第四步：通过区县的pid获取市信息
                                if ($currentArea->pid) {
                                    $cityArea = Area::find($currentArea->pid);
                                    if ($cityArea && $cityArea->level == 2) {
                                        $editData['city_id'] = $cityArea->id;
                                        $editData['city_name'] = $cityArea->name;

                                        // 第五步：通过市的pid获取省信息
                                        if ($cityArea->pid) {
                                            $provinceArea = Area::find($cityArea->pid);
                                            if ($provinceArea && $provinceArea->level == 1) {
                                                $editData['province_id'] = $provinceArea->id;
                                                $editData['province_name'] = $provinceArea->name;
                                            }
                                        }
                                    }
                                }

                                // 如果成功解析到省市区，标记为完整
                                if ($editData['province_id'] && $editData['city_id'] && $editData['district_id']) {
                                    $editData['region_complete'] = true;
                                }
                                break;
                        }
                    }
                }
            }

            return $editData;
        } catch (\Exception $e) {
            \Log::error('AgentRegion getCompleteEditData failed', [
                'id' => $id,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 获取步骤定义
     */
    protected function getStepDefinitions()
    {
        return [
            1 => [
                'title' => '基础配置',
                'description' => '选择代理商和设置管理区域',
                'icon' => 'fa-user'
            ],
            2 => [
                'title' => '合同管理',
                'description' => '配置合约信息和状态',
                'icon' => 'fa-file-text'
            ],
            3 => [
                'title' => '返佣配置',
                'description' => '设置返佣比例和类型',
                'icon' => 'fa-percent'
            ],
            4 => [
                'title' => '结算配置',
                'description' => '配置结算周期和方式',
                'icon' => 'fa-calculator'
            ],
            5 => [
                'title' => '备注说明',
                'description' => '添加备注和特殊说明',
                'icon' => 'fa-comment'
            ]
        ];
    }

    /**
     * 获取指定步骤的内容 (API接口)
     */
    public function getStepContent($step, $id = null)
    {
        try {
            switch ($step) {
                case '1':
                case 1:
                    return response()->json([
                        'success' => true,
                        'content' => $this->getStep1Content($id)
                    ]);
                case '2':
                case 2:
                    return response()->json([
                        'success' => true,
                        'content' => $this->getStep2Content($id)
                    ]);
                case '3':
                case 3:
                    return response()->json([
                        'success' => true,
                        'content' => $this->getStep3Content($id)
                    ]);
                case '4':
                case 4:
                    return response()->json([
                        'success' => true,
                        'content' => $this->getStep4Content($id)
                    ]);
                case '5':
                case 5:
                    return response()->json([
                        'success' => true,
                        'content' => $this->getStep5Content($id)
                    ]);
                default:
                    return response()->json([
                        'success' => false,
                        'message' => '无效的步骤编号'
                    ]);
            }
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取步骤内容失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 第一步：基础配置
     */
    protected function getStep1Content($id = null)
    {
        // 获取基础数据
        $agentId = request('agent_id');
        $agents = Agent::where('status', 'active')->pluck('name', 'id');
        $provinces = Area::where('level', 1)->pluck('name', 'id');

        // 初始化变量
        $agentRegion = null;
        $cities = collect();
        $districts = collect();
        $resolvedProvinceId = null;
        $resolvedCityId = null;
        $resolvedDistrictId = null;

        // 如果是编辑模式，按照用户需求的逻辑解析省市区
        if ($id) {
            // 第一步：通过id在agent_regions数据表中查询所有数据
            $agentRegion = AgentRegion::find($id);

            if ($agentRegion) {
                $agentId = $agentRegion->agent_id;

                // 第二步：判断province_id和city_id和district_id是否为空
                if (!empty($agentRegion->province_id) && !empty($agentRegion->city_id) && !empty($agentRegion->district_id)) {
                    // 如果三个ID都不为空，直接使用
                    $resolvedProvinceId = $agentRegion->province_id;
                    $resolvedCityId = $agentRegion->city_id;
                    $resolvedDistrictId = $agentRegion->district_id;
                } else {
                    // 第三步：为空则判断area_id是否为空
                    if (!empty($agentRegion->area_id)) {
                        // 第四步：area_id不为空查询areas数据表area_id=id，获取level
                        $currentArea = Area::find($agentRegion->area_id);

                        if ($currentArea) {
                            // 根据level进行不同处理
                            switch ($currentArea->level) {
                                case 1: // 省级
                                    $resolvedProvinceId = $currentArea->id;
                                    break;

                                case 2: // 市级  
                                    $resolvedCityId = $currentArea->id;
                                    // 通过市的pid获取省id
                                    if ($currentArea->pid) {
                                        $provinceArea = Area::find($currentArea->pid);
                                        if ($provinceArea && $provinceArea->level == 1) {
                                            $resolvedProvinceId = $provinceArea->id;
                                        }
                                    }
                                    break;

                                case 3: // 区县级
                                    $resolvedDistrictId = $currentArea->id;
                                    // 第五步：若level=3那么他的pid就是市id
                                    if ($currentArea->pid) {
                                        $cityArea = Area::find($currentArea->pid);
                                        if ($cityArea && $cityArea->level == 2) {
                                            $resolvedCityId = $cityArea->id;
                                            // 第六步：然后通过市id查询省id
                                            if ($cityArea->pid) {
                                                $provinceArea = Area::find($cityArea->pid);
                                                if ($provinceArea && $provinceArea->level == 1) {
                                                    $resolvedProvinceId = $provinceArea->id;
                                                }
                                            }
                                        }
                                    }
                                    break;
                            }
                        }
                    }
                }

                // 根据解析出的省市ID加载对应的下拉选项
                if ($resolvedProvinceId) {
                    $cities = Area::where('pid', $resolvedProvinceId)
                        ->where('level', 2)
                        ->pluck('name', 'id');
                }

                if ($resolvedCityId) {
                    $districts = Area::where('pid', $resolvedCityId)
                        ->where('level', 3)
                        ->pluck('name', 'id');
                }

                // 更新agent_region对象中的省市区ID（用于视图显示）
                $agentRegion->province_id = $resolvedProvinceId;
                $agentRegion->city_id = $resolvedCityId;
                $agentRegion->district_id = $resolvedDistrictId;
            }
        }

        $viewData = [
            'agent_id' => $agentId,
            'agents' => $agents,
            'provinces' => $provinces,
            'cities' => $cities,
            'districts' => $districts,
            'agent_region' => $agentRegion,
            'resolved_province_id' => $resolvedProvinceId,
            'resolved_city_id' => $resolvedCityId,
            'resolved_district_id' => $resolvedDistrictId,
            'is_editing' => !is_null($id)
        ];

        return view('admin.agent-region.steps.step1', $viewData)->render();
    }

    /**
     * 第二步：合同管理
     */
    protected function getStep2Content($id = null)
    {
        // 如果是编辑模式，加载现有数据
        $agentRegion = null;
        if ($id) {
            $agentRegion = AgentRegion::find($id);
        }

        return view('admin.agent-region.steps.step2', [
            'id' => $id,
            'agent_region' => $agentRegion
        ])->render();
    }

    /**
     * 第三步：返佣配置
     */
    protected function getStep3Content($id = null)
    {
        // 如果是编辑模式，加载现有数据
        $agentRegion = null;
        if ($id) {
            $agentRegion = AgentRegion::find($id);
        }

        return view('admin.agent-region.steps.step3', [
            'id' => $id,
            'agent_region' => $agentRegion
        ])->render();
    }

    /**
     * 第四步：结算配置
     */
    protected function getStep4Content($id = null)
    {
        // 如果是编辑模式，加载现有数据
        $agentRegion = null;
        if ($id) {
            $agentRegion = AgentRegion::find($id);
        }

        return view('admin.agent-region.steps.step4', [
            'id' => $id,
            'agent_region' => $agentRegion
        ])->render();
    }

    /**
     * 第五步：备注说明
     */
    protected function getStep5Content($id = null)
    {
        // 如果是编辑模式，加载现有数据
        $agentRegion = null;
        if ($id) {
            $agentRegion = AgentRegion::find($id);
        }

        return view('admin.agent-region.steps.step5', [
            'id' => $id,
            'agent_region' => $agentRegion
        ])->render();
    }

    /**
     * Ajax保存分步表单数据
     */
    public function saveStepData()
    {
        try {
            // 启用SQL查询日志
            DB::enableQueryLog();

            // 处理JSON请求数据
            $requestData = request()->json()->all();
            $step = $requestData['step'] ?? request('step');
            $data = $requestData['data'] ?? request('data', []);
            $editId = $requestData['edit_id'] ?? request('edit_id');

            Log::info('📊 [DEBUG] saveStepData 接收到请求', [
                'step' => $step,
                'edit_id' => $editId,
                'data_keys' => array_keys($data),
                'raw_request' => $requestData,
                'request_method' => request()->method(),
                'content_type' => request()->header('Content-Type'),
                'accept' => request()->header('Accept')
            ]);

            // 详细打印接收到的数据
            Log::info('📊 [DEBUG] 接收到的完整数据', [
                'step' => $step,
                'edit_id' => $editId,
                'data' => $data,
                'data_count' => count($data)
            ]);

            // 验证步骤数据
            $validation = $this->validateStepData($step, $data);
            if (!$validation['valid']) {
                Log::warning('⚠️ [DEBUG] 步骤数据验证失败', [
                    'step' => $step,
                    'validation' => $validation
                ]);
                return response()->json([
                    'success' => false,
                    'message' => $validation['message']
                ], 400);
            }

            // 如果是最后一步，保存完整数据到数据库
            if ($step == 5) {
                Log::info('📊 [DEBUG] 触发最终数据保存', [
                    'step' => $step,
                    'edit_id' => $editId,
                    'data_keys' => array_keys($data)
                ]);

                // 打印关键字段值
                Log::info('📊 [DEBUG] 关键字段值检查', [
                    'agent_id' => $data['agent_id'] ?? 'null',
                    'province_id' => $data['province_id'] ?? 'null',
                    'city_id' => $data['city_id'] ?? 'null',
                    'district_id' => $data['district_id'] ?? 'null',
                    'is_exclusive' => $data['is_exclusive'] ?? 'null',
                    'contract_status' => $data['contract_status'] ?? 'null',
                    'commission_type' => $data['commission_type'] ?? 'null',
                    'settlement_cycle' => $data['settlement_cycle'] ?? 'null'
                ]);

                return $this->saveFinalData($data, $editId);
            }

            // 其他步骤只返回成功
            return response()->json([
                'success' => true,
                'message' => '步骤数据验证通过',
                'step' => $step
            ]);
        } catch (Throwable $e) {
            Log::error('❌ [DEBUG] saveStepData 异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'message' => '保存失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 验证步骤数据
     */
    protected function validateStepData($step, $data)
    {
        switch ($step) {
            case 1:
                if (empty($data['agent_id'])) {
                    return ['valid' => false, 'message' => '请选择代理商'];
                }
                if (empty($data['province_id'])) {
                    return ['valid' => false, 'message' => '请选择省份'];
                }
                break;

            case 2:
                if (empty($data['contract_status'])) {
                    return ['valid' => false, 'message' => '请选择合约状态'];
                }
                if ($data['contract_status'] === 'signed' && empty($data['contract_no'])) {
                    return ['valid' => false, 'message' => '已签署状态下必须填写合约编号'];
                }
                break;

            case 3:
                if (empty($data['commission_type'])) {
                    return ['valid' => false, 'message' => '请选择返佣类型'];
                }
                if (!empty($data['commission_rate'])) {
                    $rate = floatval($data['commission_rate']);
                    if ($rate < 0 || $rate > 100) {
                        return ['valid' => false, 'message' => '返佣比例应在0-100%之间'];
                    }
                }
                break;

            case 4:
                if (empty($data['settlement_cycle'])) {
                    return ['valid' => false, 'message' => '请选择结算周期'];
                }
                break;
        }

        return ['valid' => true];
    }

    /**
     * 保存最终数据
     * 
     * @param array $allData 完整的表单数据
     * @param int|null $editId 编辑模式的ID
     * @return \Illuminate\Http\JsonResponse
     */
    protected function saveFinalData($allData, $editId = null)
    {
        try {
            // 清除任何之前的输出缓冲，确保JSON响应纯净
            if (ob_get_level()) {
                ob_clean();
            }

            // 启用SQL查询日志
            DB::enableQueryLog();

            Log::info('📊 [DEBUG] saveFinalData 开始处理数据', [
                'edit_id' => $editId,
                'input_data' => $allData,
                'input_data_count' => count($allData)
            ]);

            // 验证必要字段
            $requiredFields = ['agent_id'];
            foreach ($requiredFields as $field) {
                if (empty($allData[$field])) {
                    Log::error('❌ [DEBUG] 缺少必要字段', [
                        'missing_field' => $field,
                        'all_data' => $allData
                    ]);
                    return response()->json([
                        'success' => false,
                        'message' => "缺少必要字段: {$field}"
                    ], 400);
                }
            }

            // 设置area_id
            if (!empty($allData['district_id'])) {
                $allData['area_id'] = $allData['district_id'];
                Log::info('📊 [DEBUG] 使用区县作为area_id', ['district_id' => $allData['district_id']]);
            } elseif (!empty($allData['city_id'])) {
                $allData['area_id'] = $allData['city_id'];
                Log::info('📊 [DEBUG] 使用城市作为area_id', ['city_id' => $allData['city_id']]);
            } elseif (!empty($allData['province_id'])) {
                $allData['area_id'] = $allData['province_id'];
                Log::info('📊 [DEBUG] 使用省份作为area_id', ['province_id' => $allData['province_id']]);
            } else {
                Log::error('❌ [DEBUG] 区域ID缺失', $allData);
                return response()->json([
                    'success' => false,
                    'message' => '必须至少选择一个区域'
                ], 400);
            }

            // 设置区域名称
            if (!empty($allData['province_id'])) {
                $province = Area::find($allData['province_id']);
                if ($province) {
                    $allData['province_name'] = $province->name;
                    $allData['province_code'] = $province->citycode ?? $province->id;
                    Log::info('📊 [DEBUG] 设置省份信息', [
                        'province_id' => $allData['province_id'],
                        'province_name' => $allData['province_name'],
                        'province_code' => $allData['province_code']
                    ]);
                }
            }

            if (!empty($allData['city_id'])) {
                $city = Area::find($allData['city_id']);
                if ($city) {
                    $allData['city_name'] = $city->name;
                    $allData['city_code'] = $city->citycode ?? $city->id;
                    Log::info('📊 [DEBUG] 设置城市信息', [
                        'city_id' => $allData['city_id'],
                        'city_name' => $allData['city_name'],
                        'city_code' => $allData['city_code']
                    ]);
                }
            }

            if (!empty($allData['district_id'])) {
                $district = Area::find($allData['district_id']);
                if ($district) {
                    $allData['district_name'] = $district->name;
                    $allData['district_code'] = $district->citycode ?? $district->id;
                    Log::info('📊 [DEBUG] 设置区县信息', [
                        'district_id' => $allData['district_id'],
                        'district_name' => $allData['district_name'],
                        'district_code' => $allData['district_code']
                    ]);
                }
            }

            // 检查区域独占（编辑模式需要排除自己）
            if (!empty($allData['is_exclusive'])) {
                Log::info('📊 [DEBUG] 开始检查区域独占', [
                    'target_area_id' => $allData['area_id'],
                    'is_exclusive' => $allData['is_exclusive'],
                    'edit_id' => $editId,
                    'edit_mode' => !is_null($editId)
                ]);

                $query = AgentRegion::where('area_id', $allData['area_id'])
                    ->where('is_exclusive', true);

                // 编辑模式：排除当前记录
                if ($editId) {
                    $query->where('id', '!=', $editId);
                    Log::info('📊 [DEBUG] 编辑模式：排除当前记录', ['excluded_id' => $editId]);
                } else {
                    Log::info('📊 [DEBUG] 创建模式：不排除任何记录');
                }

                // 打印实际执行的SQL查询
                $sqlQuery = $query->toSql();
                $bindings = $query->getBindings();
                Log::info('📊 [DEBUG] 独占检查SQL', [
                    'sql' => $sqlQuery,
                    'bindings' => $bindings
                ]);

                $existingRegion = $query->first();

                if ($existingRegion) {
                    Log::warning('⚠️ [DEBUG] 发现区域独占冲突', [
                        'conflict_record_id' => $existingRegion->id,
                        'conflict_agent_id' => $existingRegion->agent_id,
                        'conflict_agent_name' => $existingRegion->agent->name ?? 'Unknown',
                        'conflict_area_id' => $existingRegion->area_id,
                        'current_edit_id' => $editId,
                        'is_same_record' => $existingRegion->id == $editId
                    ]);
                    return response()->json([
                        'success' => false,
                        'message' => '该区域已被代理商 "' . $existingRegion->agent->name . '" 独占 (记录ID: ' . $existingRegion->id . ')'
                    ], 400);
                } else {
                    Log::info('✅ [DEBUG] 区域独占检查通过', [
                        'area_id' => $allData['area_id'],
                        'no_conflicts_found' => true
                    ]);
                }
            } else {
                Log::info('📊 [DEBUG] 跳过区域独占检查', [
                    'reason' => 'is_exclusive为false或空',
                    'is_exclusive_value' => $allData['is_exclusive'] ?? 'null'
                ]);
            }

            // 自动计算下次结算日期
            if (!empty($allData['settlement_cycle']) && !empty($allData['settlement_day'])) {
                $now = now();
                switch ($allData['settlement_cycle']) {
                    case 'weekly':
                        $allData['next_settlement_at'] = $now->addWeek()->format('Y-m-d');
                        break;
                    case 'monthly':
                        $settlementDay = min($allData['settlement_day'], $now->addMonth()->daysInMonth);
                        $allData['next_settlement_at'] = $now->addMonth()->day($settlementDay)->format('Y-m-d');
                        break;
                    case 'quarterly':
                        $settlementDay = min($allData['settlement_day'], $now->addMonths(3)->daysInMonth);
                        $allData['next_settlement_at'] = $now->addMonths(3)->day($settlementDay)->format('Y-m-d');
                        break;
                }
                Log::info('📊 [DEBUG] 计算下次结算日期', [
                    'settlement_cycle' => $allData['settlement_cycle'],
                    'settlement_day' => $allData['settlement_day'],
                    'next_settlement_at' => $allData['next_settlement_at']
                ]);
            }

            Log::info('📊 [DEBUG] 准备保存的完整数据', $allData);

            // 保存到数据库
            if ($editId) {
                // 编辑模式：更新现有记录
                Log::info('📊 [DEBUG] 编辑模式：查找现有记录', ['edit_id' => $editId]);

                // 先查询现有记录
                $existingQuery = AgentRegion::where('id', $editId);
                $existingSql = $existingQuery->toSql();
                $existingBindings = $existingQuery->getBindings();
                Log::info('📊 [DEBUG] 查询现有记录SQL', [
                    'sql' => $existingSql,
                    'bindings' => $existingBindings
                ]);

                $agentRegion = $existingQuery->first();

                if (!$agentRegion) {
                    Log::error('❌ [DEBUG] 编辑记录不存在', ['edit_id' => $editId]);
                    return response()->json([
                        'success' => false,
                        'message' => '要编辑的记录不存在'
                    ], 404);
                }

                Log::info('📊 [DEBUG] 更新前的原始数据', $agentRegion->toArray());

                // 清除SQL日志，准备记录更新操作
                DB::flushQueryLog();

                // 强制更新所有字段，即使值没有变化
                // 方法1：使用update方法直接更新
                $updateData = array_intersect_key($allData, array_flip([
                    'agent_id',
                    'area_id',
                    'province_id',
                    'city_id',
                    'district_id',
                    'province_name',
                    'city_name',
                    'district_name',
                    'province_code',
                    'city_code',
                    'district_code',
                    'is_exclusive',
                    'region_status',
                    'contract_status',
                    'contract_no',
                    'contract_title',
                    'contract_file_url',
                    'signed_at',
                    'contract_start_date',
                    'contract_end_date',
                    'contract_notes',
                    'commission_type',
                    'commission_rate',
                    'min_commission_amount',
                    'max_commission_amount',
                    'commission_amount',
                    'min_order_amount',
                    'max_commission_per_order',
                    'max_commission_per_month',
                    'commission_rules',
                    'commission_effective_date',
                    'commission_expire_date',
                    'settlement_cycle',
                    'settlement_day',
                    'settlement_bank_account',
                    'settlement_bank_name',
                    'settlement_account_name',
                    'bank_branch',
                    'next_settlement_at',
                    'last_settlement_at',
                    'settlement_notes',
                    'notes',
                    'created_by',
                    'contact_info',
                    'remark'
                ]));

                // 添加更新时间
                $updateData['updated_at'] = now();

                Log::info('📊 [DEBUG] 准备强制更新的数据', $updateData);

                // 执行强制更新
                $updateResult = AgentRegion::where('id', $editId)->update($updateData);

                // 获取更新操作的SQL
                $updateQueries = DB::getQueryLog();
                Log::info('📊 [DEBUG] 强制更新操作的SQL语句', $updateQueries);

                // 验证更新结果
                $updatedRecord = AgentRegion::find($editId);
                Log::info('📊 [DEBUG] 更新后的数据', $updatedRecord->toArray());

                $message = '区域配置更新成功';
            } else {
                // 创建模式：新建记录
                Log::info('📊 [DEBUG] 创建模式：新建记录');

                // 清除SQL日志，准备记录创建操作
                DB::flushQueryLog();

                $agentRegion = new AgentRegion();
                $agentRegion->fill($allData);
                $agentRegion->save();

                // 获取创建操作的SQL
                $createQueries = DB::getQueryLog();
                Log::info('📊 [DEBUG] 创建操作的SQL语句', $createQueries);

                Log::info('📊 [DEBUG] 新建记录的数据', $agentRegion->toArray());

                $message = '区域配置创建成功';
            }

            // 获取所有执行的SQL语句
            $queries = DB::getQueryLog();
            Log::info('📊 [DEBUG] 所有执行的SQL语句', $queries);

            // 确保输出缓冲区干净
            while (ob_get_level()) {
                ob_end_clean();
            }

            return response()->json([
                'success' => true,
                'message' => $message,
                'redirect' => admin_url('agent-regions'),
                'record_id' => $agentRegion->id,
                'debug_info' => [
                    'edit_id' => $editId,
                    'operation' => $editId ? 'update' : 'create',
                    'sql_count' => count($queries)
                ]
            ]);
        } catch (Throwable $e) {
            Log::error('❌ [DEBUG] 保存失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $allData
            ]);

            return response()->json([
                'success' => false,
                'message' => '保存失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取城市列表 (Ajax)
     */
    public function getCities()
    {
        $provinceId = request('province_id');
        $cities = Area::where('pid', $provinceId)->pluck('name', 'id');

        return response()->json([
            'success' => true,
            'data' => $cities
        ]);
    }

    /**
     * 获取区县列表 (Ajax)
     */
    public function getDistricts()
    {
        $cityId = request('city_id');
        $districts = Area::where('pid', $cityId)->pluck('name', 'id');

        return response()->json([
            'success' => true,
            'data' => $districts
        ]);
    }

    /**
     * 详情页面.
     *
     * @param mixed $id
     * @param Content $content
     * @return Content
     */
    public function show($id, Content $content)
    {
        // 权限检查
        if (!PermissionService::hasPermission('agent.region')) {
            abort(403, '没有权限访问此页面');
        }

        $agentRegion = AgentRegion::with(['agent', 'province', 'city', 'district'])->findOrFail($id);

        // 根据用户类型检查数据访问权限
        $userType = PermissionService::getCurrentUserType();
        $currentUser = auth('admin')->user();

        if ($userType == 2) { // 一级代理商
            // 一级代理商只能查看自己下属的二级代理商的区域配置
            $currentAgentId = $currentUser->agent_id;
            if ($currentAgentId) {
                $subAgentIds = Agent::where('parent_agent_id', $currentAgentId)->pluck('id');
                if (!$subAgentIds->contains($agentRegion->agent_id)) {
                    abort(403, '没有权限查看此区域配置');
                }
            } else {
                abort(403, '没有权限查看此区域配置');
            }
        } elseif ($userType == 3) { // 二级代理商
            // 二级代理商只能查看自己的区域配置
            $currentAgentId = $currentUser->agent_id;
            if (!$currentAgentId || $agentRegion->agent_id != $currentAgentId) {
                abort(403, '没有权限查看此区域配置');
            }
        }
        // 平台管理员和超级管理员可以查看所有数据

        return $content
            ->header('代理商区域配置详情')
            ->description('查看区域配置的完整信息')
            ->body(view('admin.agent-region.show', ['agent_region' => $agentRegion]));
    }

    /**
     * 列表页面配置
     * 🔄 开发阶段：完整的代理商区域配置列表展示
     * 根据用户权限展示不同的数据范围
     *
     * @return Grid
     */
    protected function grid()
    {
        // 权限检查
        if (!PermissionService::hasPermission('agent.region')) {
            abort(403, '没有权限访问此页面');
        }

        $grid = new Grid(new AgentRegion());

        // 根据用户类型过滤数据
        $userType = PermissionService::getCurrentUserType();
        $currentUser = auth('admin')->user();

        if ($userType == 2) { // 一级代理商
            // 一级代理商只能看到自己下属的二级代理商的区域配置
            $currentAgentId = $currentUser->agent_id;
            if ($currentAgentId) {
                // 获取当前代理商的下级代理商ID
                $subAgentIds = Agent::where('parent_agent_id', $currentAgentId)->pluck('id');
                $grid->model()->whereIn('agent_id', $subAgentIds);
            } else {
                // 如果找不到对应的代理商，则不显示任何数据
                $grid->model()->where('id', 0);
            }
        } elseif ($userType == 3) { // 二级代理商
            // 二级代理商只能看到自己的区域配置
            $currentAgentId = $currentUser->agent_id;
            if ($currentAgentId) {
                $grid->model()->where('agent_id', $currentAgentId);
            } else {
                $grid->model()->where('id', 0);
            }
        }
        // 平台管理员和超级管理员可以看到所有数据，不需要额外过滤

        // 代理商基础信息
        $grid->column('agent.name', '代理商名称')->sortable();
        $grid->column('agent.contact_person', '联系人');
        $grid->column('agent.phone', '联系电话');

        // 区域信息
        $grid->column('full_region', '管理区域')->display(function () {
            /** @var \App\Models\AgentRegion $this */
            $region = [];
            if ($this->province_name) $region[] = $this->province_name;
            if ($this->city_name) $region[] = $this->city_name;
            if ($this->district_name) $region[] = $this->district_name;
            return implode(' - ', $region);
        })->help('显示省市区完整信息');

        // 区域状态
        $grid->column('region_status', '区域状态')->display(function ($status) {
            $statusMap = [
                'active' => '<span class="label label-success">正常</span>',
                'inactive' => '<span class="label label-warning">暂停</span>',
                'suspended' => '<span class="label label-danger">已暂停</span>',
                'terminated' => '<span class="label label-default">已终止</span>'
            ];
            return $statusMap[$status] ?? '<span class="label label-secondary">未知</span>';
        });

        // 独占状态
        $grid->column('is_exclusive', '是否独占')->display(function ($value) {
            return $value ? '<span class="label label-primary">独占</span>' : '<span class="label label-default">非独占</span>';
        });

        // 合同信息
        $grid->column('contract_status', '合约状态')->display(function ($status) {
            $statusMap = [
                'draft' => '<span class="label label-info">草稿</span>',
                'signed' => '<span class="label label-success">已签署</span>',
                'expired' => '<span class="label label-warning">已过期</span>',
                'terminated' => '<span class="label label-danger">已终止</span>'
            ];
            return $statusMap[$status] ?? '<span class="label label-secondary">未知</span>';
        });

        $grid->column('contract_no', '合约编号')->limit(20);

        // 返佣配置
        $grid->column('commission_rate', '返佣比例')->display(function ($rate) {
            return $rate ? $rate . '%' : '-';
        })->sortable();

        $grid->column('commission_type', '返佣类型')->display(function ($type) {
            $typeMap = [
                'percentage' => '按比例',
                'fixed' => '固定金额'
            ];
            return $typeMap[$type] ?? '-';
        });

        // 结算配置
        $grid->column('settlement_cycle', '结算周期')->display(function ($cycle) {
            $cycleMap = [
                'weekly' => '周结算',
                'monthly' => '月结算',
                'quarterly' => '季度结算'
            ];
            return $cycleMap[$cycle] ?? '-';
        });

        $grid->column('next_settlement_at', '下次结算')->date('Y-m-d');

        // 统计信息
        $grid->column('total_stores', '商铺数')->display(function ($total) {
            $active = $this->active_stores ?? 0;
            return $total ? "{$active}/{$total}" : '0/0';
        })->help('活跃/总数');

        $grid->column('total_revenue', '区域收入')->display(function ($revenue) {
            return $revenue ? '￥' . number_format($revenue, 2) : '￥0.00';
        })->sortable();

        $grid->column('pending_settlement_amount', '待结算金额')->display(function ($amount) {
            return $amount ? '￥' . number_format($amount, 2) : '￥0.00';
        })->sortable();

        // 时间信息
        $grid->column('signed_at', '签约日期')->date('Y-m-d');
        $grid->column('created_at', '创建时间')->datetime('Y-m-d H:i');

        // 筛选器 - 根据权限调整
        $grid->filter(function ($filter) use ($userType, $currentUser) {
            $filter->disableIdFilter();

            // 代理商筛选 - 根据权限调整选项
            if ($userType == 0 || $userType == 1) { // 超级管理员和平台管理员
                $filter->equal('agent_id', '代理商')->select(function () {
                    return Agent::where('status', 'active')->pluck('name', 'id');
                });
            } elseif ($userType == 2) { // 一级代理商
                $currentAgentId = $currentUser->agent_id;
                if ($currentAgentId) {
                    $subAgents = Agent::where('parent_agent_id', $currentAgentId)->pluck('name', 'id');
                    if ($subAgents->isNotEmpty()) {
                        $filter->equal('agent_id', '下级代理商')->select($subAgents->toArray());
                    }
                }
            }
            // 二级代理商不显示代理商筛选器，因为只能看到自己的数据

            // 区域状态筛选
            $filter->equal('region_status', '区域状态')->select([
                'active' => '正常',
                'inactive' => '暂停',
                'suspended' => '已暂停',
                'terminated' => '已终止'
            ]);

            // 合约状态筛选
            $filter->equal('contract_status', '合约状态')->select([
                'draft' => '草稿',
                'signed' => '已签署',
                'expired' => '已过期',
                'terminated' => '已终止'
            ]);

            // 省份筛选
            $filter->like('province_name', '省份');
            $filter->like('city_name', '城市');

            // 独占筛选
            $filter->equal('is_exclusive', '是否独占')->select([
                1 => '独占',
                0 => '非独占'
            ]);

            // 返佣类型筛选
            $filter->equal('commission_type', '返佣类型')->select([
                'percentage' => '按比例',
                'fixed' => '固定金额'
            ]);

            // 结算周期筛选
            $filter->equal('settlement_cycle', '结算周期')->select([
                'weekly' => '周结算',
                'monthly' => '月结算',
                'quarterly' => '季度结算'
            ]);

            // 时间筛选
            $filter->between('signed_at', '签约日期')->date();
            $filter->between('created_at', '创建时间')->datetime();
        });

        // 操作按钮 - 根据权限调整
        $grid->actions(function ($actions) use ($userType) {
            // 一级代理商和二级代理商只能查看，不能编辑和删除
            if ($userType == 2 || $userType == 3) {
                $actions->disableEdit();
                $actions->disableDelete();
            } else {
                $actions->disableDelete(); // 区域配置一般不删除，改为禁用状态

                // 获取当前行数据
                $row = $actions->row;

                // 添加自定义操作按钮 - 仅限平台管理员
                if ($row->region_status === 'active') {
                    $actions->append('<a href="javascript:void(0);" class="btn btn-xs btn-warning" onclick="pauseRegion(' . $row->id . ')"><i class="fa fa-pause"></i> 暂停</a>');
                } elseif ($row->region_status === 'inactive') {
                    $actions->append('<a href="javascript:void(0);" class="btn btn-xs btn-success" onclick="activateRegion(' . $row->id . ')"><i class="fa fa-play"></i> 激活</a>');
                }
            }

            // 所有用户都可以查看合约文件
            $row = $actions->row;
            if ($row->contract_file_url) {
                $actions->append('<a href="' . $row->contract_file_url . '" target="_blank" class="btn btn-xs btn-info"><i class="fa fa-file-pdf-o"></i> 查看合约</a>');
            }
        });

        // 批量操作 - 根据权限调整
        $grid->batchActions(function ($batch) use ($userType) {
            $batch->disableDelete();

            // 一级代理商和二级代理商不显示批量操作
            if ($userType == 2 || $userType == 3) {
                // 禁用所有批量操作
                $batch->disableDelete();
                // 不添加任何批量操作项
            } else {
                // 暂时注释批量操作，待创建相应的Action类后启用
                // $batch->add('批量暂停', new \App\Admin\Actions\BatchPauseRegion());
                // $batch->add('批量激活', new \App\Admin\Actions\BatchActivateRegion());
            }
        });

        // 工具栏 - 根据权限调整
        $grid->tools(function ($tools) use ($userType) {
            // 一级代理商和二级代理商不能新增区域配置
            if ($userType == 0 || $userType == 1) { // 只有超级管理员和平台管理员可以新增
                $tools->append('<a href="/admin/agent-regions/create" class="btn btn-sm btn-success"><i class="fa fa-plus"></i> 新增区域配置</a>');
            }
            // 代理商用户不显示新增按钮（不添加任何新增相关的工具）

            // API接口链接 - 仅限平台管理员
            if ($userType == 0 || $userType == 1) {
                $tools->append('<a href="/admin/agent-regions/api/list" target="_blank" class="btn btn-sm btn-info"><i class="fa fa-code"></i> API接口</a>');
            }
        });

        // 根据权限控制默认的新增按钮
        if ($userType == 2 || $userType == 3) {
            $grid->disableCreateButton();
        }

        // 设置每页显示数量
        $grid->paginate(20);

        // 默认排序
        $grid->model()->orderBy('created_at', 'desc');

        return $grid;
    }

    /**
     * 获取代理商区域配置API（支持外部调用）
     * 接口调用：GET /admin/agent-regions/api/list
     * 作者: lauJinyu
     * 
     * @api {get} /admin/agent-regions/api/list 获取代理商区域配置列表
     * @apiName GetAgentRegions
     * @apiGroup 代理商区域管理
     * @apiVersion 1.0.0
     * @apiDescription 获取代理商区域配置信息
     * 
     * @apiParam {String} [status] 代理商状态筛选
     * @apiParam {Integer} [agent_id] 代理商ID筛选
     * @apiParam {String} [province] 省份筛选
     * @apiParam {Boolean} [is_exclusive] 是否独占筛选
     * 
     * @apiSuccess {Integer} code 状态码
     * @apiSuccess {String} message 响应消息
     * @apiSuccess {Object} data 代理商区域数据
     * @apiSuccess {Array} data.regions 区域配置列表
     * @apiSuccess {Object} data.statistics 统计信息
     * 
     * @return \Illuminate\Http\JsonResponse
     */
    public function apiList()
    {
        try {
            $query = AgentRegion::with('agent');

            // 代理商状态筛选
            if (request('status')) {
                $query->whereHas('agent', function ($q) {
                    $q->where('status', request('status'));
                });
            }

            // 代理商ID筛选
            if (request('agent_id')) {
                $query->where('agent_id', request('agent_id'));
            }

            // 省份筛选
            if (request('province')) {
                $query->where('province', 'like', '%' . request('province') . '%');
            }

            // 是否独占筛选
            if (request('is_exclusive') !== null) {
                $query->where('is_exclusive', request('is_exclusive'));
            }

            $regions = $query->get()->map(function ($region) {
                return [
                    'id' => $region->id,
                    'agent_id' => $region->agent_id,
                    'agent_name' => $region->agent->name,
                    'agent_contact' => $region->agent->contact_person,
                    'agent_phone' => $region->agent->phone,
                    'province' => $region->province,
                    'city' => $region->city,
                    'district' => $region->district,
                    'full_region' => $region->full_region,
                    'is_exclusive' => $region->is_exclusive,
                    'is_exclusive_text' => $region->is_exclusive_text,
                    'remark' => $region->remark,
                    'created_at' => $region->created_at,
                ];
            });

            // 统计信息
            $totalRegions = AgentRegion::count();
            $exclusiveRegions = AgentRegion::where('is_exclusive', true)->count();
            $agentsWithRegions = AgentRegion::distinct('agent_id')->count('agent_id');

            $statistics = [
                'total_regions' => $totalRegions,
                'exclusive_regions' => $exclusiveRegions,
                'shared_regions' => $totalRegions - $exclusiveRegions,
                'agents_with_regions' => $agentsWithRegions,
            ];

            return response()->json([
                'code' => 200,
                'message' => '代理商区域配置获取成功',
                'data' => [
                    'regions' => $regions,
                    'statistics' => $statistics,
                ],
                'timestamp' => now()->toDateTimeString()
            ]);
        } catch (Throwable $e) {
            return response()->json([
                'code' => 500,
                'message' => '服务器错误: ' . $e->getMessage(),
                'data' => null,
                'timestamp' => now()->toDateTimeString()
            ], 500);
        }
    }

    /**
     * 获取区域层级数据API
     * 接口调用：GET /admin/agent-regions/api/areas
     * 
     * @api {get} /admin/agent-regions/api/areas 获取区域层级数据
     * @apiName GetAreas
     * @apiGroup 代理商区域管理
     * @apiVersion 1.0.0
     * @apiDescription 获取可用的区域层级数据
     * 
     * @apiParam {Integer} [level] 区域级别（1-省，2-市，3-区县）
     * @apiParam {Integer} [pid] 父级区域ID
     * 
     * @return \Illuminate\Http\JsonResponse
     */
    public function apiAreas()
    {
        try {
            $query = Area::query();

            if (request('level')) {
                $query->where('level', request('level'));
            }

            if (request('pid')) {
                $query->where('pid', request('pid'));
            }

            // 限制到区县级
            $query->where('level', '<=', 3);

            $areas = $query->orderBy('level')->orderBy('name')->get()->map(function ($area) {
                return [
                    'id' => $area->id,
                    'name' => $area->name,
                    'level' => $area->level,
                    'pid' => $area->pid,
                    'citycode' => $area->citycode ?? null,
                    'full_name' => $this->getAreaFullName($area),
                ];
            });

            return response()->json([
                'code' => 200,
                'message' => '区域数据获取成功',
                'data' => $areas,
                'timestamp' => now()->toDateTimeString()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '服务器错误: ' . $e->getMessage(),
                'data' => null,
                'timestamp' => now()->toDateTimeString()
            ], 500);
        }
    }

    /**
     * 获取区域完整名称
     * 
     * @param Area $area
     * @return string
     */
    private function getAreaFullName($area)
    {
        $names = [$area->name];
        $current = $area;

        while ($current->pid) {
            $parent = Area::find($current->pid);
            if ($parent) {
                $names[] = $parent->name;
                $current = $parent;
            } else {
                break;
            }
        }

        return implode(' ', array_reverse($names));
    }
}
