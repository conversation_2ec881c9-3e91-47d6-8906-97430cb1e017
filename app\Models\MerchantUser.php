<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MerchantUser extends Model
{
    use HasFactory;
    
    protected $table = 'merchant_users';
    
    protected $fillable = [
        'username',
        'password',
        'name',
        'phone',
        'email',
        'avatar',
        'status',
        'last_login_at',
        'last_login_ip',
        'permissions',
        'remark',
        'province_id',
        'city_id',
        'district_id',
        'address',
    ];
    
    protected $hidden = [
        'password',
        'remember_token',
    ];
    
    protected $casts = [
        'email_verified_at' => 'datetime',
        'last_login_at' => 'datetime',
        'permissions' => 'array',
        'password' => 'hashed',
    ];
    
    /**
     * 获取商家用户所在省份
     * <AUTHOR>
     */
    public function province()
    {
        return $this->belongsTo(Area::class, 'province_id', 'id');
    }
    
    /**
     * 获取商家用户所在城市
     * <AUTHOR>
     */
    public function city()
    {
        return $this->belongsTo(Area::class, 'city_id', 'id');
    }
    
    /**
     * 获取商家用户所在区县
     * <AUTHOR>
     */
    public function district()
    {
        return $this->belongsTo(Area::class, 'district_id', 'id');
    }
    
    /**
     * 获取商家用户完整地址信息
     * <AUTHOR>
     */
    public function getFullAddressAttribute()
    {
        $address = '';
        if ($this->province) {
            $address .= $this->province->name;
        }
        if ($this->city) {
            $address .= $this->city->name;
        }
        if ($this->district) {
            $address .= $this->district->name;
        }
        if ($this->address) {
            $address .= $this->address;
        }
        return $address;
    }
}
