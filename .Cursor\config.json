{"rules": ["This is a Laravel + Laravel-Admin NFC multimedia marketing platform backend project", "Admin panel pages are generated through Laravel-Admin, not involving frontend uni-app development standards", "Module menus are created through admin_menu table operations to establish module directory structure", "Merchants table has been deleted, unified use of stores table for shop management to avoid functional conflicts", "Database structure follows existing standards, prioritize JSON field extensions for functionality, submit impact assessment report for structural changes", "CRITICAL: Agent business configurations have been migrated to agent_regions table, deprecated business fields in agents table (commission_rate, settlement_cycle, signed_at etc.) are strictly prohibited for use in other modules", "New agent architecture: agents table only maintains basic information, agent_regions table serves as business configuration center, supporting one agent managing multiple regions", "Store management uses stores table, including NFC chip management, approval status, feature configuration", "API interfaces adopt RESTful design with unified JSON response format", "Permission system based on Laravel-Admin, supporting role and permission fine-grained management", "PERMISSION SYSTEM WORKFLOW: 1) Create permissions in admin_permissions table 2) Create roles in admin_roles table 3) Assign permissions to roles via admin_role_permissions 4) Assign roles to users via admin_role_users 5) Controller permission checks using PermissionService::hasPermission()", "PERMISSION NAMING CONVENTION: module.action format (e.g., agent.create, store.show, material.delete), supports hierarchical permissions with dot notation", "ROLE-BASED ACCESS CONTROL: Platform Admin (full access), Primary Agent (limited management), Secondary Agent (view-only), Operation Staff (specific modules only)", "PERMISSION IMPLEMENTATION STANDARD: All controller methods must check permissions, menu items require corresponding permissions, UI controls dynamically show/hide based on user permissions", "CRITICAL: <PERSON><PERSON><PERSON><PERSON><PERSON> DATABASE RELATIONSHIPS STUDY - Before any development work, developers MUST thoroughly study database_relationships.md to understand all table structures, foreign key relationships, data flow patterns, and permission design. This prevents architectural conflicts and ensures proper data handling.", "File uploads require security checks, supporting images, documents and other formats", "All business operations must record operation logs for audit tracking", "CRITICAL: Strictly prohibit arbitrary modification of .env environment configuration files! Must backup before modification, test in development environment before deployment", "CRITICAL RULE: Must understand module business logic and examine table structure before creating modules", "When writing code, write Chinese comments first, clarify logic before coding", "CRITICAL RULE: All interfaces must strictly follow ApiDoc format for interface annotations, including @api, @apiName, @apiGroup, @apiParam, @apiSuccess tags", "Bug handling process: Check syntax errors → routes → variable methods → model instantiation → version compatibility → database table fields → parameter format", "COMPLEX MODULE DEVELOPMENT STANDARD: List pages generated through Laravel-Admin system methods, create/edit pages through resources/views, data layer through controller API methods, view layer through Ajax+JavaScript data interaction", "Development workflow: 1) Create module directory in admin_menu table 2) Generate Laravel-Admin pages 3) Create API interface controller 4) Pages call API for data interaction", "Strictly follow data layer and view layer separation principle, Laravel-Admin only responsible for page display, business logic unified in API controller processing", "CRITICAL: Strictly prohibit database migration operations to avoid generating too many migration files, use sql commands for database operations", "CRITICAL: Database table operations strictly avoid table redundancy, use existing complete functional main tables", "Code author identified as <PERSON><PERSON><PERSON><PERSON><PERSON>, strictly adopt MVC architecture pattern, all code must add function-level comments", "Development environment: Windows system, VSCode + Intelephense, strict code formatting and type checking", "MODULE DEVELOPMENT FOCUS STANDARD: Focus on current module functionality implementation, prohibit unnecessary modifications to completed modules, detailed standards refer to project_rules.md", "DATA DEVELOPMENT PHASE STANDARD: Admin platform view layer development phase temporarily does not connect to real database data, all data returned in simulated form for page display and functionality debugging. Switch to real data sources uniformly during integration phase. Use development phase and integration phase comment identifiers for data source switching management", "LARAVEL ADMIN INTERFACE EXTERNAL CALL STANDARD: Admin controller interfaces support external debugging tools like Postman, responsibilities limited to backend data debugging, distinguished from business REST API", "STEP FORM DEVELOPMENT STANDARD: Complex forms adopt step wizard mode, implement step switching and data validation through step-wizard view + JavaScript + Ajax to ensure smooth user experience", "MVC ARCHITECTURE STRICT STANDARD: Controller layer strictly prohibits direct database operations, direct HTML code usage, direct echo statements, must process data through Model layer and render pages through View layer", "PSR-4 STANDARD STRICT COMPLIANCE: Controller and Model layers strictly prohibit direct HTML code and echo statements, all output must be processed through view layer to ensure code structure clarity and maintainability", "MODULE DEVELOPMENT WORKFLOW STANDARD: Non-list operation pages (detail pages, edit pages, add pages etc.) development order: 1) Create page views in resources/views first 2) Build corresponding Model 3) Create Controller 4) Implement data interaction logic", "DATA RENDERING SEPARATION STANDARD: All data rendering must be implemented through JavaScript, strictly ensure data layer and view layer separation, Controller only responsible for data processing and API response, View only responsible for HTML structure, JavaScript responsible for data binding and interaction", "LARAVEL ADMIN DEVELOPMENT GOLDEN RULES: Code separation (PHP/HTML/JS strict separation), dependency management (avoid circular dependencies), PJAX compatibility (support seamless page switching), debugging priority (comprehensive error handling and logging)"], "projectContext": {"type": "backend-admin", "framework": "<PERSON><PERSON> 9+", "admin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "database": "MySQL 8.0+", "cache": "Redis", "charts": "consoletvs/charts ^6.7", "features": {"completed": ["Chart extension package installation (supports multiple chart types)", "Database design and model creation (complete core business tables and models)", "Agent management module (100% complete: CRUD, province-city-district linkage, settlement configuration, file management, step form wizard)", "Agent region configuration module (100% complete: 5-step form, data echo, three-level linkage, complete business process)", "Controller basic architecture (all major controllers created, basic CRUD functionality complete)", "System infrastructure (Laravel-Admin configuration, routing, menu complete)", "Development environment configuration (IDE configuration, code standards, project structure optimized)", "Step form component (reusable step-wizard component, supports PJAX environment and data echo)"], "inProgress": ["Frontend interface optimization (85% complete: step form interface complete, some interaction and styling needs optimization)", "Data statistics functionality (60% complete: chart components installed, specific statistical logic needs improvement)", "File management system (75% complete: basic upload/download complete, needs optimization for preview and management)"], "planned": ["API interface development (provide interfaces for other endpoints)", "Permission system improvement (role and permission management)", "System integration (third-party service integration)", "Testing and deployment (automated testing and production environment deployment)"]}, "businessModules": {"agents": "Agent management (basic information + regional business configuration separation architecture, supports one agent managing multiple regions)", "agent_regions": "Agent region configuration (core business configuration center: contracts, commissions, settlements, remarks, 5-step form)", "areas": "Province-city-district management (three-level linkage, supports agent region association)", "stores": "Store management (platform unified management, supports forced unbinding, NFC chip management)", "materials": "Material library management (video, images, AI prompts and other multimedia materials)", "mediaConfigs": "Media configuration management (media platform configuration for H5 page display)", "salespersons": "Salesperson management (permission management, team relationships, performance statistics)", "recruitments": "Recruitment management (application approval, attribution allocation, source statistics)", "teams": "Team management (hierarchical structure, reward configuration, member management)", "statistics": "Data statistics center (financial reports, promotion effect analysis, real-time data)"}, "developmentPatterns": {"complexForms": {"approach": "Step wizard mode", "structure": "step-wizard.blade.php + steps/*.blade.php + JavaScript", "dataFlow": "getCompleteEditData() -> complete data one-time acquisition -> frontend direct filling", "stepLogic": "Dynamic step definition + Ajax content loading + frontend validation", "example": "Agent region configuration (5 steps: basic configuration → contract management → commission configuration → settlement configuration → remarks)"}, "viewLayerArchitecture": {"listPages": "Laravel-Admin system method generation (grid() + filters + batch operations)", "createEditPages": "resources/views custom views + Ajax data interaction + JavaScript logic", "dataLayer": "API controller methods implement business logic + unified JSON response format", "separation": "Strict data layer and view layer separation, Laravel-Admin only responsible for display"}, "dataBinding": {"newMode": "Blank form + three-level linkage selection", "editMode": "getCompleteEditData() one-time parsing + fillFormData() direct filling", "cascading": "Province → city → district three-level linkage + intelligent data parsing + delayed filling mechanism", "validation": "Frontend validation + backend API validation + unified error handling"}, "mvcArchitecture": {"controller": "Strictly prohibit direct database operations, HTML code, echo statements, only responsible for business logic and API response", "model": "Strictly prohibit HTML code and echo statements, only responsible for data model and database interaction", "view": "Only responsible for HTML structure and template rendering, does not contain business logic", "javascript": "Responsible for data rendering, user interaction, Ajax communication, implement data layer and view layer separation"}, "developmentOrder": {"detailPages": "1. Create resources/views view → 2. Build Model → 3. Create Controller → 4. Implement JavaScript data interaction", "listPages": "Laravel-Admin system method generation, follow grid() pattern", "apiEndpoints": "Controller provides API methods, JavaScript calls for data interaction", "errorHandling": "Frontend and backend dual error handling, comprehensive debugging logging"}, "permissionSystem": {"databaseTables": {"admin_permissions": "Permission definitions table, stores all available permissions with slug and name", "admin_roles": "Role definitions table, stores different user roles", "admin_role_permissions": "Many-to-many relationship between roles and permissions", "admin_role_users": "Many-to-many relationship between roles and users", "admin_menu": "Menu items table, each menu item has corresponding permission slug"}, "permissionNaming": {"format": "module.action (e.g., agent.create, store.show, material.delete)", "modules": ["agent", "store", "material", "salesperson", "team", "recruitment", "statistics", "system"], "actions": ["list", "create", "edit", "show", "delete", "manage", "config", "export", "import"], "examples": ["agent.list", "agent.create", "agent.edit", "agent.show", "agent.delete", "agent.manage"]}, "roleHierarchy": {"super-admin": "Super administrator with full system access", "platform-admin": "Platform administrator with business management permissions", "primary-agent": "Primary agent with limited management permissions for subordinates", "secondary-agent": "Secondary agent with view-only permissions for own data", "operation-staff": "Operation staff with specific module permissions"}, "implementationFlow": ["1. Create permissions in admin_permissions table with proper naming convention", "2. Create roles in admin_roles table with descriptive names", "3. Assign permissions to roles via admin_role_permissions table", "4. Assign roles to users via admin_role_users table", "5. Add permission checks in controller methods using PermissionService::hasPermission()", "6. Configure menu permissions in admin_menu table", "7. Implement UI permission controls in views and JavaScript"], "controllerImplementation": {"permissionCheck": "PermissionService::hasPermission(auth('admin')->user(), 'permission.slug')", "redirectOnFailure": "Redirect to unauthorized page with proper error message", "methodProtection": "All CRUD methods (index, create, store, show, edit, update, destroy) must check permissions", "apiEndpoints": "API methods must also implement permission checks for external access"}, "menuConfiguration": {"permissionSlug": "Each menu item must have corresponding permission slug", "hierarchicalPermissions": "Parent menu permissions automatically grant access to child menus", "dynamicMenus": "Menu items show/hide based on user permissions", "roleBasedMenus": "Different roles see different menu structures"}, "uiPermissionControls": {"buttonVisibility": "Create/Edit/Delete buttons show based on user permissions", "dataFiltering": "List data filtered based on user role and permissions", "formFieldAccess": "Form fields enabled/disabled based on user permissions", "bulkOperations": "Batch operations available only with proper permissions"}}}}, "codeStyle": {"php": {"standard": "PSR-12", "framework": "<PERSON><PERSON>", "linting": "PHPStan"}, "database": {"naming": "snake_case", "charset": "utf8mb4_unicode_ci", "engine": "InnoDB", "softDeletes": true}}, "development": {"environment": "PHPStudy Pro", "os": "Windows 10.0.19045", "shell": "PowerShell", "ide": "VSCode + Intelephense", "debugging": true, "logging": true, "testing": "PHPUnit", "apiTesting": {"tool": "Postman", "baseUrl": "http://localhost/admin", "collections": "Laravel Admin API Collection", "environment": "Laravel Admin API Environment"}, "mockData": {"enabled": true, "phase": "development", "identifiers": {"development": "Development phase - mock data", "production": "Integration phase - real data (to be enabled)", "description": "Data description - specific data structure and source description"}, "services": "app/Services/MockDataService.php", "files": "resources/mock/"}, "database": {"host": "127.0.0.1", "port": 3306, "database": "laravel", "username": "root", "password": "root"}}, "documentation": {"main": ".Cursor/rules/project_rules.md", "frontend": ".Cursor/rules/front-end.md", "database": ".Cursor/rules/laravel.sql", "databaseRelationships": "database_relationships.md", "apiDocFormat": "ApiDoc standard format, including @api, @apiName, @apiGroup and other 12 required tags"}, "qualityControl": {"bugHandlingSteps": ["1. Syntax check", "2. Route check", "3. Variable and method check", "4. Model and instantiation check", "5. System function and version check", "6. Database check", "7. Parameter format check"], "developmentProcess": ["1. Create module directory in admin_menu table", "2. <PERSON><PERSON>vel-Admin pages (list/form/detail)", "3. Create API interface controller (data layer)", "4. Add API methods supporting external calls (apiList, apiStore, apiUpdate etc.)", "5. Configure Postman test collections for interface-level debugging verification", "6. Pages call API for data interaction", "7. Use mock data for page debugging during development phase", "8. Switch to real data sources uniformly during integration phase"], "moduleDevRules": ["Focus on current module development, prohibit unnecessary modifications to completed modules", "Cross-module changes must undergo impact assessment analysis", "View layer modifications follow minimal change principle", "Emergency fixes require complete backup and testing procedures", "All code commits must pass code review checkpoints", "All interfaces must support external tool debugging verification like Postman", "Interface response format standardized (code, message, data, timestamp)"], "complexFormDevelopment": ["Complex forms adopt step wizard mode to improve user experience", "Step definitions dynamically configured through getStepDefinitions() method", "Edit mode parses all data at once through getCompleteEditData()", "Frontend fills directly through fillFormData(), avoiding multiple API calls", "Support PJAX environment, ensure normal page switching", "Three-level linkage implemented through intelligent data parsing and delayed filling", "All step content loaded dynamically through Ajax, supports error handling and retry"], "architectureChecklist": ["Controller layer check: prohibit direct database operations, HTML code, echo statements", "Model layer check: prohibit HTML code, echo statements, only responsible for data model", "View layer check: only contains HTML structure and template syntax, no business logic", "JavaScript check: responsible for data rendering and interaction, ensure data layer and view layer separation", "PSR-4 standard check: class naming, namespace, file structure compliance", "Development process check: non-list pages follow View → Model → Controller → JavaScript order", "PJAX compatibility check: JavaScript initialization supports multiple trigger methods", "Error handling check: comprehensive frontend and backend dual error handling mechanism"]}, "technicalDebt": {"resolved": ["settlement_cycle field database constraint error (fixed to nullable)", "Province-city-district three-level linkage rendering issue (resolved through URL pattern matching)", "File system NULL value exception (added conditional check)", "Form submission process optimization (improved JavaScript initialization logic)", "Agent table structure optimization (completed business configuration field migration to agent_regions table, supports one-to-many region management)", "agents table deprecated field cleanup (clarified deprecated field list, updated project specification documentation)", "Agent region configuration three-level linkage edit issue (perfectly resolved through getCompleteEditData+fillFormData solution)", "Step form data echo issue (implemented optimal solution of one-time acquisition and direct filling)", "JavaScript initialization timing issue (supports PJAX environment and multiple initialization trigger methods)", "Step configuration optimization (removed meaningless statistical information configuration step, optimized to 5-step process)", "Commission settlement detail page JavaScript syntax error (resolved through code separation: PHP/HTML/JS strict separation)", "PJAX environment JavaScript execution timing issue (resolved through multiple event listening and container waiting mechanism)", "Laravel Admin detail page architecture issue (established MVC strict separation standard development pattern)"], "currentIssues": [], "futureOptimizations": ["Query optimization and caching mechanism", "Data encryption and access control", "Load balancing and monitoring alerts", "Agent model fillable field cleanup (remove deprecated fields)", "Store, salesperson and other related module relationship table adjustments (adapt to new agent_regions architecture)", "Step form component encapsulation as reusable component"]}}