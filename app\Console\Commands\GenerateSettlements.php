<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\AgentRegion;
use App\Models\CommissionSettlement;
use App\Models\Store;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class GenerateSettlements extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'settlements:generate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '手动为代理商区域生成结算单';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('开始执行结算单生成任务...');
        Log::info('开始执行结算单生成任务...');

        $today = Carbon::today();

        $regions = AgentRegion::where('region_status', 'active')
            ->where('contract_status', 'signed')
            ->get();

        if ($regions->isEmpty()) {
            $this->info('今日无到期结算区域');
            Log::info('今日无到期结算区域');
            return 0;
        }

        $this->info("找到 " . $regions->count() . " 个今日到期的结算区域。");

        foreach ($regions as $region) {
            // 合约有效期、返佣有效期判断
            if (!$this->isRegionValidForSettlement($region, $today)) {
                continue;
            }

            DB::transaction(function () use ($region, $today) {
                // 1. 查询本周期未结算商铺（仅按 agent_id 统计，不再用 agent_region_id）
                $pendingStores = Store::where('agent_id', $region->agent_id)
                    ->where('settlement_status', 0)
                    ->get();

                $pendingCount = $pendingStores->count();
                $pendingAmount = $pendingStores->sum('product_amount');

                // 如果没有未结算商铺，直接返回
                if ($pendingCount == 0) {
                    return;
                }

                // 2. 佣金计算
                $commission = $this->calculateCommission($region, $pendingStores);

                // 3. 生成结算单
                $settlement = CommissionSettlement::create([
                    'settlement_no' => 'SET-' . date('YmdHis') . '-' . $region->agent_id . '-' . $region->id,
                    'target_type' => 'agent',
                    'target_id' => $region->agent_id,
                    'settlement_period_start' => $region->last_settlement_at ? Carbon::parse($region->last_settlement_at)->addDay()->toDateString() : ($region->signed_at ?? $region->created_at),
                    'settlement_period_end' => $today->toDateString(),
                    'total_amount' => $pendingAmount,
                    'commission_amount' => $commission,
                    'deduction_amount' => 0.00,
                    'actual_amount' => $commission,
                    'store_count' => $pendingCount,
                    'active_store_count' => $pendingCount,
                    'status' => 'pending',
                    'remark' => "手动生成（区域ID: {$region->id}）",
                ]);

                // 4. 批量更新商铺结算状态
                Store::whereIn('id', $pendingStores->pluck('id'))->update([
                    'settlement_status' => 1, // 1=待结算
                    'last_settlement_id' => $settlement->id,
                ]);

                // 5. 统计字段更新（仅按 agent_id 统计）
                $settledAmount = Store::where('agent_id', $region->agent_id)
                    ->where('settlement_status', 2)
                    ->sum('product_amount');
                $pendingAmountNew = Store::where('agent_id', $region->agent_id)
                    ->where('settlement_status', 1)
                    ->sum('product_amount');
                $totalRevenue = Store::where('agent_id', $region->agent_id)
                    ->sum('product_amount');

                // 6. 更新 agent_regions
                $region->update([
                    'total_settled_amount' => $settledAmount,
                    'pending_settlement_amount' => $pendingAmountNew,
                    'total_revenue' => $totalRevenue,
                    'last_settlement_at' => $today,
                    'next_settlement_at' => $this->calculateNextSettlementDate($today, $region),
                ]);

                $this->info("结算单生成成功：区域#{$region->id} 代理商#{$region->agent_id} 结算单号#{$settlement->settlement_no}");
                Log::info("结算单生成成功：区域#{$region->id} 代理商#{$region->agent_id} 结算单号#{$settlement->settlement_no}");
            });
        }

        $this->info('结算单生成任务执行完毕。');
        Log::info('结算单生成任务执行完毕。');
        return 0;
    }

    // 合约与返佣有效性判断
    private function isRegionValidForSettlement($region, $today)
    {
        if ($region->contract_status !== 'signed') return false;
        if ($region->contract_start_date && $today < $region->contract_start_date) return false;
        if ($region->contract_end_date && $today > $region->contract_end_date) return false;
        $start = $region->commission_effective_date ?? $region->contract_start_date;
        $end = $region->commission_expire_date ?? $region->contract_end_date;
        if ($start && $today < $start) return false;
        if ($end && $today > $end) return false;
        return true;
    }

    // 佣金计算
    private function calculateCommission($region, $pendingStores)
    {
        if ($pendingStores->isEmpty()) return 0.00;

        if ($region->commission_type === 'percentage') {
            $amount = $pendingStores->sum('product_amount');
            return round($amount * ($region->commission_rate / 100), 2);
        } elseif ($region->commission_type === 'fixed') {
            return round($pendingStores->count() * $region->commission_amount, 2);
        } elseif ($region->commission_type === 'tiered') {
            return $this->calcTieredCommission($pendingStores, $region->commission_rules);
        }
        return 0.00;
    }

    // 阶梯佣金计算
    private function calcTieredCommission($stores, $rules)
    {
        if (!$rules) return 0.00;
        $total = 0.00;
        $parsed = $this->parseTieredRules($rules);
        foreach ($stores as $store) {
            $amt = $store->product_amount;
            foreach ($parsed as $rule) {
                if (
                    ($rule['min'] === null || $amt >= $rule['min']) &&
                    ($rule['max'] === null || $amt <= $rule['max'])
                ) {
                    $total += round($amt * ($rule['rate'] / 100), 2);
                    break;
                }
            }
        }
        return $total;
    }

    // 解析阶梯规则字符串
    private function parseTieredRules($rules)
    {
        // 例：1-1000元5%；1001-5000元8%；5001元以上:10%
        $result = [];
        $parts = preg_split('/[；;]/u', $rules);
        foreach ($parts as $part) {
            if (preg_match('/(\d+)-(\d+)元[:：]?(\d+\.?\d*)%/', $part, $m)) {
                $result[] = [
                    'min' => floatval($m[1]),
                    'max' => floatval($m[2]),
                    'rate' => floatval($m[3]),
                ];
            } elseif (preg_match('/(\d+)元以上[:：]?(\d+\.?\d*)%/', $part, $m)) {
                $result[] = [
                    'min' => floatval($m[1]),
                    'max' => null,
                    'rate' => floatval($m[2]),
                ];
            }
        }
        return $result;
    }

    // 结算周期计算
    private function calculateNextSettlementDate($currentDate, $region)
    {
        if ($region->settlement_cycle) {
            switch ($region->settlement_cycle) {
                case 'weekly':
                    return Carbon::parse($currentDate)->addWeek()->toDateString();
                case 'monthly':
                    return Carbon::parse($currentDate)->addMonth()->toDateString();
                case 'quarterly':
                    return Carbon::parse($currentDate)->addMonths(3)->toDateString();
            }
        }
        // 若无周期，按结算日间隔
        if ($region->settlement_day) {
            return Carbon::parse($currentDate)->addDays($region->settlement_day)->toDateString();
        }
        // 默认下月
        return Carbon::parse($currentDate)->addMonth()->toDateString();
    }
}
