<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Validator;

/**
 * 媒体配置模型
 * 
 * @property int $id
 * @property int $merchant_id 商户ID
 * @property string $module_type 模块类型
 * @property array $config_data 配置数据
 * @property bool $is_enabled 是否启用
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Merchant $merchant
 */
class MediaConfig extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'merchant_id',
        'module_type',
        'config_data',
        'is_enabled',
    ];

    protected $casts = [
        'config_data' => 'array',
        'is_enabled' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 模块类型常量
     */
    const MODULE_VIDEO_PUBLISH = 'video_publish';
    const MODULE_REVIEW_CHECKIN = 'review_checkin';
    const MODULE_WECHAT_MARKETING = 'wechat_marketing';
    const MODULE_GROUP_BUYING = 'group_buying';
    const MODULE_FOLLOW_ACCOUNT = 'follow_account';
    const MODULE_WIFI_CONFIG = 'wifi_config';
    const MODULE_LOTTERY = 'lottery';

    /**
     * 获取模块类型选项
     */
    public static function getModuleTypeOptions()
    {
        return [
            self::MODULE_VIDEO_PUBLISH => '发视频配置',
            self::MODULE_REVIEW_CHECKIN => '点评打卡配置',
            self::MODULE_WECHAT_MARKETING => '微信营销配置',
            self::MODULE_GROUP_BUYING => '团购配置',
            self::MODULE_FOLLOW_ACCOUNT => '关注账号配置',
            self::MODULE_WIFI_CONFIG => 'WiFi配置',
            self::MODULE_LOTTERY => '抽奖配置',
        ];
    }

    /**
     * 获取默认配置结构
     */
    public static function getDefaultConfig($moduleType)
    {
        $configs = [
            self::MODULE_VIDEO_PUBLISH => [
                'platforms' => ['douyin', 'kuaishou', 'xiaohongshu'],
                'auto_publish' => false,
                'publish_time' => '09:00',
                'video_quality' => 'high',
                'add_watermark' => true,
                'watermark_position' => 'bottom_right',
                'tags' => [],
                'description_template' => '',
            ],
            self::MODULE_REVIEW_CHECKIN => [
                'platforms' => ['dianping', 'meituan'],
                'auto_reply' => true,
                'reply_template' => '感谢您的光临，期待下次再见！',
                'reward_points' => 10,
                'check_keywords' => ['好评', '推荐', '不错'],
                'notification_enabled' => true,
            ],
            self::MODULE_WECHAT_MARKETING => [
                'welcome_message' => '欢迎关注我们！',
                'auto_reply_enabled' => true,
                'menu_config' => [],
                'keywords' => [],
                'mass_message_enabled' => false,
                'customer_service_enabled' => true,
            ],
            self::MODULE_GROUP_BUYING => [
                'min_participants' => 2,
                'max_participants' => 10,
                'duration_hours' => 24,
                'discount_rate' => 0.8,
                'auto_refund' => true,
                'notification_enabled' => true,
            ],
            self::MODULE_FOLLOW_ACCOUNT => [
                'platforms' => ['wechat', 'weibo', 'douyin'],
                'reward_type' => 'points',
                'reward_amount' => 5,
                'verification_required' => true,
                'daily_limit' => 100,
            ],
            self::MODULE_WIFI_CONFIG => [
                'ssid' => '',
                'password' => '',
                'auth_type' => 'wpa2',
                'portal_enabled' => false,
                'portal_url' => '',
                'time_limit_minutes' => 120,
                'bandwidth_limit_mbps' => 10,
            ],
            self::MODULE_LOTTERY => [
                'prizes' => [],
                'daily_chances' => 1,
                'total_chances' => 10,
                'start_date' => null,
                'end_date' => null,
                'participation_rules' => '',
            ],
        ];

        return $configs[$moduleType] ?? [];
    }

    /**
     * 关联商户
     */
    public function merchant()
    {
        return $this->belongsTo(Merchant::class);
    }

    /**
     * 获取模块类型标签
     */
    public function getModuleTypeLabelAttribute()
    {
        return self::getModuleTypeOptions()[$this->module_type] ?? $this->module_type;
    }

    /**
     * 获取状态标签
     */
    public function getStatusLabelAttribute()
    {
        return $this->is_enabled ? '已启用' : '已禁用';
    }

    /**
     * 获取状态颜色
     */
    public function getStatusColorAttribute()
    {
        return $this->is_enabled ? 'success' : 'danger';
    }

    /**
     * 获取配置项数量
     */
    public function getConfigCountAttribute()
    {
        return is_array($this->config_data) ? count($this->config_data) : 0;
    }

    /**
     * 获取特定配置值
     */
    public function getConfigValue($key, $default = null)
    {
        return data_get($this->config_data, $key, $default);
    }

    /**
     * 设置特定配置值
     */
    public function setConfigValue($key, $value)
    {
        $configData = $this->config_data ?? [];
        data_set($configData, $key, $value);
        $this->config_data = $configData;
        return $this;
    }

    /**
     * 合并配置数据
     */
    public function mergeConfig(array $config)
    {
        $this->config_data = array_merge($this->config_data ?? [], $config);
        return $this;
    }

    /**
     * 重置为默认配置
     */
    public function resetToDefault()
    {
        $this->config_data = self::getDefaultConfig($this->module_type);
        return $this;
    }

    /**
     * 作用域：按商户筛选
     */
    public function scopeByMerchant($query, $merchantId)
    {
        return $query->where('merchant_id', $merchantId);
    }

    /**
     * 作用域：按模块类型筛选
     */
    public function scopeByModuleType($query, $moduleType)
    {
        return $query->where('module_type', $moduleType);
    }

    /**
     * 作用域：已启用
     */
    public function scopeEnabled($query)
    {
        return $query->where('is_enabled', true);
    }

    /**
     * 作用域：已禁用
     */
    public function scopeDisabled($query)
    {
        return $query->where('is_enabled', false);
    }

    /**
     * 启用配置
     */
    public function enable()
    {
        $this->update(['is_enabled' => true]);
        return $this;
    }

    /**
     * 禁用配置
     */
    public function disable()
    {
        $this->update(['is_enabled' => false]);
        return $this;
    }

    /**
     * 切换启用状态
     */
    public function toggleStatus()
    {
        $this->update(['is_enabled' => !$this->is_enabled]);
        return $this;
    }

    /**
     * 验证配置数据
     */
    public function validateConfig()
    {
        $rules = $this->getValidationRules();
        
        if (empty($rules)) {
            return true;
        }

        $validator = Validator::make($this->config_data ?? [], $rules);
        return $validator->passes();
    }

    /**
     * 获取验证规则
     */
    private function getValidationRules()
    {
        $rules = [
            self::MODULE_VIDEO_PUBLISH => [
                'platforms' => 'array',
                'auto_publish' => 'boolean',
                'publish_time' => 'string',
                'video_quality' => 'in:low,medium,high',
            ],
            self::MODULE_REVIEW_CHECKIN => [
                'platforms' => 'array',
                'auto_reply' => 'boolean',
                'reward_points' => 'integer|min:0',
            ],
            self::MODULE_WECHAT_MARKETING => [
                'welcome_message' => 'string|max:500',
                'auto_reply_enabled' => 'boolean',
            ],
            self::MODULE_GROUP_BUYING => [
                'min_participants' => 'integer|min:1',
                'max_participants' => 'integer|min:2',
                'duration_hours' => 'integer|min:1',
                'discount_rate' => 'numeric|between:0,1',
            ],
            self::MODULE_FOLLOW_ACCOUNT => [
                'platforms' => 'array',
                'reward_amount' => 'integer|min:0',
                'daily_limit' => 'integer|min:1',
            ],
            self::MODULE_WIFI_CONFIG => [
                'ssid' => 'required|string|max:32',
                'password' => 'string|min:8',
                'time_limit_minutes' => 'integer|min:1',
            ],
            self::MODULE_LOTTERY => [
                'daily_chances' => 'integer|min:1',
                'total_chances' => 'integer|min:1',
            ],
        ];

        return $rules[$this->module_type] ?? [];
    }

    /**
     * 获取配置摘要
     */
    public function getConfigSummaryAttribute()
    {
        if (!$this->config_data) {
            return '未配置';
        }

        $summary = [];
        
        switch ($this->module_type) {
            case self::MODULE_VIDEO_PUBLISH:
                $platforms = $this->getConfigValue('platforms', []);
                $summary[] = '平台: ' . implode(', ', $platforms);
                break;
                
            case self::MODULE_REVIEW_CHECKIN:
                $points = $this->getConfigValue('reward_points', 0);
                $summary[] = '奖励积分: ' . $points;
                break;
                
            case self::MODULE_GROUP_BUYING:
                $min = $this->getConfigValue('min_participants', 0);
                $max = $this->getConfigValue('max_participants', 0);
                $summary[] = "参与人数: {$min}-{$max}人";
                break;
                
            case self::MODULE_WIFI_CONFIG:
                $ssid = $this->getConfigValue('ssid', '');
                $summary[] = 'SSID: ' . ($ssid ?: '未设置');
                break;
        }

        return implode(', ', $summary) ?: '已配置';
    }

    /**
     * 复制配置到其他商户
     */
    public function copyToMerchant($targetMerchantId)
    {
        return self::create([
            'merchant_id' => $targetMerchantId,
            'module_type' => $this->module_type,
            'config_data' => $this->config_data,
            'is_enabled' => false, // 默认禁用，需要手动启用
        ]);
    }
}