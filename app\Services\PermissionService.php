<?php

namespace App\Services;

use Encore\Admin\Auth\Database\Administrator;
use App\Models\Agent;
use App\Models\Store;
use App\Models\Salesperson;
use App\Models\Material;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class PermissionService
{
    // 用户类型常量
    const USER_TYPE_PLATFORM_ADMIN = 1;    // 平台管理员
    const USER_TYPE_PRIMARY_AGENT = 2;     // 一级代理商
    const USER_TYPE_SECONDARY_AGENT = 3;   // 二级代理商

    /**
     * 获取当前用户类型
     */
    public static function getCurrentUserType(): int
    {
        $user = Auth::guard('admin')->user();
        return $user ? ($user->user_type ?? self::USER_TYPE_PLATFORM_ADMIN) : self::USER_TYPE_PLATFORM_ADMIN;
    }

    /**
     * 获取当前用户关联的代理商
     */
    public static function getCurrentAgent(): ?Agent
    {
        $user = Auth::guard('admin')->user();
        return $user && $user->agent_id ? Agent::find($user->agent_id) : null;
    }

    /**
     * 检查用户是否为平台管理员
     */
    public static function isPlatformAdmin(): bool
    {
        return self::getCurrentUserType() === self::USER_TYPE_PLATFORM_ADMIN;
    }

    /**
     * 检查用户是否为一级代理商
     */
    public static function isPrimaryAgent(): bool
    {
        return self::getCurrentUserType() === self::USER_TYPE_PRIMARY_AGENT;
    }

    /**
     * 检查用户是否为二级代理商
     */
    public static function isSecondaryAgent(): bool
    {
        return self::getCurrentUserType() === self::USER_TYPE_SECONDARY_AGENT;
    }

    /**
     * 获取用户可访问的代理商ID列表
     */
    public static function getAccessibleAgentIds(): array
    {
        $userType = self::getCurrentUserType();
        $currentAgent = self::getCurrentAgent();

        switch ($userType) {
            case self::USER_TYPE_PLATFORM_ADMIN:
                // 平台管理员可以看到所有代理商
                return Agent::pluck('id')->toArray();

            case self::USER_TYPE_PRIMARY_AGENT:
                if (!$currentAgent) return [];
                // 一级代理商可以看到自己和下级代理商
                $agentIds = [$currentAgent->id];
                $subAgentIds = $currentAgent->subAgents()->pluck('id')->toArray();
                return array_merge($agentIds, $subAgentIds);

            case self::USER_TYPE_SECONDARY_AGENT:
                if (!$currentAgent) return [];
                // 二级代理商只能看到自己
                return [$currentAgent->id];

            default:
                return [];
        }
    }

    /**
     * 获取用户可访问的商铺ID列表
     */
    public static function getAccessibleStoreIds(): array
    {
        $userType = self::getCurrentUserType();
        $currentAgent = self::getCurrentAgent();

        switch ($userType) {
            case self::USER_TYPE_PLATFORM_ADMIN:
                // 平台管理员可以看到所有商铺
                return Store::pluck('id')->toArray();

            case self::USER_TYPE_PRIMARY_AGENT:
                if (!$currentAgent) return [];
                // 一级代理商可以看到自己和下级代理商的商铺
                $agentIds = self::getAccessibleAgentIds();
                return Store::whereIn('agent_id', $agentIds)->pluck('id')->toArray();

            case self::USER_TYPE_SECONDARY_AGENT:
                if (!$currentAgent) return [];
                // 二级代理商只能看到自己的商铺
                return Store::where('agent_id', $currentAgent->id)->pluck('id')->toArray();

            default:
                return [];
        }
    }

    /**
     * 检查用户是否拥有指定权限
     */
    public static function hasPermission(string $permission): bool
    {
        $user = auth('admin')->user();
        if (!$user) return false;

        // 平台管理员拥有所有权限
        if (self::isPlatformAdmin()) {
            return true;
        }

        // 检查用户是否拥有该权限
        return $user->can($permission);
    }

    /**
     * 获取用户可访问的推广员ID列表
     */
    public static function getAccessibleSalespersonIds(): array
    {
        $userType = self::getCurrentUserType();
        $currentAgent = self::getCurrentAgent();

        switch ($userType) {
            case self::USER_TYPE_PLATFORM_ADMIN:
                // 平台管理员可以看到所有推广员
                return Salesperson::pluck('id')->toArray();

            case self::USER_TYPE_PRIMARY_AGENT:
                if (!$currentAgent) return [];
                // 一级代理商可以看到自己和下级代理商的推广员
                $agentIds = self::getAccessibleAgentIds();
                return Salesperson::whereIn('agent_id', $agentIds)->pluck('id')->toArray();

            case self::USER_TYPE_SECONDARY_AGENT:
                if (!$currentAgent) return [];
                // 二级代理商只能看到自己的推广员
                return Salesperson::where('agent_id', $currentAgent->id)->pluck('id')->toArray();

            default:
                return [];
        }
    }

    /**
     * 获取用户可访问的素材ID列表
     */
    public static function getAccessibleMaterialIds(): array
    {
        $userType = self::getCurrentUserType();
        $currentAgent = self::getCurrentAgent();

        switch ($userType) {
            case self::USER_TYPE_PLATFORM_ADMIN:
                // 平台管理员可以看到所有素材
                return Material::pluck('id')->toArray();

            case self::USER_TYPE_PRIMARY_AGENT:
            case self::USER_TYPE_SECONDARY_AGENT:
                if (!$currentAgent) return [];
                // 代理商可以看到平台素材和自己上传的素材
                return Material::where(function ($query) use ($currentAgent) {
                    $query->where('uploader_type', 'admin')
                        ->orWhere(function ($subQuery) use ($currentAgent) {
                            $subQuery->where('uploader_type', 'agent')
                                ->where('uploader_id', $currentAgent->id);
                        });
                })->pluck('id')->toArray();

            default:
                return [];
        }
    }

    /**
     * 过滤代理商查询
     */
    public static function filterAgentQuery(Builder $query): Builder
    {
        $userType = self::getCurrentUserType();
        $currentAgent = self::getCurrentAgent();

        switch ($userType) {
            case self::USER_TYPE_PLATFORM_ADMIN:
                // 平台管理员不需要过滤
                return $query;

            case self::USER_TYPE_PRIMARY_AGENT:
                if (!$currentAgent) return $query->whereRaw('1=0'); // 无数据
                // 一级代理商看到自己和下级
                return $query->where(function ($q) use ($currentAgent) {
                    $q->where('id', $currentAgent->id)
                        ->orWhere('parent_agent_id', $currentAgent->id);
                });

            case self::USER_TYPE_SECONDARY_AGENT:
                if (!$currentAgent) return $query->whereRaw('1=0');
                // 二级代理商只能看到自己
                return $query->where('id', $currentAgent->id);

            default:
                return $query->whereRaw('1=0');
        }
    }

    /**
     * 过滤商铺查询
     */
    public static function filterStoreQuery(Builder $query): Builder
    {
        $agentIds = self::getAccessibleAgentIds();

        if (empty($agentIds)) {
            return $query->whereRaw('1=0'); // 无数据
        }

        return $query->whereIn('agent_id', $agentIds);
    }

    /**
     * 过滤业务员查询
     */
    public static function filterSalespersonQuery(Builder $query): Builder
    {
        $agentIds = self::getAccessibleAgentIds();

        if (empty($agentIds)) {
            return $query->whereRaw('1=0'); // 无数据
        }

        return $query->whereIn('agent_id', $agentIds);
    }

    /**
     * 过滤素材查询
     */
    public static function filterMaterialQuery(Builder $query): Builder
    {
        $userType = self::getCurrentUserType();
        $currentAgent = self::getCurrentAgent();

        switch ($userType) {
            case self::USER_TYPE_PLATFORM_ADMIN:
                // 平台管理员可以看到所有素材
                return $query;

            case self::USER_TYPE_PRIMARY_AGENT:
            case self::USER_TYPE_SECONDARY_AGENT:
                // 代理商可以看到公开素材和自己上传的素材
                return $query->where(function ($q) use ($currentAgent) {
                    $q->where('is_public', 1)
                        ->orWhere(function ($subQ) use ($currentAgent) {
                            $subQ->where('uploader_type', 'agent')
                                ->where('uploader_id', $currentAgent ? $currentAgent->id : 0);
                        });
                });

            default:
                return $query->where('is_public', 1); // 只能看公开素材
        }
    }

    /**
     * 检查用户是否可以访问指定的代理商
     */
    public static function canAccessAgent(int $agentId): bool
    {
        return in_array($agentId, self::getAccessibleAgentIds());
    }

    /**
     * 检查用户是否可以访问指定的商铺
     */
    public static function canAccessStore(int $storeId): bool
    {
        return in_array($storeId, self::getAccessibleStoreIds());
    }

    /**
     * 检查用户是否有权限执行特定操作
     */
    public static function canPerformAction(string $action, ?int $targetId = null): bool
    {
        $userType = self::getCurrentUserType();
        $currentAgent = self::getCurrentAgent();

        // 平台管理员拥有所有权限
        if ($userType === self::USER_TYPE_PLATFORM_ADMIN) {
            return true;
        }

        // 根据操作类型检查权限
        switch ($action) {
            case 'create_agent':
                // 只有一级代理商可以创建下级代理商
                return $userType === self::USER_TYPE_PRIMARY_AGENT &&
                    $currentAgent && $currentAgent->canAddSubAgent();

            case 'edit_agent':
                // 代理商只能编辑自己和下级的信息
                return $targetId && self::canAccessAgent($targetId);

            case 'delete_agent':
                // 只有平台管理员可以删除代理商
                return false;

            case 'create_store':
                // 代理商可以创建商铺（在配额内）
                return $currentAgent && $currentAgent->canAddDirectStore();

            case 'edit_store':
                // 可以编辑自己权限范围内的商铺
                return $targetId && self::canAccessStore($targetId);

            case 'view_statistics':
                // 代理商可以查看自己权限范围内的统计
                return true;

            case 'export_data':
                // 根据角色限制导出权限
                return $userType === self::USER_TYPE_PRIMARY_AGENT;

            default:
                return false;
        }
    }

    /**
     * 获取用户角色标签
     */
    public static function getUserTypeLabel(?int $userType = null): string
    {
        $userType = $userType ?? self::getCurrentUserType();

        $labels = [
            self::USER_TYPE_PLATFORM_ADMIN => '平台管理员',
            self::USER_TYPE_PRIMARY_AGENT => '一级代理商',
            self::USER_TYPE_SECONDARY_AGENT => '二级代理商',
        ];

        return $labels[$userType] ?? '未知角色';
    }

    /**
     * 获取用户数据权限配置
     */
    public static function getDataPermissions(): array
    {
        $user = Auth::guard('admin')->user();
        $permissions = $user->data_permissions ?? [];

        // 如果是数组格式，直接返回；如果是JSON字符串，解析后返回
        return is_array($permissions) ? $permissions : json_decode($permissions, true) ?? [];
    }

    /**
     * 设置用户数据权限配置
     */
    public static function setDataPermissions(array $permissions): bool
    {
        $user = Auth::guard('admin')->user();
        if (!$user) return false;

        return DB::table('admin_users')
            ->where('id', $user->id)
            ->update(['data_permissions' => json_encode($permissions)]);
    }

    /**
     * 获取当前用户信息摘要
     */
    public static function getCurrentUserInfo(): array
    {
        $user = auth('admin')->user();
        $agent = self::getCurrentAgent();

        return [
            'user_id' => $user ? $user->id : null,
            'username' => $user ? $user->username : null,
            'user_type' => self::getCurrentUserType(),
            'agent_id' => $agent ? $agent->id : null,
            'agent_name' => $agent ? $agent->name : null,
            'agent_level' => $agent ? $agent->level : null,
            'is_platform_admin' => self::isPlatformAdmin(),
            'is_primary_agent' => self::isPrimaryAgent(),
            'is_secondary_agent' => self::isSecondaryAgent(),
        ];
    }
}
