<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class CreateAdminUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'admin:create {username=admin} {name=超级管理员} {password=123456}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '创建管理员账户并分配超级管理员权限';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $username = $this->argument('username');
        $name = $this->argument('name');
        $password = $this->argument('password');

        // 检查用户是否已存在
        $existingUser = DB::table('admin_users')->where('username', $username)->first();
        
        if ($existingUser) {
            $this->info('用户已存在，正在为其分配权限...');
            $userId = $existingUser->id;
        } else {
            // 创建新用户
            $userId = DB::table('admin_users')->insertGetId([
                'username' => $username,
                'password' => Hash::make($password),
                'name' => $name,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
            $this->info('管理员账户创建成功！');
        }

        // 确保超级管理员角色存在
        $roleId = DB::table('admin_roles')->where('slug', 'administrator')->value('id');
        if (!$roleId) {
            $roleId = DB::table('admin_roles')->insertGetId([
                'name' => 'Administrator',
                'slug' => 'administrator',
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        // 确保全部权限存在
        $permissionId = DB::table('admin_permissions')->where('slug', '*')->value('id');
        if (!$permissionId) {
            $permissionId = DB::table('admin_permissions')->insertGetId([
                'name' => 'All permission',
                'slug' => '*',
                'http_method' => '',
                'http_path' => '*',
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        // 为用户分配角色
        DB::table('admin_role_users')->updateOrInsert(
            ['user_id' => $userId, 'role_id' => $roleId],
            ['created_at' => now(), 'updated_at' => now()]
        );

        // 为角色分配权限
        DB::table('admin_role_permissions')->updateOrInsert(
            ['role_id' => $roleId, 'permission_id' => $permissionId],
            ['created_at' => now(), 'updated_at' => now()]
        );

        // 创建基础菜单（如果不存在）
        $this->createBasicMenus();

        $this->info('用户名: ' . $username);
        $this->info('密码: ' . $password);
        $this->info('姓名: ' . $name);
        $this->info('用户ID: ' . $userId);
        $this->info('权限分配完成！');

        return Command::SUCCESS;
    }

    private function createBasicMenus()
    {
        $menus = [
            ['id' => 1, 'parent_id' => 0, 'order' => 1, 'title' => 'Dashboard', 'icon' => 'fa-bar-chart', 'uri' => '/', 'permission' => null],
            ['id' => 2, 'parent_id' => 0, 'order' => 2, 'title' => 'Admin', 'icon' => 'fa-tasks', 'uri' => '', 'permission' => null],
            ['id' => 3, 'parent_id' => 2, 'order' => 3, 'title' => 'Users', 'icon' => 'fa-users', 'uri' => 'auth/users', 'permission' => null],
            ['id' => 4, 'parent_id' => 2, 'order' => 4, 'title' => 'Roles', 'icon' => 'fa-user', 'uri' => 'auth/roles', 'permission' => null],
            ['id' => 5, 'parent_id' => 2, 'order' => 5, 'title' => 'Permission', 'icon' => 'fa-ban', 'uri' => 'auth/permissions', 'permission' => null],
            ['id' => 6, 'parent_id' => 2, 'order' => 6, 'title' => 'Menu', 'icon' => 'fa-bars', 'uri' => 'auth/menu', 'permission' => null],
            ['id' => 7, 'parent_id' => 2, 'order' => 7, 'title' => 'Operation log', 'icon' => 'fa-history', 'uri' => 'auth/logs', 'permission' => null],
        ];

        foreach ($menus as $menu) {
            DB::table('admin_menu')->updateOrInsert(
                ['id' => $menu['id']],
                array_merge($menu, ['created_at' => now(), 'updated_at' => now()])
            );
        }
    }
}
