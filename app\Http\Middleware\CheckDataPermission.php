<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Services\PermissionService;

class CheckDataPermission
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // 检查是否为管理后台请求
        if ($request->is('admin/*')) {
            $user = auth('admin')->user();
            
            if (!$user) {
                return redirect()->route('admin.login');
            }
            
            // 将用户权限信息注入到请求中，供控制器使用
            $request->merge([
                '_user_info' => PermissionService::getCurrentUserInfo(),
                '_accessible_agent_ids' => PermissionService::getAccessibleAgentIds(),
                '_accessible_store_ids' => PermissionService::getAccessibleStoreIds(),
            ]);
        }
        
        return $next($request);
    }
}