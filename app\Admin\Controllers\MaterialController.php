<?php

namespace App\Admin\Controllers;

use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;
use Encore\Admin\Layout\Content;
use App\Models\Material;
use App\Services\PermissionService;

class MaterialController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = '素材库管理';

    /**
     * 素材列表页面
     */
    public function index(Content $content)
    {
        // 检查权限
        if (!PermissionService::hasPermission('material.list')) {
            abort(403, '您没有权限访问素材库');
        }

        return $content
            ->title($this->title)
            ->description('素材库列表')
            ->body($this->grid());
    }

    /**
     * 素材详情页面
     */
    public function show($id, Content $content)
    {
        // 检查权限
        if (!PermissionService::hasPermission('material.show')) {
            abort(403, '您没有权限查看素材详情');
        }

        return $content
            ->title('素材详情')
            ->description('查看素材详细信息')
            ->body($this->detail($id));
    }

    /**
     * 创建素材页面
     */
    public function create(Content $content)
    {
        // 检查权限
        if (!PermissionService::hasPermission('material.create')) {
            abort(403, '您没有权限上传素材');
        }

        return $content
            ->title('上传素材')
            ->description('添加新的素材')
            ->body($this->form());
    }

    /**
     * 编辑素材页面
     */
    public function edit($id, Content $content)
    {
        // 检查权限
        if (!PermissionService::hasPermission('material.edit')) {
            abort(403, '您没有权限编辑素材');
        }

        return $content
            ->title('编辑素材')
            ->description('修改素材信息')
            ->body($this->form()->edit($id));
    }

    /**
     * 存储素材
     */
    public function store()
    {
        // 检查权限
        if (!PermissionService::hasPermission('material.create')) {
            abort(403, '您没有权限上传素材');
        }

        return $this->form()->store();
    }

    /**
     * 更新素材
     */
    public function update($id)
    {
        // 检查权限
        if (!PermissionService::hasPermission('material.edit')) {
            abort(403, '您没有权限编辑素材');
        }

        return $this->form()->update($id);
    }

    /**
     * 删除素材
     */
    public function destroy($id)
    {
        // 检查权限
        if (!PermissionService::hasPermission('material.delete')) {
            abort(403, '您没有权限删除素材');
        }

        return $this->form()->destroy($id);
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new Material());

        // 根据用户角色过滤数据 - 素材管理的权限控制
        $userType = PermissionService::getCurrentUserType();
        $currentAgent = PermissionService::getCurrentAgent();

        if (!PermissionService::isPlatformAdmin()) {
            // 代理商只能看到公开素材和自己上传的素材
            $grid->model()->where(function ($query) use ($currentAgent) {
                $query->where('is_public', true)  // 公开素材
                    ->orWhere('uploaded_by', auth('admin')->id()) // 自己上传的
                    ->orWhere('agent_id', $currentAgent ? $currentAgent->id : null); // 同代理商的
            });
        }

        $grid->column('id', __('ID'));
        $grid->column('title', __('素材标题'));
        $grid->column('type', __('素材类型'))->using([
            'video' => '视频',
            'image' => '图片',
            'text' => '文案',
            'ai_prompt' => 'AI提示词'
        ])->label([
            'video' => 'primary',
            'image' => 'success',
            'text' => 'info',
            'ai_prompt' => 'warning'
        ]);
        $grid->column('category', __('分类'));
        $grid->column('tags', __('标签'));
        $grid->column('applicable_modules', __('适用模块'));
        $grid->column('file_path', __('文件'))->display(function ($path) {
            /** @var Material $this */
            if ($this->type == 'image') {
                return "<img src='$path' style='max-width:100px;max-height:60px;'>";
            } elseif ($this->type == 'video') {
                return "<video src='$path' style='max-width:100px;max-height:60px;' controls></video>";
            }
            return $path;
        });
        $grid->column('audit_status', __('审核状态'))->using([
            'pending' => '待审核',
            'approved' => '已通过',
            'rejected' => '已驳回'
        ])->label([
            'pending' => 'warning',
            'approved' => 'success',
            'rejected' => 'danger'
        ]);
        $grid->column('created_at', __('创建时间'));
        $grid->column('updated_at', __('更新时间'));

        $grid->filter(function ($filter) {
            $filter->like('title', '标题');
            $filter->equal('type', '类型')->select([
                'video' => '视频',
                'image' => '图片',
                'text' => '文案',
                'ai_prompt' => 'AI提示词'
            ]);
            $filter->equal('audit_status', '审核状态')->select([
                'pending' => '待审核',
                'approved' => '已通过',
                'rejected' => '已驳回'
            ]);
        });

        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(Material::findOrFail($id));

        $show->field('id', __('ID'));
        $show->field('title', __('素材标题'));
        $show->field('type', __('素材类型'));
        $show->field('category', __('分类'));
        $show->field('tags', __('标签'));
        $show->field('description', __('描述'));
        $show->field('file_path', __('文件路径'));
        $show->field('file_size', __('文件大小'));
        $show->field('applicable_modules', __('适用模块'));
        $show->field('audit_status', __('审核状态'));
        $show->field('audit_remark', __('审核备注'));
        $show->field('created_at', __('创建时间'));
        $show->field('updated_at', __('更新时间'));

        return $show;
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new Material());

        $form->text('title', __('素材标题'))->required();
        $form->select('type', __('素材类型'))->options([
            'video' => '视频',
            'image' => '图片',
            'text' => '文案',
            'ai_prompt' => 'AI提示词'
        ])->required();
        $form->text('category', __('分类'));
        $form->tags('tags', __('标签'));
        $form->textarea('description', __('描述'));

        $form->when('type', 'video', function (Form $form) {
            $form->file('file_path', __('视频文件'))->rules('mimes:mp4,avi,mov,wmv');
        });

        $form->when('type', 'image', function (Form $form) {
            $form->image('file_path', __('图片文件'));
        });

        $form->when('type', 'text', function (Form $form) {
            $form->textarea('content', __('文案内容'));
        });

        $form->when('type', 'ai_prompt', function (Form $form) {
            $form->textarea('content', __('AI提示词'));
        });

        $form->checkbox('applicable_modules', __('适用模块'))->options([
            'video_publish' => '发视频',
            'review_checkin' => '点评打卡',
            'wechat_marketing' => '微信营销',
            'group_buy' => '团购',
            'follow_account' => '关注账号'
        ]);

        // 只有平台管理员可以设置审核状态
        if (PermissionService::isPlatformAdmin()) {
            $form->select('audit_status', __('审核状态'))->options([
                'pending' => '待审核',
                'approved' => '已通过',
                'rejected' => '已驳回'
            ])->default('pending');

            $form->textarea('audit_remark', __('审核备注'));
        }

        // 自动设置上传者和代理商信息
        $form->saving(function (Form $form) {
            if (!$form->model()->exists) {
                $form->model()->uploaded_by = auth('admin')->id();
                $currentAgent = PermissionService::getCurrentAgent();
                if ($currentAgent) {
                    $form->model()->agent_id = $currentAgent->id;
                }
            }
        });

        return $form;
    }
}
