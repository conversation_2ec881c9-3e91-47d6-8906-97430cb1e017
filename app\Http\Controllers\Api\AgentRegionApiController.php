<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Agent;
use App\Models\Area;
use Illuminate\Http\Request;
use Exception;

/**
 * 代理商区域配置API控制器（无需认证）
 * 
 * 提供外部系统调用的代理商区域配置接口
 * 
 * <AUTHOR>
 */
class AgentRegionApiController extends Controller
{
    /**
     * 获取代理商区域配置列表
     * 接口调用：GET /api/agent-regions/list
     * 
     * @api {get} /api/agent-regions/list 获取代理商区域配置列表
     * @apiName GetAgentRegionsList
     * @apiGroup 代理商区域管理API
     * @apiVersion 1.0.0
     * @apiDescription 获取代理商区域配置信息（无需认证）
     * 
     * @apiParam {String} [status] 代理商状态筛选（active|inactive|disabled）
     * @apiParam {Integer} [has_region] 是否已配置区域（1-是，0-否）
     * @apiParam {Integer} [limit] 每页数量，默认20
     * @apiParam {Integer} [page] 页码，默认1
     * 
     * @apiSuccess {Integer} code 状态码（200表示成功）
     * @apiSuccess {String} message 响应消息
     * @apiSuccess {Object} data 代理商区域数据
     * @apiSuccess {Array} data.agents 代理商列表
     * @apiSuccess {Object} data.statistics 统计信息
     * @apiSuccess {Object} data.pagination 分页信息
     * @apiSuccess {String} timestamp 响应时间戳
     * 
     * @apiSuccessExample {json} 成功响应:
     * {
     *   "code": 200,
     *   "message": "代理商区域配置获取成功",
     *   "data": {
     *     "agents": [
     *       {
     *         "id": 1,
     *         "name": "张三代理",
     *         "contact_person": "张三",
     *         "phone": "13800138001",
     *         "region_code": "440100",
     *         "region_name": "广州市",
     *         "region_info": "广东省 → 广州市",
     *         "status": "active",
     *         "has_region": true
     *       }
     *     ],
     *     "statistics": {
     *       "total_agents": 10,
     *       "configured_agents": 6,
     *       "unconfigured_agents": 4,
     *       "configuration_rate": 60.0
     *     }
     *   },
     *   "timestamp": "2024-12-19 16:30:00"
     * }
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function list(Request $request)
    {
        try {
            $query = Agent::where('status', '!=', 'deleted');
            
            // 状态筛选
            if ($request->status) {
                $query->where('status', $request->status);
            }
            
            // 区域配置筛选
            if ($request->has_region !== null) {
                if ($request->has_region == '1') {
                    $query->where(function ($q) {
                        $q->whereNotNull('region_code')->orWhereNotNull('region_name');
                    });
                } else {
                    $query->whereNull('region_code')->whereNull('region_name');
                }
            }
            
            // 分页参数
            $limit = min(100, max(1, (int)($request->limit ?? 20))); // 限制每页最多100条
            $page = max(1, (int)($request->page ?? 1));
            
            $total = $query->count();
            $agents = $query->skip(($page - 1) * $limit)
                           ->take($limit)
                           ->get()
                           ->map(function ($agent) {
                return $this->formatAgentData($agent);
            });
            
            // 统计信息
            $statistics = $this->getStatistics();
            
            // 分页信息
            $pagination = [
                'current_page' => $page,
                'per_page' => $limit,
                'total' => $total,
                'last_page' => ceil($total / $limit),
                'has_more' => $page * $limit < $total,
            ];
            
            return response()->json([
                'code' => 200,
                'message' => '代理商区域配置获取成功',
                'data' => [
                    'agents' => $agents,
                    'statistics' => $statistics,
                    'pagination' => $pagination,
                ],
                'timestamp' => now()->toDateTimeString()
            ]);
            
        } catch (Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '服务器错误: ' . $e->getMessage(),
                'data' => null,
                'timestamp' => now()->toDateTimeString()
            ], 500);
        }
    }

    /**
     * 获取区域层级数据
     * 接口调用：GET /api/agent-regions/areas
     * 
     * @api {get} /api/agent-regions/areas 获取区域层级数据
     * @apiName GetAreasData
     * @apiGroup 代理商区域管理API
     * @apiVersion 1.0.0
     * @apiDescription 获取可用的区域层级数据（无需认证）
     * 
     * @apiParam {Integer} [level] 区域级别（1-省，2-市，3-区县）
     * @apiParam {Integer} [pid] 父级区域ID
     * @apiParam {Integer} [limit] 每页数量，默认100
     * 
     * @apiSuccess {Integer} code 状态码
     * @apiSuccess {String} message 响应消息
     * @apiSuccess {Array} data 区域数据列表
     * @apiSuccess {String} timestamp 响应时间戳
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function areas(Request $request)
    {
        try {
            $query = Area::query();
            
            if ($request->level) {
                $query->where('level', $request->level);
            }
            
            if ($request->pid) {
                $query->where('pid', $request->pid);
            }
            
            // 限制到区县级
            $query->where('level', '<=', 3);
            
            // 分页限制
            $limit = min(1000, max(1, (int)($request->limit ?? 100))); 
            
            $areas = $query->orderBy('level')
                          ->orderBy('name')
                          ->take($limit)
                          ->get()
                          ->map(function ($area) {
                return [
                    'id' => $area->id,
                    'name' => $area->name,
                    'level' => $area->level,
                    'pid' => $area->pid,
                    'citycode' => $area->citycode ?? null,
                    'full_name' => $this->getAreaFullName($area),
                ];
            });
            
            return response()->json([
                'code' => 200,
                'message' => '区域数据获取成功',
                'data' => $areas,
                'timestamp' => now()->toDateTimeString()
            ]);
            
        } catch (Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '服务器错误: ' . $e->getMessage(),
                'data' => null,
                'timestamp' => now()->toDateTimeString()
            ], 500);
        }
    }

    /**
     * 获取代理商区域配置统计
     * 接口调用：GET /api/agent-regions/statistics
     * 
     * @api {get} /api/agent-regions/statistics 获取统计数据
     * @apiName GetRegionStatistics
     * @apiGroup 代理商区域管理API
     * @apiVersion 1.0.0
     * @apiDescription 获取代理商区域配置统计信息
     * 
     * @return \Illuminate\Http\JsonResponse
     */
    public function statistics()
    {
        try {
            $statistics = $this->getStatistics();
            
            // 添加更详细的统计
            $statusStatistics = Agent::where('status', '!=', 'deleted')
                ->selectRaw('status, count(*) as count')
                ->groupBy('status')
                ->pluck('count', 'status')
                ->toArray();
            
            $regionLevelStatistics = Agent::whereNotNull('region_code')
                ->join('areas', 'agents.region_code', '=', 'areas.id')
                ->selectRaw('areas.level, count(*) as count')
                ->groupBy('areas.level')
                ->pluck('count', 'areas.level')
                ->toArray();
            
            $statistics['by_status'] = $statusStatistics;
            $statistics['by_region_level'] = $regionLevelStatistics;
            
            return response()->json([
                'code' => 200,
                'message' => '统计数据获取成功',
                'data' => $statistics,
                'timestamp' => now()->toDateTimeString()
            ]);
            
        } catch (Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '服务器错误: ' . $e->getMessage(),
                'data' => null,
                'timestamp' => now()->toDateTimeString()
            ], 500);
        }
    }

    /**
     * 格式化代理商数据
     * 
     * @param Agent $agent
     * @return array
     */
    private function formatAgentData($agent)
    {
        $regionInfo = '';
        
        if ($agent->region_name) {
            $regionInfo = $agent->region_name;
        }
        
        if ($agent->region_code) {
            $area = Area::find($agent->region_code);
            if ($area) {
                $areaName = $area->name;
                $regionInfo = $regionInfo ? "{$regionInfo} ({$areaName})" : $areaName;
                
                // 获取父级区域
                $parentInfo = $this->getParentRegions($area);
                if ($parentInfo) {
                    $regionInfo = "{$parentInfo} → {$regionInfo}";
                }
            }
        }
        
        return [
            'id' => $agent->id,
            'name' => $agent->name,
            'contact_person' => $agent->contact_person,
            'phone' => $agent->phone,
            'email' => $agent->email,
            'region_code' => $agent->region_code,
            'region_name' => $agent->region_name,
            'region_info' => $regionInfo ?: '未配置',
            'status' => $agent->status,
            'has_region' => !empty($agent->region_code) || !empty($agent->region_name),
            'created_at' => $agent->created_at,
            'updated_at' => $agent->updated_at,
        ];
    }

    /**
     * 获取统计信息
     * 
     * @return array
     */
    private function getStatistics()
    {
        $totalAgents = Agent::where('status', '!=', 'deleted')->count();
        $configuredAgents = Agent::where('status', '!=', 'deleted')
            ->where(function ($q) {
                $q->whereNotNull('region_code')->orWhereNotNull('region_name');
            })->count();
        
        return [
            'total_agents' => $totalAgents,
            'configured_agents' => $configuredAgents,
            'unconfigured_agents' => $totalAgents - $configuredAgents,
            'configuration_rate' => $totalAgents > 0 ? round(($configuredAgents / $totalAgents) * 100, 2) : 0,
        ];
    }

    /**
     * 获取父级区域信息
     * 
     * @param Area $area
     * @return string
     */
    private function getParentRegions($area)
    {
        $parents = [];
        $current = $area;
        
        while ($current && $current->pid) {
            $parent = Area::find($current->pid);
            if ($parent) {
                $parents[] = $parent->name;
                $current = $parent;
            } else {
                break;
            }
        }
        
        return implode(' → ', array_reverse($parents));
    }

    /**
     * 获取区域完整名称
     * 
     * @param Area $area
     * @return string
     */
    private function getAreaFullName($area)
    {
        $names = [$area->name];
        $current = $area;
        
        while ($current->pid) {
            $parent = Area::find($current->pid);
            if ($parent) {
                $names[] = $parent->name;
                $current = $parent;
            } else {
                break;
            }
        }
        
        return implode(' ', array_reverse($names));
    }
} 