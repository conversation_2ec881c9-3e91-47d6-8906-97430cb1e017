/**
 * Commission Settlement Detail JavaScript Manager
 * Version: 2.0.0
 * 根据开发日志中的PJAX环境最佳实践开发
 */

// 调试信息
console.log('🚀 commission-settlement-detail.js 开始加载...');

// 全局命名空间
window.CommissionSettlementDetail = {
    // 配置参数
    config: {
        settlementId: null,
        apiUrl: null,
        data: {
            settlement: null,
            stores: [],
            agentRegion: null
        }
    },

    // 初始化方法
    init: function (settlementId, apiUrl) {
        console.log('🔧 初始化结算详情页面，ID:', settlementId, 'API URL:', apiUrl);

        this.config.settlementId = settlementId;
        this.config.apiUrl = apiUrl;

        // 等待DOM就绪后加载数据
        this.waitForContainer(function () {
            window.CommissionSettlementDetail.loadData();
        });
    },

    // 容器等待机制（参考开发日志中的最佳实践）
    waitForContainer: function (callback, maxWait = 5000) {
        var startTime = Date.now();
        var checkInterval = setInterval(function () {
            var container = document.getElementById('settlement-detail');
            if (container) {
                clearInterval(checkInterval);
                console.log('✅ 找到结算详情容器');
                callback();
            } else if (Date.now() - startTime > maxWait) {
                clearInterval(checkInterval);
                console.error('❌ 等待结算详情容器超时');
            }
        }, 100);
    },

    // 加载数据
    loadData: function () {
        var self = this;

        console.log('🔧 开始加载结算数据，API URL:', this.config.apiUrl);

        // 显示加载状态
        this.showLoading();

        // Ajax请求数据
        $.ajax({
            url: this.config.apiUrl,
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function (response) {
                console.log('✅ 数据加载成功:', response);
                if (response.status) {
                    self.config.data = {
                        settlement: response.settlement,
                        stores: response.stores || [],
                        agentRegion: response.agent_region
                    };
                    self.renderSettlement(response.settlement, response.agent_region);
                    self.renderStores(response.stores || []);
                } else {
                    console.error('❌ API返回错误:', response.message);
                    self.showError();
                }
            },
            error: function (xhr, status, error) {
                console.error('❌ 数据加载失败:', {
                    status: status,
                    error: error,
                    responseText: xhr.responseText,
                    statusCode: xhr.status
                });
                self.showError();
            }
        });
    },

    // 显示加载状态
    showLoading: function () {
        console.log('🔄 显示加载状态');
        $('#settlement-loading').show();
        $('#settlement-error').hide();
        $('#settlement-table').hide();
        $('#stores-loading').show();
        $('#stores-error').hide();
        $('#stores-table').hide();
    },

    // 显示错误状态
    showError: function () {
        console.log('❌ 显示错误状态');
        $('#settlement-loading').hide();
        $('#settlement-error').show();
        $('#stores-loading').hide();
        $('#stores-error').show();
    },

    // 渲染结算信息
    renderSettlement: function (settlement, agentRegion) {
        console.log('🎨 开始渲染结算信息:', settlement);
        var tbody = $('#settlement-tbody');
        tbody.empty();

        var rows = [
            ['ID', settlement.id || '-'],
            ['结算单号', settlement.settlement_no || '-'],
            ['结算对象类型', this.getTargetTypeName(settlement.target_type)],
            ['结算对象ID', settlement.target_id || '-'],
            ['结算对象名称', settlement.target_name || '-'],
            ['结算周期开始', settlement.settlement_period_start || '-'],
            ['结算周期结束', settlement.settlement_period_end || '-'],
            ['总金额', settlement.total_amount ? '￥' + this.formatNumber(settlement.total_amount) : '￥0.00'],
            ['佣金金额', settlement.commission_amount ? '￥' + this.formatNumber(settlement.commission_amount) : '￥0.00'],
            ['扣除金额', settlement.deduction_amount ? '￥' + this.formatNumber(settlement.deduction_amount) : '￥0.00'],
            ['实际结算金额', settlement.actual_amount ? '￥' + this.formatNumber(settlement.actual_amount) : '￥0.00'],
            ['商铺数量', (settlement.store_count || 0) + '家'],
            ['活跃商铺数量', (settlement.active_store_count || 0) + '家'],
            ['状态', this.getStatusLabel(settlement.status)],
            ['备注', settlement.remark || '-'],
            ['详细数据', settlement.detail_data ? JSON.stringify(settlement.detail_data) : '-'],
            ['审核时间', settlement.approved_at || '-'],
            ['审核人ID', settlement.approved_by || '-'],
            ['审核人姓名', settlement.approver_name || '-'],
            ['支付时间', settlement.paid_at || '-'],
            ['支付方式', settlement.payment_method || '-'],
            ['支付凭证', settlement.payment_reference || '-'],
            ['创建时间', settlement.created_at || '-'],
            ['更新时间', settlement.updated_at || '-']
        ];

        // 添加计算公式
        if (agentRegion) {
            rows.splice(11, 0, ['计算公式', this.getCalculationFormula(settlement, agentRegion)]);
        }

        for (var i = 0; i < rows.length; i++) {
            var row = $('<tr></tr>');
            row.append($('<th></th>').text(rows[i][0]));

            if (rows[i][0] === '计算公式') {
                row.append($('<td></td>').html(rows[i][1]));
            } else if (rows[i][0] === '状态') {
                row.append($('<td></td>').html(rows[i][1]));
            } else {
                row.append($('<td></td>').text(rows[i][1]));
            }

            tbody.append(row);
        }

        console.log('✅ 结算信息渲染完成');
        $('#settlement-loading').hide();
        $('#settlement-table').show();
    },

    // 渲染商铺信息
    renderStores: function (stores) {
        console.log('🎨 开始渲染商铺信息，数量:', stores.length);
        var tbody = $('#stores-tbody');
        tbody.empty();

        for (var i = 0; i < stores.length; i++) {
            tbody.append(this.createStoreRow(stores[i]));
        }

        console.log('✅ 商铺信息渲染完成');
        $('#stores-loading').hide();
        $('#stores-table').show();
    },

    // 创建商铺行
    createStoreRow: function (store) {
        var row = $('<tr></tr>');
        var fields = [
            store.id || '-',
            store.name || '-',
            store.contact_person || '-',
            store.phone || '-',
            this.getFullAddress(store),
            store.product_amount ? '￥' + this.formatNumber(store.product_amount) : '￥0.00',
            this.getIsSettledLabel(store.is_settled),
            this.getSettlementStatusLabel(store.settlement_status),
            store.agent_id || '-',
            store.province_name || '-',
            store.city_name || '-',
            store.district_name || '-',
            store.audit_status_name || store.audit_status || '-',
            store.store_status_name || store.store_status || '-',
            store.audit_remark || '-',
            store.last_settlement_id || '-'
        ];

        for (var i = 0; i < fields.length; i++) {
            var cell = $('<td></td>');
            if (i === 6 || i === 7) {
                cell.html(fields[i]);
            } else {
                cell.text(fields[i]);
            }
            row.append(cell);
        }

        return row;
    },

    // 工具方法
    formatNumber: function (value) {
        var num = parseFloat(value) || 0;
        return num.toFixed(2);
    },

    getTargetTypeName: function (type) {
        var types = {
            'agent': '代理商',
            'salesperson': '业务员',
            'team_leader': '团队长'
        };
        return types[type] || type;
    },

    getStatusLabel: function (status) {
        var statusMap = {
            'pending': '<span class="label label-warning">待审核</span>',
            'approved': '<span class="label label-success">已审核</span>',
            'paid': '<span class="label label-info">已支付</span>',
            'rejected': '<span class="label label-danger">已拒绝</span>'
        };
        return statusMap[status] || status;
    },

    getIsSettledLabel: function (isSettled) {
        if (isSettled === 1 || isSettled === true) {
            return '<span class="label label-success">已结算</span>';
        } else {
            return '<span class="label label-default">未结算</span>';
        }
    },

    getSettlementStatusLabel: function (status) {
        var statusMap = {
            0: '<span class="label label-default">未结算</span>',
            1: '<span class="label label-warning">待结算</span>',
            2: '<span class="label label-success">已结算</span>'
        };
        return statusMap[status] || '<span class="label label-default">未知</span>';
    },

    getFullAddress: function (store) {
        var parts = [];
        if (store.province_name) parts.push(store.province_name);
        if (store.city_name) parts.push(store.city_name);
        if (store.district_name) parts.push(store.district_name);
        if (store.address) parts.push(store.address);
        return parts.join(' ') || '-';
    },

    getCalculationFormula: function (settlement, agentRegion) {
        var formula = '<div class="calculation-formula">';

        if (agentRegion.commission_type === 'percentage') {
            var totalAmount = parseFloat(settlement.total_amount) || 0;
            var commissionRate = parseFloat(agentRegion.commission_rate) || 0;
            var calculated = totalAmount * (commissionRate / 100);
            formula += '<span class="text-info">按比例计算：总金额 × ' + commissionRate + '% = ￥' + calculated.toFixed(2) + '</span>';
        } else if (agentRegion.commission_type === 'fixed') {
            var storeCount = parseInt(settlement.store_count) || 0;
            var commissionAmount = parseFloat(agentRegion.commission_amount) || 0;
            var calculated = storeCount * commissionAmount;
            formula += '<span class="text-info">固定金额：' + storeCount + '家商铺 × ￥' + commissionAmount.toFixed(2) + ' = ￥' + calculated.toFixed(2) + '</span>';
        } else if (agentRegion.commission_type === 'tiered') {
            formula += '<span class="text-info">阶梯计算：' + (agentRegion.commission_rules || '规则未配置') + '</span>';
        } else {
            formula += '<span class="text-muted">计算方式：' + (agentRegion.commission_type || '未知') + '</span>';
        }

        var commissionAmount = parseFloat(settlement.commission_amount) || 0;
        var deductionAmount = parseFloat(settlement.deduction_amount) || 0;
        var actualAmount = parseFloat(settlement.actual_amount) || 0;

        formula += '<br><small class="text-muted">实际结算金额 = 佣金金额 - 扣除金额 = ￥' +
            commissionAmount.toFixed(2) + ' - ￥' +
            deductionAmount.toFixed(2) + ' = ￥' +
            actualAmount.toFixed(2) + '</small>';
        formula += '</div>';

        return formula;
    }
};

console.log('✅ CommissionSettlementDetail 对象已创建');

// PJAX兼容的自动初始化系统（参考开发日志最佳实践）
(function () {
    function autoInitialize() {
        console.log('�� 检查是否为结算详情页面...');

        // 检查当前页面是否为结算详情页面
        var isDetailPage = window.location.href.includes('commission-settlements') &&
            $('#settlement-detail').length > 0;

        console.log('页面检查结果:', {
            url: window.location.href,
            containsCommissionSettlements: window.location.href.includes('commission-settlements'),
            hasContainer: $('#settlement-detail').length > 0,
            isDetailPage: isDetailPage
        });

        if (isDetailPage) {
            console.log('🔧 检测到结算详情页面，准备初始化...');

            // 从DOM获取配置数据
            var container = $('#settlement-detail');
            var settlementId = container.data('id');
            var apiUrl = container.data('api-url');

            console.log('配置参数:', {
                settlementId: settlementId,
                apiUrl: apiUrl
            });

            if (settlementId && apiUrl) {
                // 延迟初始化确保DOM完全就绪
                setTimeout(function () {
                    window.CommissionSettlementDetail.init(settlementId, apiUrl);
                }, 200);
            } else {
                console.error('❌ 缺少必要的配置参数', {
                    settlementId: settlementId,
                    apiUrl: apiUrl
                });
            }
        } else {
            console.log('ℹ️ 非结算详情页面，跳过初始化');
        }
    }

    console.log('🔧 设置事件监听器...');

    // 多种事件监听确保在PJAX环境下正常工作
    $(document).ready(function () {
        console.log('📄 Document ready 事件触发');
        autoInitialize();
    });

    $(document).on('pjax:complete', function () {
        console.log('🔄 PJAX complete 事件触发');
        setTimeout(autoInitialize, 100);
    });

    $(document).on('pjax:end', function () {
        console.log('🔄 PJAX end 事件触发');
        setTimeout(autoInitialize, 200);
    });

    window.addEventListener('load', function () {
        console.log('🌐 Window load 事件触发');
        autoInitialize();
    });

    console.log('✅ 事件监听器设置完成');
})();

console.log('🎉 commission-settlement-detail.js 加载完成'); 