<?php

namespace App\Console;

use App\Admin\Commands\CreateAgentMenu;
use App\Console\Commands\AddDeletedAtToAgentsTable;
use App\Console\Commands\AddSignedAtToAgentsTable;
use App\Console\Commands\GenerateSettlements;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * 应用程序提供的Artisan命令
     *
     * @var array
     */
    protected $commands = [
        CreateAgentMenu::class,
        AddDeletedAtToAgentsTable::class,
        AddSignedAtToAgentsTable::class,
        GenerateSettlements::class,
    ];
    
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // $schedule->command('inspire')->hourly();
        // 删除自动结算单生成任务，只保留手动生成功能
        // $schedule->command('settlements:generate')->daily()->at('02:00');
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');
        $this->load(__DIR__.'/../Admin/Commands');

        require base_path('routes/console.php');
    }
}
