/**
 * 仪表板图表管理器
 * 解决PJAX环境下的图表加载问题
 * 作者: lauJinyu
 * 日期: 2025-01-08
 */

(function() {
    'use strict';
    
    // 全局图表实例管理
    window.DashboardCharts = {
        revenueChart: null,
        userTypeChart: null,
        
        // 清理所有图表实例
        cleanup: function() {
            if (this.revenueChart) {
                try {
                    this.revenueChart.dispose();
                    console.log('✅ 收入图表实例已清理');
                } catch (e) {
                    console.warn('⚠️ 清理收入图表时出错:', e);
                }
                this.revenueChart = null;
            }
            
            if (this.userTypeChart) {
                try {
                    this.userTypeChart.dispose();
                    console.log('✅ 用户分布图表实例已清理');
                } catch (e) {
                    console.warn('⚠️ 清理用户分布图表时出错:', e);
                }
                this.userTypeChart = null;
            }
        },
        
        // 等待容器加载
        waitForContainer: function(containerId, callback, maxWait = 5000) {
            var startTime = Date.now();
            var checkInterval = setInterval(function() {
                var container = document.getElementById(containerId);
                if (container) {
                    clearInterval(checkInterval);
                    callback(container);
                } else if (Date.now() - startTime > maxWait) {
                    clearInterval(checkInterval);
                    console.error('❌ 等待容器超时:', containerId);
                }
            }, 50);
        },
        
        // 初始化收入图表
        initRevenueChart: function(data) {
            var self = this;
            console.log('🔄 开始初始化收入图表');
            
            // 检查ECharts是否可用
            if (typeof echarts === 'undefined') {
                console.error('❌ ECharts未加载');
                return;
            }
            
            // 等待容器加载
            this.waitForContainer('revenue-chart', function(chartDom) {
                console.log('✅ 找到收入图表容器');
                
                // 清理旧实例
                if (self.revenueChart) {
                    self.revenueChart.dispose();
                }
                
                try {
                    // 创建新实例
                    self.revenueChart = echarts.init(chartDom);
                    
                    var option = {
                        title: {
                            text: '最近7天收入趋势',
                            left: 'center',
                            textStyle: { fontSize: 14, color: '#333' }
                        },
                        tooltip: {
                            trigger: 'axis',
                            formatter: function(params) {
                                return params[0].name + '\n收入: ￥' + params[0].value.toLocaleString();
                            }
                        },
                        grid: {
                            left: '3%', right: '4%', bottom: '3%', containLabel: true
                        },
                        xAxis: {
                            type: 'category',
                            boundaryGap: false,
                            data: data.labels || ['06-20','06-21','06-22','06-23','06-24','06-25','06-26']
                        },
                        yAxis: {
                            type: 'value',
                            axisLabel: { formatter: '￥{value}' }
                        },
                        series: [{
                            name: '收入',
                            type: 'line',
                            smooth: true,
                            areaStyle: {
                                color: {
                                    type: 'linear',
                                    x: 0, y: 0, x2: 0, y2: 1,
                                    colorStops: [
                                        { offset: 0, color: 'rgba(54, 162, 235, 0.3)' },
                                        { offset: 1, color: 'rgba(54, 162, 235, 0.1)' }
                                    ]
                                }
                            },
                            lineStyle: { color: 'rgba(54, 162, 235, 1)', width: 2 },
                            data: data.data || [2800, 3200, 2900, 3500, 4100, 3800, 4200]
                        }]
                    };
                    
                    self.revenueChart.setOption(option);
                    console.log('✅ 收入图表初始化成功');
                    
                } catch (error) {
                    console.error('❌ 收入图表初始化失败:', error);
                    chartDom.innerHTML = 
                        '<div class="alert alert-danger text-center" style="margin: 20px;">' +
                        '<h4>图表加载失败</h4>' +
                        '<p>错误信息: ' + error.message + '</p>' +
                        '</div>';
                }
            });
        },
        
        // 初始化用户分布图表
        initUserTypeChart: function(data) {
            var self = this;
            console.log('🔄 开始初始化用户分布图表');
            
            // 检查ECharts是否可用
            if (typeof echarts === 'undefined') {
                console.error('❌ ECharts未加载');
                return;
            }
            
            // 等待容器加载
            this.waitForContainer('user-type-chart', function(chartDom) {
                console.log('✅ 找到用户分布图表容器');
                
                // 清理旧实例
                if (self.userTypeChart) {
                    self.userTypeChart.dispose();
                }
                
                try {
                    // 创建新实例
                    self.userTypeChart = echarts.init(chartDom);
                    
                    var option = {
                        title: {
                            text: '用户类型分布',
                            left: 'center',
                            textStyle: { fontSize: 14, color: '#333' }
                        },
                        tooltip: {
                            trigger: 'item',
                            formatter: function(params) {
                                return params.seriesName + '\n' + 
                                       params.name + ': ' + params.value + ' (' + params.percent + '%)';
                            }
                        },
                        legend: {
                            orient: 'vertical',
                            left: 'left'
                        },
                        series: [{
                            name: '用户分布',
                            type: 'pie',
                            radius: ['40%', '70%'],
                            center: ['50%', '60%'],
                            avoidLabelOverlap: false,
                            emphasis: {
                                label: { show: true, fontSize: '18', fontWeight: 'bold' }
                            },
                            data: data || [
                                { name: '代理商', value: 45 },
                                { name: '商铺', value: 30 },
                                { name: '业务员', value: 20 },
                                { name: '其他', value: 5 }
                            ],
                            itemStyle: {
                                color: function(params) {
                                    var colors = ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0'];
                                    return colors[params.dataIndex % colors.length];
                                }
                            }
                        }]
                    };
                    
                    self.userTypeChart.setOption(option);
                    console.log('✅ 用户分布图表初始化成功');
                    
                } catch (error) {
                    console.error('❌ 用户分布图表初始化失败:', error);
                    chartDom.innerHTML = 
                        '<div class="alert alert-danger text-center" style="margin: 20px;">' +
                        '<h4>图表加载失败</h4>' +
                        '<p>错误信息: ' + error.message + '</p>' +
                        '</div>';
                }
            });
        },
        
        // 初始化所有图表
        initAll: function(revenueData, userTypeData) {
            console.log('🔄 开始初始化所有仪表板图表');
            
            // 清理旧实例
            this.cleanup();
            
            // 延迟初始化确保DOM准备就绪
            var self = this;
            setTimeout(function() {
                self.initRevenueChart(revenueData);
                self.initUserTypeChart(userTypeData);
            }, 100);
        },
        
        // 响应式调整
        resize: function() {
            if (this.revenueChart) {
                this.revenueChart.resize();
            }
            if (this.userTypeChart) {
                this.userTypeChart.resize();
            }
        }
    };
    
    // 窗口大小调整处理
    window.addEventListener('resize', function() {
        if (window.DashboardCharts) {
            window.DashboardCharts.resize();
        }
    });
    
    // 自动初始化机制
    function autoInit() {
        // 检查是否在仪表板页面
        if (document.getElementById('revenue-chart') || document.getElementById('user-type-chart')) {
            console.log('🔄 检测到仪表板页面，准备初始化图表');
            
            // 检查是否有图表数据
            var revenueData = window.dashboardRevenueData || {};
            var userTypeData = window.dashboardUserTypeData || [];
            
            window.DashboardCharts.initAll(revenueData, userTypeData);
        }
    }
    
    // 多种触发方式确保初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', autoInit);
    } else {
        setTimeout(autoInit, 100);
    }
    
    // PJAX环境支持
    if (typeof $ !== 'undefined') {
        $(document).on('pjax:complete', function() {
            console.log('🔄 PJAX完成，重新初始化仪表板图表');
            setTimeout(autoInit, 200);
        });
        
        $(document).on('pjax:end', function() {
            console.log('🔄 PJAX结束，确保图表初始化');
            setTimeout(autoInit, 400);
        });
    }
    
})(); 