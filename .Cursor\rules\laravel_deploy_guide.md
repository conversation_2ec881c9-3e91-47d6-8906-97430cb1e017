# 碰一碰 Laravel 项目服务器部署说明文档

## 🧾 基础信息

- **服务器提供商**：阿里云 ECS
- **公网 IP**：************
- **操作系统**：CentOS 7.9 64 位
- **域名**：99nice.cc
- **项目子域名**：pyp.99nice.cc
- **数据库信息**：
  - 用户名：`root`
  - 密码：`zsjsb&mysql`
  - 数据库名：`pyp_laravel`

---

## ✅ 部署方式选择记录

本次项目部署采用 **手动部署 + DNS 绑定二级域名 + XShell连接配置环境**。

---

## 📦 第一步：阿里云资源准备

1. 已购买 ECS（2核4G）CentOS 系统
2. 安装了 XShell 工具，用于远程连接服务器
3. 安装了 WinSCP 用于上传项目文件
4. 阿里云域名控制台中添加了解析记录：
   - `@` → ************ ✅
   - `www` → ************ ✅
   - `pyp` → ************ ✅（项目主入口）
5. 阿里云安全组规则已开放：22（SSH）、80（HTTP）、443（HTTPS）、3306（MySQL）

---

## 🖥 第二步：服务器环境初始化

通过 XShell 成功连接后执行如下命令：

```bash
# 系统更新
yum update -y

# 安装必要工具
yum install -y wget curl git unzip vim

# 安装 Nginx
yum install -y nginx
systemctl enable nginx && systemctl start nginx

# 安装 PHP 8.0
yum install -y epel-release
rpm -ivh https://rpms.remirepo.net/enterprise/remi-release-7.rpm
yum-config-manager --enable remi-php80
yum install -y php php-fpm php-mysqlnd php-gd php-mbstring php-xml php-curl php-bcmath php-zip

# 启动 PHP
systemctl enable php-fpm && systemctl start php-fpm

# 安装 MySQL
wget https://dev.mysql.com/get/mysql80-community-release-el7-3.noarch.rpm
rpm -ivh mysql80-community-release-el7-3.noarch.rpm
yum install -y mysql-server
systemctl enable mysqld && systemctl start mysqld

# 安装 Composer
curl -sS https://getcomposer.org/installer | php
mv composer.phar /usr/local/bin/composer
chmod +x /usr/local/bin/composer
```
...（省略其余内容，实为 markdown 文档内容）
