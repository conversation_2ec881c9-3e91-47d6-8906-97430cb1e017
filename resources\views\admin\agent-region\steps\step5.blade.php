<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label for="notes">区域配置备注</label>
            <textarea class="form-control" name="notes" id="notes" rows="6" 
                      placeholder="请输入区域配置的相关备注信息，如特殊要求、注意事项、历史变更记录等...">{{ isset($agent_region) ? $agent_region->notes : '' }}</textarea>
            <small class="text-muted">详细记录区域配置的相关说明和注意事项</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            <label for="created_by">创建人</label>
            <input type="text" class="form-control" name="created_by" id="created_by" 
                   value="{{ auth()->user()->name ?? '系统管理员' }}" readonly>
            <small class="text-muted">区域配置的创建人员</small>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="form-group">
            <label for="contact_info">联系方式</label>
            <input type="text" class="form-control" name="contact_info" id="contact_info" 
                   value="{{ isset($agent_region) ? $agent_region->contact_info : '' }}"
                   placeholder="请输入负责人联系方式">
            <small class="text-muted">区域负责人的联系电话或邮箱</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="panel panel-primary">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <i class="fa fa-check-circle"></i>
                    配置信息确认
                </h4>
            </div>
            <div class="panel-body">
                <div id="configSummary">
                    <!-- 配置摘要将通过JavaScript动态生成 -->
                    <div class="text-center text-muted">
                        <i class="fa fa-spinner fa-spin"></i>
                        正在加载配置摘要...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="checkbox">
            <label>
                <input type="checkbox" id="confirmSubmit" required>
                <strong>我确认以上配置信息无误，同意提交此代理商区域配置</strong>
            </label>
        </div>
        <small class="text-muted">
            请仔细检查所有配置信息，提交后部分信息可能需要审批才能修改
        </small>
    </div>
</div>

<div class="alert alert-info">
    <i class="fa fa-check"></i>
    <strong>配置完成说明：</strong>
    <ul class="mb-0 mt-2">
        <li>提交后系统将创建代理商区域配置记录</li>
        <li>返佣规则将立即生效，开始计算相关订单的返佣</li>
        <li>结算周期将按设定时间自动执行</li>
        <li>后续如需修改配置，请联系系统管理员</li>
    </ul>
</div>

<style>
/* 配置摘要样式优化 */
#configSummary .panel {
    margin-bottom: 15px;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#configSummary .panel-heading {
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    border-bottom: 1px solid #dee2e6;
    border-radius: 6px 6px 0 0;
}

#configSummary .panel-heading h5 {
    margin: 0;
    font-weight: 600;
    color: #495057;
}

#configSummary .panel-heading .fa {
    margin-right: 8px;
    color: #6c757d;
}

#configSummary .table {
    margin-bottom: 0;
}

#configSummary .table td {
    border-top: 1px solid #f1f3f4;
    padding: 8px 12px;
    font-size: 13px;
}

#configSummary .table td:first-child {
    width: 35%;
    background-color: #f8f9fa;
    font-weight: 500;
    color: #495057;
}

#configSummary .table td:last-child {
    color: #212529;
}

/* 加载动画样式 */
#configSummary .fa-spinner {
    color: #6c757d;
    font-size: 18px;
    margin-bottom: 10px;
}

/* 错误提示样式 */
#configSummary .alert-warning {
    border-radius: 6px;
    border: 1px solid #ffc107;
    background-color: #fff3cd;
}

#configSummary .alert-warning .btn {
    margin-top: 10px;
}
</style>

<script>
// 开发阶段调试脚本
$(document).ready(function() {
    // 添加调试按钮（仅在开发环境显示）
    if (window.location.hostname === 'localhost' || window.location.hostname.includes('127.0.0.1')) {
        const debugBtn = `
            <div style="margin-top: 15px; text-align: center;">
                <button id="debugGenerateSummary" class="btn btn-xs btn-info">
                    <i class="fa fa-bug"></i> 调试：手动生成摘要
                </button>
                <button id="debugShowCache" class="btn btn-xs btn-warning" style="margin-left: 10px;">
                    <i class="fa fa-database"></i> 显示缓存数据
                </button>
            </div>
        `;
        $('#configSummary').after(debugBtn);
        
        // 绑定调试按钮事件
        $(document).on('click', '#debugGenerateSummary', function() {
            if (window.agentRegionStepWizard && typeof window.agentRegionStepWizard.generateConfigSummary === 'function') {
                console.log('🐛 [DEBUG] 手动触发配置摘要生成');
                console.log('🗂️ [DEBUG] 当前缓存的数据:', window.agentRegionStepWizard.stepDataCache);
                window.agentRegionStepWizard.generateConfigSummary();
            } else {
                console.error('❌ [DEBUG] agentRegionStepWizard实例或generateConfigSummary方法不可用');
                alert('StepWizard实例未找到，请检查JavaScript是否正确加载');
            }
        });
        
        // 绑定显示缓存数据按钮事件
        $(document).on('click', '#debugShowCache', function() {
            if (window.agentRegionStepWizard && window.agentRegionStepWizard.stepDataCache) {
                const cache = window.agentRegionStepWizard.stepDataCache;
                const cacheInfo = [];
                
                for (let i = 1; i <= 5; i++) {
                    const stepKey = `step${i}`;
                    const stepData = cache[stepKey];
                    const hasData = Object.keys(stepData).length > 0;
                    cacheInfo.push(`步骤${i}: ${hasData ? '有数据 ✓' : '无数据 ✗'}`);
                }
                
                console.log('🗂️ [DEBUG] 缓存数据详情:', cache);
                alert(`缓存状态:\n${cacheInfo.join('\n')}\n\n详细数据请查看控制台`);
            } else {
                alert('StepWizard实例未找到');
            }
        });
    }
    
    // 监听摘要生成事件
    $(document).on('summaryGenerated', function() {
        console.log('✅ [DEBUG] 配置摘要已生成');
    });
});
</script> 