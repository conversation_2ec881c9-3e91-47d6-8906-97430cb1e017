<?php

namespace App\Observers;

use App\Models\Store;
use App\Models\AgentRegion;
use App\Models\CommissionSettlement;
use Illuminate\Support\Carbon;

class StoreObserver
{
    public function created(Store $store)
    {
        // 注释掉自动生成结算单逻辑，只保留手动生成功能
        /*
        // 只为有代理商的商铺生成结算单
        if ($store->agent_id) {
            // 查找有效的代理区域配置
            $region = AgentRegion::where('agent_id', $store->agent_id)
                ->where('area_id', $store->area_id)
                ->where('region_status', 'active')
                ->where('contract_status', 'signed')
                ->first();

            if ($region) {
                // 佣金计算
                $commission = 0;
                if ($region->commission_type === 'percentage') {
                    $commission = round($store->product_amount * ($region->commission_rate / 100), 2);
                } elseif ($region->commission_type === 'fixed') {
                    $commission = $region->commission_amount;
                } elseif ($region->commission_type === 'tiered') {
                    $commission = $this->calcTieredCommission($store->product_amount, $region->commission_rules);
                }

                // 生成结算单
                CommissionSettlement::create([
                    'settlement_no' => 'SET-' . date('YmdHis') . '-' . $store->agent_id . '-' . $store->id,
                    'target_type' => 'agent',
                    'target_id' => $store->agent_id,
                    'settlement_period_start' => now(),
                    'settlement_period_end' => now(),
                    'total_amount' => $store->product_amount,
                    'commission_amount' => $commission,
                    'actual_amount' => $commission,
                    'store_count' => 1,
                    'active_store_count' => 1,
                    'status' => 'pending',
                    'remark' => '单店实时结算，商铺ID:' . $store->id,
                    'detail_data' => json_encode(['store_id' => $store->id]),
                ]);
            }
        }
        */
    }

    /*
    private function calcTieredCommission($amount, $rules)
    {
        if (!$rules) return 0.00;
        $parsed = $this->parseTieredRules($rules);
        foreach ($parsed as $rule) {
            if (
                ($rule['min'] === null || $amount >= $rule['min']) &&
                ($rule['max'] === null || $amount <= $rule['max'])
            ) {
                return round($amount * ($rule['rate'] / 100), 2);
            }
        }
        return 0.00;
    }

    private function parseTieredRules($rules)
    {
        // 例：1-1000元5%；1001-5000元8%；5001元以上:10%
        $result = [];
        $parts = preg_split('/[；;]/u', $rules);
        foreach ($parts as $part) {
            if (preg_match('/(\d+)-(\d+)元[:：]?(\d+\.?\d*)%/', $part, $m)) {
                $result[] = [
                    'min' => floatval($m[1]),
                    'max' => floatval($m[2]),
                    'rate' => floatval($m[3]),
                ];
            } elseif (preg_match('/(\d+)元以上[:：]?(\d+\.?\d*)%/', $part, $m)) {
                $result[] = [
                    'min' => floatval($m[1]),
                    'max' => null,
                    'rate' => floatval($m[2]),
                ];
            }
        }
        return $result;
    }
    */
} 