# 小红书API接入完整文档

## 📖 概述

本文档详细介绍了小红书API的完整接入过程，包括签名算法、接口调用方法以及我们实现的代理系统。

## 🎯 核心发现

经过深入研究和测试，我们发现小红书API的关键特点：

- **签名参数** 和 **请求参数** 使用了不同的参数名
- 签名中使用：`appKey`, `nonce`, `timeStamp` (驼峰命名)
- 请求中使用：`app_key`, `nonce`, `timestamp` (下划线命名)

## 🔧 技术实现

### 1. 基础参数生成

```php
// 生成16位十六进制随机数
$nonce = bin2hex(random_bytes(8));

// 生成毫秒级时间戳
$timestamp = time() * 1000;
```

### 2. 签名算法 (核心)

```php
/**
 * 小红书API签名生成
 * @param string $appKey 应用标识
 * @param string $nonce 随机数
 * @param int $timestamp 时间戳
 * @param string $appSecret 应用密钥
 * @return string 签名
 */
function generateXiaohongshuSignature($appKey, $nonce, $timestamp, $appSecret) {
    // 签名参数 - 注意使用timeStamp
    $signParams = [
        'appKey' => $appKey,
        'nonce' => $nonce,
        'timeStamp' => $timestamp  // 驼峰命名
    ];
    
    // 按键名排序
    ksort($signParams);
    
    // 拼接参数字符串
    $signString = '';
    foreach ($signParams as $key => $value) {
        if ($signString) $signString .= '&';
        $signString .= $key . '=' . $value;
    }
    
    // 追加密钥
    $signString .= $appSecret;
    
    // 生成SHA-256签名
    return hash('sha256', $signString);
}
```

### 3. 完整调用示例

```php
<?php
// 配置信息
$appKey = 'red.U8c2BaTqz6xllLY6';
$appSecret = '42aab997868559f305b55a4ffd32f688';
$apiUrl = 'https://edith.xiaohongshu.com/api/sns/v1/ext/access/token';

// 1. 生成基础参数
$nonce = bin2hex(random_bytes(8));
$timestamp = time() * 1000;

// 2. 生成签名
$signature = generateXiaohongshuSignature($appKey, $nonce, $timestamp, $appSecret);

// 3. 构建请求参数 - 注意使用app_key和timestamp
$requestData = [
    'app_key' => $appKey,        // 下划线命名
    'nonce' => $nonce,
    'timestamp' => $timestamp,   // 小写
    'signature' => $signature
];

// 4. 发送POST请求
$payload = json_encode($requestData);

$ch = curl_init($apiUrl);
curl_setopt_array($ch, [
    CURLOPT_POSTFIELDS => $payload,
    CURLOPT_HTTPHEADER => ['Content-Type: application/json'],
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_TIMEOUT => 30
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

// 5. 处理响应
if ($response) {
    $data = json_decode($response, true);
    
    if (isset($data['data']['access_token'])) {
        $accessToken = $data['data']['access_token'];
        $expiresIn = $data['data']['expires_in'];
        
        echo "获取成功！\n";
        echo "Access Token: {$accessToken}\n";
        echo "过期时间: {$expiresIn}秒\n";
        
        // 建议缓存token
        file_put_contents('xiaohongshu_token.json', json_encode([
            'access_token' => $accessToken,
            'expires_at' => time() + ($expiresIn / 1000)
        ]));
    } else {
        echo "请求失败: " . ($data['msg'] ?? '未知错误') . "\n";
    }
}

// 签名生成函数
function generateXiaohongshuSignature($appKey, $nonce, $timestamp, $appSecret) {
    $signParams = [
        'appKey' => $appKey,
        'nonce' => $nonce,
        'timeStamp' => $timestamp
    ];
    
    ksort($signParams);
    
    $signString = '';
    foreach ($signParams as $key => $value) {
        if ($signString) $signString .= '&';
        $signString .= $key . '=' . $value;
    }
    $signString .= $appSecret;
    
    return hash('sha256', $signString);
}
?>
```

## 🚀 我们实现的代理系统

为了简化使用，我们开发了完整的Laravel代理系统：

### 接口列表

| 接口      | 方法 | 用途       | URL                             |
| --------- | ---- | ---------- | ------------------------------- |
| 获取Token | POST | 主要接口   | `/api/xiaohongshu/access-token` |
| 代理请求  | POST | 直接代理   | `/api/xiaohongshu/proxy`        |
| 连接测试  | GET  | 测试连通性 | `/api/xiaohongshu/test`         |

### 推荐用法

```bash
# 获取access_token (推荐)
curl -X POST "http://localhost/pyp-Laravel-new/public/api/xiaohongshu/access-token" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{"grant_type": "client_credentials"}'
```

### 成功响应格式

```json
{
    "code": 200,
    "message": "获取小红书access_token成功",
    "data": {
        "access_token": "VyUDSEMH63o5XeOn",
        "expires_in": 1752226898872
    },
    "timestamp": 1752140407
}
```

## 🔍 常见问题排查

### 错误类型

| 错误信息          | 原因           | 解决方法                      |
| ----------------- | -------------- | ----------------------------- |
| `非法参数-appKey` | 参数名格式错误 | 确保请求中使用`app_key`       |
| `签名校验失败`    | 签名算法错误   | 检查签名中是否使用`timeStamp` |
| `请求已过期`      | 时间戳太旧     | 使用当前时间戳                |
| `成功`            | 一切正常       | 可以使用返回的token           |

### 调试技巧

1. **检查签名字符串**
   ```php
   $signString = "appKey={$appKey}&nonce={$nonce}&timeStamp={$timestamp}{$appSecret}";
   echo "签名字符串: " . $signString . "\n";
   ```

2. **验证时间戳**
   ```php
   echo "当前时间戳: " . time() . "\n";
   echo "毫秒时间戳: " . (time() * 1000) . "\n";
   ```

3. **检查请求格式**
   ```php
   echo "请求数据: " . json_encode($requestData, JSON_PRETTY_PRINT) . "\n";
   ```

## 📱 在不同语言中的实现

### JavaScript/Node.js

```javascript
const crypto = require('crypto');
const axios = require('axios');

function generateSignature(appKey, nonce, timeStamp, appSecret) {
    const params = { appKey, nonce, timeStamp };
    const sorted = Object.keys(params)
        .sort()
        .map(k => `${k}=${params[k]}`)
        .join('&');
    const str = sorted + appSecret;
    return crypto.createHash('sha256').update(str).digest('hex');
}

async function getAccessToken() {
    const appKey = 'red.U8c2BaTqz6xllLY6';
    const appSecret = '42aab997868559f305b55a4ffd32f688';
    const nonce = crypto.randomBytes(8).toString('hex');
    const timestamp = Date.now();
    
    const signature = generateSignature(appKey, nonce, timestamp, appSecret);
    
    const response = await axios.post(
        'https://edith.xiaohongshu.com/api/sns/v1/ext/access/token',
        {
            app_key: appKey,
            nonce,
            timestamp,
            signature
        }
    );
    
    return response.data.data.access_token;
}
```

### Python

```python
import hashlib
import secrets
import time
import requests
import json

def generate_signature(app_key, nonce, timestamp, app_secret):
    params = {
        'appKey': app_key,
        'nonce': nonce,
        'timeStamp': timestamp
    }
    
    sorted_params = sorted(params.items())
    sign_string = '&'.join([f'{k}={v}' for k, v in sorted_params]) + app_secret
    
    return hashlib.sha256(sign_string.encode()).hexdigest()

def get_access_token():
    app_key = 'red.U8c2BaTqz6xllLY6'
    app_secret = '42aab997868559f305b55a4ffd32f688'
    nonce = secrets.token_hex(8)
    timestamp = int(time.time() * 1000)
    
    signature = generate_signature(app_key, nonce, timestamp, app_secret)
    
    response = requests.post(
        'https://edith.xiaohongshu.com/api/sns/v1/ext/access/token',
        json={
            'app_key': app_key,
            'nonce': nonce,
            'timestamp': timestamp,
            'signature': signature
        }
    )
    
    return response.json()['data']['access_token']
```

## 🎯 最佳实践

### 1. 缓存管理

```php
class XiaohongshuTokenManager {
    private $cacheFile = 'xiaohongshu_token.json';
    
    public function getAccessToken() {
        // 检查缓存
        if ($this->isCacheValid()) {
            return $this->getCachedToken();
        }
        
        // 获取新token
        $token = $this->fetchNewToken();
        $this->cacheToken($token);
        
        return $token['access_token'];
    }
    
    private function isCacheValid() {
        if (!file_exists($this->cacheFile)) {
            return false;
        }
        
        $cache = json_decode(file_get_contents($this->cacheFile), true);
        return isset($cache['expires_at']) && $cache['expires_at'] > time();
    }
    
    private function getCachedToken() {
        $cache = json_decode(file_get_contents($this->cacheFile), true);
        return $cache['access_token'];
    }
    
    private function fetchNewToken() {
        // 实现获取新token的逻辑
        // 返回包含access_token和expires_in的数组
    }
    
    private function cacheToken($tokenData) {
        $cache = [
            'access_token' => $tokenData['access_token'],
            'expires_at' => time() + ($tokenData['expires_in'] / 1000) - 300 // 提前5分钟过期
        ];
        file_put_contents($this->cacheFile, json_encode($cache));
    }
}
```

### 2. 错误处理

```php
try {
    $token = getXiaohongshuToken();
    echo "获取成功: {$token}\n";
} catch (Exception $e) {
    error_log("小红书API调用失败: " . $e->getMessage());
    
    // 根据错误类型采取不同策略
    if (strpos($e->getMessage(), '签名校验失败') !== false) {
        // 检查签名算法
    } elseif (strpos($e->getMessage(), '请求已过期') !== false) {
        // 重新生成时间戳
    }
}
```

### 3. 日志记录

```php
function logApiCall($request, $response, $duration) {
    $logData = [
        'timestamp' => date('Y-m-d H:i:s'),
        'request' => $request,
        'response' => $response,
        'duration' => $duration . 'ms',
        'status' => isset($response['data']['access_token']) ? 'success' : 'failed'
    ];
    
    file_put_contents('xiaohongshu_api.log', json_encode($logData) . "\n", FILE_APPEND);
}
```

## 📋 配置清单

### 环境要求
- PHP 7.4+
- cURL 扩展
- JSON 扩展
- OpenSSL 扩展

### 必需参数
- `appKey`: 小红书开发者平台分配的应用标识
- `appSecret`: 小红书开发者平台分配的应用密钥

### 网络要求
- 能够访问 `https://edith.xiaohongshu.com`
- 支持HTTPS请求
- 建议超时时间设置为30秒

## 🎉 总结

通过本文档的指导，您应该能够：

1. ✅ 理解小红书API的签名机制
2. ✅ 正确实现签名算法
3. ✅ 成功调用小红书API获取access_token
4. ✅ 使用我们提供的代理系统简化调用
5. ✅ 处理常见错误和异常情况

小红书API接入的关键在于理解其独特的参数命名规则：**签名用驼峰，请求用下划线**。

掌握了这个核心要点，您就能够成功集成小红书API到您的应用中！

---

**文档版本**: 1.0  
**最后更新**: 2025年1月  
**适用版本**: 小红书API v1 