# Laravel项目阿里云部署完整指南

## 📋 部署准备清单

### 必需资源
- [ ] 阿里云账号
- [ ] ECS云服务器实例
- [ ] 域名（可选，用于生产环境）
- [ ] RDS数据库实例（推荐）或本地MySQL

### 项目要求
- [ ] Laravel 8+ 项目
- [ ] PHP 8.0+
- [ ] MySQL 5.7+ 或 8.0+
- [ ] Composer 依赖管理

---

## 🖥️ 第一步：购买配置ECS云服务器

### 1.1 登录阿里云控制台
访问：https://ecs.console.aliyun.com/

### 1.2 创建ECS实例
**推荐配置（测试环境）：**
- **实例规格**: ecs.t5-lc1m2.small（1核2GB）或 ecs.t6-c1m2.large（2核4GB）
- **操作系统**: CentOS 7.9 64位 或 Ubuntu 20.04 LTS
- **存储**: 40GB SSD云盘
- **网络**: 专有网络VPC，分配公网IP
- **安全组**: 开放 22(SSH)、80(HTTP)、443(HTTPS)、3306(MySQL) 端口

### 1.3 配置安全组规则
```bash
# 必需开放的端口
22/TCP    # SSH连接
80/TCP    # HTTP访问
443/TCP   # HTTPS访问
3306/TCP  # MySQL数据库（如果使用本地数据库）
8080/TCP  # 备用端口（可选）
```

---

## 🔧 第二步：服务器环境配置

### 2.1 连接服务器
```bash
# 使用SSH连接（替换为您的服务器IP）
ssh root@your_server_ip

# 首次连接需要输入密码，建议后续配置SSH密钥
```

### 2.2 更新系统并安装基础软件
```bash
# CentOS 7
yum update -y
yum install -y wget curl git vim unzip

# Ubuntu 20.04
apt update && apt upgrade -y
apt install -y wget curl git vim unzip software-properties-common
```

### 2.3 安装Nginx
```bash
# CentOS 7
yum install -y nginx
systemctl start nginx
systemctl enable nginx

# Ubuntu 20.04
apt install -y nginx
systemctl start nginx
systemctl enable nginx
```

### 2.4 安装PHP 8.0+
```bash
# CentOS 7 - 安装Remi仓库
yum install -y epel-release
yum install -y https://rpms.remirepo.net/enterprise/remi-release-7.rpm
yum-config-manager --enable remi-php80

# 安装PHP和必需扩展
yum install -y php php-fpm php-mysqlnd php-pdo php-gd php-mbstring \
    php-xml php-curl php-zip php-intl php-bcmath php-json php-opcache

# Ubuntu 20.04
add-apt-repository ppa:ondrej/php -y
apt update
apt install -y php8.0 php8.0-fpm php8.0-mysql php8.0-pdo php8.0-gd \
    php8.0-mbstring php8.0-xml php8.0-curl php8.0-zip php8.0-intl \
    php8.0-bcmath php8.0-opcache
```

### 2.5 安装Composer
```bash
# 下载并安装Composer
curl -sS https://getcomposer.org/installer | php
mv composer.phar /usr/local/bin/composer
chmod +x /usr/local/bin/composer

# 验证安装
composer --version
```

### 2.6 安装MySQL（或配置RDS）
```bash
# CentOS 7 - 安装MySQL 8.0
wget https://dev.mysql.com/get/mysql80-community-release-el7-3.noarch.rpm
rpm -ivh mysql80-community-release-el7-3.noarch.rpm
yum install -y mysql-server
systemctl start mysqld
systemctl enable mysqld

# 获取临时密码
grep 'temporary password' /var/log/mysqld.log

# Ubuntu 20.04
apt install -y mysql-server
systemctl start mysql
systemctl enable mysql

# 安全配置
mysql_secure_installation
```

---

## 📁 第三步：项目文件部署

### 3.1 创建项目目录
```bash
# 创建网站根目录
mkdir -p /var/www/html/pyp-laravel
cd /var/www/html/pyp-laravel

# 设置目录权限
chown -R nginx:nginx /var/www/html/pyp-laravel  # CentOS
chown -R www-data:www-data /var/www/html/pyp-laravel  # Ubuntu
```

### 3.2 上传项目文件

**方法1：使用Git（推荐）**
```bash
# 如果项目在Git仓库中
git clone https://github.com/your-username/pyp-Laravel-new.git /var/www/html/pyp-laravel

# 或者从本地推送到服务器
# 在本地执行：
git remote add production root@your_server_ip:/var/www/html/pyp-laravel
git push production main
```

**方法2：使用SCP上传**
```bash
# 在本地执行（将整个项目打包上传）
tar -czf pyp-laravel.tar.gz pyp-Laravel-new/
scp pyp-laravel.tar.gz root@your_server_ip:/tmp/

# 在服务器上解压
cd /var/www/html
tar -xzf /tmp/pyp-laravel.tar.gz
mv pyp-Laravel-new pyp-laravel
```

**方法3：使用SFTP工具**
- 使用 FileZilla、WinSCP 等工具
- 连接服务器并上传项目文件

### 3.3 安装项目依赖
```bash
cd /var/www/html/pyp-laravel

# 安装Composer依赖
composer install --optimize-autoloader --no-dev

# 安装NPM依赖（如果有前端资源）
# 首先安装Node.js
curl -fsSL https://rpm.nodesource.com/setup_16.x | bash -  # CentOS
curl -fsSL https://deb.nodesource.com/setup_16.x | bash -  # Ubuntu

yum install -y nodejs  # CentOS
apt install -y nodejs  # Ubuntu

npm install
npm run production
```

---

## ⚙️ 第四步：Laravel项目配置

### 4.1 环境配置
```bash
cd /var/www/html/pyp-laravel

# 复制环境文件
cp .env.example .env

# 生成应用密钥
php artisan key:generate
```

### 4.2 编辑.env文件
```bash
vim .env
```

```env
# 应用配置
APP_NAME="碰一碰智能营销系统"
APP_ENV=production
APP_KEY=base64:your_generated_key_here
APP_DEBUG=false
APP_URL=http://your_domain_or_ip

# 数据库配置
DB_CONNECTION=mysql
DB_HOST=127.0.0.1  # 或RDS内网地址
DB_PORT=3306
DB_DATABASE=pyp_laravel
DB_USERNAME=your_db_username
DB_PASSWORD=your_db_password

# 缓存配置
CACHE_DRIVER=file
SESSION_DRIVER=file
QUEUE_CONNECTION=database

# 邮件配置（如需要）
MAIL_MAILER=smtp
MAIL_HOST=smtp.aliyun.com
MAIL_PORT=465
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_email_password
MAIL_ENCRYPTION=ssl

# 文件存储
FILESYSTEM_DISK=local
```

### 4.3 数据库配置
```bash
# 登录MySQL
mysql -u root -p

# 创建数据库和用户
CREATE DATABASE pyp_laravel CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'pyp_user'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON pyp_laravel.* TO 'pyp_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 4.4 运行数据库迁移
```bash
cd /var/www/html/pyp-laravel

# 运行迁移
php artisan migrate

# 如果有种子数据
php artisan db:seed

# 创建管理员用户
php artisan admin:create-user
```

### 4.5 设置文件权限
```bash
# 设置正确的文件权限
chown -R nginx:nginx /var/www/html/pyp-laravel  # CentOS
chown -R www-data:www-data /var/www/html/pyp-laravel  # Ubuntu

# 设置存储和缓存目录权限
chmod -R 775 storage/
chmod -R 775 bootstrap/cache/
chmod -R 775 public/

# 设置.env文件权限
chmod 600 .env
```

---

## 🌐 第五步：Nginx配置

### 5.1 创建虚拟主机配置
```bash
# 创建配置文件
vim /etc/nginx/conf.d/pyp-laravel.conf
```

```nginx
server {
    listen 80;
    server_name your_domain_or_ip;  # 替换为您的域名或IP
    root /var/www/html/pyp-laravel/public;
    index index.php index.html;

    # 日志文件
    access_log /var/log/nginx/pyp-laravel-access.log;
    error_log /var/log/nginx/pyp-laravel-error.log;

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # Laravel URL重写
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # PHP文件处理
    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;  # 或 unix:/var/run/php-fpm/www.sock
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
        
        # 安全设置
        fastcgi_hide_header X-Powered-By;
        fastcgi_read_timeout 300;
        fastcgi_buffer_size 128k;
        fastcgi_buffers 4 256k;
        fastcgi_busy_buffers_size 256k;
    }

    # 静态文件缓存
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|woff|woff2|ttf|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # 安全设置：隐藏.env和其他敏感文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    location ~ /\.env {
        deny all;
        access_log off;
        log_not_found off;
    }

    # 限制文件上传大小
    client_max_body_size 50M;
}
```

### 5.2 配置PHP-FPM
```bash
# 编辑PHP-FPM配置
vim /etc/php-fpm.d/www.conf  # CentOS
vim /etc/php/8.0/fpm/pool.d/www.conf  # Ubuntu

# 关键配置项：
user = nginx  # CentOS使用nginx，Ubuntu使用www-data
group = nginx  # CentOS使用nginx，Ubuntu使用www-data
listen = 127.0.0.1:9000
pm = dynamic
pm.max_children = 50
pm.start_servers = 5
pm.min_spare_servers = 5
pm.max_spare_servers = 35
```

### 5.3 启动服务
```bash
# 启动PHP-FPM
systemctl start php-fpm    # CentOS
systemctl start php8.0-fpm # Ubuntu

systemctl enable php-fpm    # CentOS
systemctl enable php8.0-fpm # Ubuntu

# 重启Nginx
nginx -t  # 测试配置
systemctl restart nginx
```

---

## 🔒 第六步：安全和优化配置

### 6.1 防火墙配置
```bash
# CentOS 7 - 使用firewalld
systemctl start firewalld
systemctl enable firewalld

firewall-cmd --permanent --add-service=http
firewall-cmd --permanent --add-service=https
firewall-cmd --permanent --add-service=ssh
firewall-cmd --reload

# Ubuntu - 使用ufw
ufw enable
ufw allow ssh
ufw allow http
ufw allow https
```

### 6.2 SSL证书配置（可选）
```bash
# 使用Let's Encrypt免费SSL证书
# 安装certbot
yum install -y certbot python2-certbot-nginx  # CentOS
apt install -y certbot python3-certbot-nginx  # Ubuntu

# 获取SSL证书
certbot --nginx -d your_domain.com

# 自动续期
echo "0 12 * * * /usr/bin/certbot renew --quiet" | crontab -
```

### 6.3 Laravel优化
```bash
cd /var/www/html/pyp-laravel

# 优化配置缓存
php artisan config:cache

# 优化路由缓存
php artisan route:cache

# 优化视图缓存
php artisan view:cache

# 优化类映射
php artisan optimize

# 清理不必要的文件
php artisan clear-compiled
```

---

## 🧪 第七步：测试部署

### 7.1 基础功能测试
```bash
# 检查PHP配置
php -v
php -m | grep mysql

# 检查Laravel状态
cd /var/www/html/pyp-laravel
php artisan --version
php artisan config:show database

# 测试数据库连接
php artisan tinker
>>> DB::connection()->getPdo()
```

### 7.2 网站访问测试
1. **浏览器访问**: `http://your_server_ip` 或 `http://your_domain.com`
2. **管理后台**: `http://your_server_ip/admin`
3. **API测试**: 使用Postman测试API接口

### 7.3 日志监控
```bash
# 查看Nginx访问日志
tail -f /var/log/nginx/pyp-laravel-access.log

# 查看Nginx错误日志
tail -f /var/log/nginx/pyp-laravel-error.log

# 查看Laravel日志
tail -f /var/www/html/pyp-laravel/storage/logs/laravel.log

# 查看PHP-FPM日志
tail -f /var/log/php-fpm/www-error.log
```

---

## 🚨 常见问题和解决方案

### 问题1：403 Forbidden错误
```bash
# 检查文件权限
ls -la /var/www/html/pyp-laravel/public/

# 重新设置权限
chown -R nginx:nginx /var/www/html/pyp-laravel
chmod -R 755 /var/www/html/pyp-laravel/public
```
### 问题2：500 Internal Server Error
```bash
# 检查Laravel日志
tail -20 /var/www/html/pyp-laravel/storage/logs/laravel.log

# 检查存储目录权限
chmod -R 775 storage/
chmod -R 775 bootstrap/cache/
```

### 问题3：数据库连接失败
```bash
# 检查数据库服务状态
systemctl status mysqld

# 测试数据库连接
mysql -u pyp_user -p -h 127.0.0.1 pyp_laravel

# 检查.env配置
cat .env | grep DB_
```

### 问题4：Composer依赖错误
```bash
# 清除Composer缓存
composer clear-cache

# 重新安装依赖
rm -rf vendor/
composer install --optimize-autoloader --no-dev
```

---

## 📋 部署检查清单

### 服务器环境
- [ ] ECS实例正常运行
- [ ] 安全组端口已开放
- [ ] Nginx服务正常
- [ ] PHP-FPM服务正常
- [ ] MySQL服务正常

### 项目配置
- [ ] 项目文件已上传
- [ ] .env文件已配置
- [ ] 数据库连接正常
- [ ] 数据库迁移已完成
- [ ] 文件权限设置正确

### 功能测试
- [ ] 网站首页可访问
- [ ] 管理后台可登录
- [ ] 代理商层级管理功能正常
- [ ] API接口响应正常
- [ ] 文件上传功能正常

---

## 🎯 后续维护建议

1. **定期备份**: 设置自动备份脚本
2. **监控告警**: 配置服务器监控
3. **安全更新**: 定期更新系统和软件
4. **性能优化**: 根据访问量调整配置
5. **日志轮转**: 配置日志文件轮转避免磁盘满

---

## 📞 技术支持

如果在部署过程中遇到问题，可以：
1. 查看服务器日志文件
2. 检查Laravel文档
3. 咨询阿里云技术支持
4. 参考Laravel社区资源

部署完成后，您的碰一碰智能营销系统就可以在阿里云上正常运行了！ 
