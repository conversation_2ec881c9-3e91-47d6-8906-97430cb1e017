{"name": "<PERSON>vel Admin Platform", "version": "1.0.0", "files.maxSize": 5000000, "files.exclude": ["**/node_modules/**", "**/vendor/**/Tests/**", "**/vendor/**/tests/**", "**/vendor/**/*test*/**", "**/storage/framework/cache/**", "**/storage/framework/sessions/**", "**/storage/framework/views/**"], "environment.includePaths": ["./vendor", "./vendor/encore/laravel-admin/src", "./vendor/consoletvs/charts/src", "./app", "./bootstrap"], "environment.phpVersion": "8.2", "diagnostics.enable": true, "diagnostics.undefinedTypes": false, "diagnostics.undefinedFunctions": false, "diagnostics.undefinedConstants": false, "diagnostics.undefinedMethods": false, "diagnostics.undefinedProperties": false, "diagnostics.undefinedVariables": false, "diagnostics.unusedSymbols": false, "diagnostics.deprecated": false, "completion.insertUseDeclaration": true, "completion.fullyQualifyGlobalConstantsAndFunctions": false, "intelephense.stubs": ["Core", "standard", "SPL", "<PERSON><PERSON>", "reflection", "json", "mbstring", "pcre", "mysq<PERSON>", "pdo_mysql", "openssl", "date", "filter", "hash", "pcntl", "session", "xml", "curl", "zip"], "stubs": ["Core", "standard", "SPL", "<PERSON><PERSON>", "reflection", "json", "mbstring", "pcre", "mysq<PERSON>", "pdo_mysql", "openssl", "date", "filter", "hash", "pcntl", "session", "xml", "curl", "zip"]}