# 抖音API接口测试指南

## 📖 接口概述

本文档描述了抖音 client_token 获取接口的测试方法和使用说明。

## 🔗 接口信息

### 获取抖音客户端访问令牌

**接口地址：** `POST /api/douyin/client-token`

**请求方式：** POST

**Content-Type：** application/json

## 📋 请求参数

该接口无需传递参数，client_key 和 client_secret 已在控制器中配置。

## 📤 响应格式

### 成功响应示例

```json
{
    "code": 200,
    "message": "获取抖音client_token和ticket成功",
    "data": {
        "request_params": {
            "client_key": "aw1fufi86b0ef0tq",
            "client_secret": "a393ed92791a4b5b0d26ec14e6334802",
            "grant_type": "client_credential"
        },
        "douyin_response": {
            "access_token": "act.xxxxxxxxxxxxxxxxxxxxx",
            "expires_in": 7200,
            "token_type": "Bearer",
            "scope": "user_info"
        },
        "token_info": {
            "access_token": "act.xxxxxxxxxxxxxxxxxxxxx",
            "expires_in": 7200,
            "token_type": "Bearer",
            "scope": "user_info"
        },
        "ticket_info": {
            "success": true,
            "ticket_response": {
                "ticket": "bkt.xxxxxxxxxxxxxxxxxxxxx",
                "expires_in": 7200
            },
            "request_url": "https://open.douyin.com/open/getticket/",
            "request_headers": {
                "Content-Type": "application/json",
                "access-token": "act.xxxxxx..."
            }
        },
        "from_cache": false
    },
    "timestamp": 1752130495
}
```

### 缓存响应示例

```json
{
    "code": 200,
    "message": "获取抖音client_token成功（缓存）",
    "data": {
        "request_params": {
            "client_key": "aw1fufi86b0ef0tq",
            "client_secret": "a393ed92791a4b5b0d26ec14e6334802",
            "grant_type": "client_credential"
        },
        "douyin_response": {
            "access_token": "act.xxxxxxxxxxxxxxxxxxxxx",
            "expires_in": 7200,
            "token_type": "Bearer",
            "scope": "user_info"
        },
        "token_info": {
            "access_token": "act.xxxxxxxxxxxxxxxxxxxxx",
            "expires_in": 7200,
            "token_type": "Bearer",
            "scope": "user_info"
        },
        "ticket_info": {
            "success": true,
            "ticket_response": {
                "ticket": "bkt.xxxxxxxxxxxxxxxxxxxxx",
                "expires_in": 7200
            },
            "request_url": "https://open.douyin.com/open/getticket/",
            "request_headers": {
                "Content-Type": "application/json",
                "access-token": "act.xxxxxx..."
            }
        },
        "from_cache": true
    },
    "timestamp": 1752130495
}
```

### 错误响应示例

```json
{
    "code": 400,
    "message": "抖音API调用失败：400",
    "timestamp": 1752130495
}
```

## 🧪 测试方法

### 1. 使用 curl 测试

```bash
curl -X POST http://your-domain.com/api/douyin/client-token \
  -H "Content-Type: application/json" \
  -H "Accept: application/json"
```

### 2. 使用 Postman 测试

1. **创建新请求**
   - 方法：POST
   - URL：`http://your-domain.com/api/douyin/client-token`

2. **设置请求头**
   - Content-Type: application/json
   - Accept: application/json

3. **发送请求**
   - 无需设置请求体
   - 点击 Send 按钮

### 3. 使用 JavaScript 测试

```javascript
fetch('/api/douyin/client-token', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
})
.then(response => response.json())
.then(data => {
    console.log('抖音token和ticket获取成功:', data);
    if (data.code === 200) {
        const token = data.data.token_info.access_token;
        const ticketInfo = data.data.ticket_info;

        console.log('Access Token:', token);
        console.log('Ticket获取状态:', ticketInfo.success);

        if (ticketInfo.success) {
            console.log('Ticket响应:', ticketInfo.ticket_response);
        } else {
            console.log('Ticket获取失败:', ticketInfo.error);
        }
    }
})
.catch(error => {
    console.error('请求失败:', error);
});
```

## 🔧 配置说明

### 应用凭据配置

在 `app/Http/Controllers/Api/DouyinController.php` 中配置：

```php
private $clientKey = 'ttxxxxxx';  // 替换为实际的client_key
private $clientSecret = 'cbs***'; // 替换为实际的client_secret
```

### 缓存配置

- **缓存键格式：** `douyin_client_token:{client_key}`
- **缓存时间：** 根据抖音返回的 `expires_in` 字段（默认7200秒）
- **缓存驱动：** 使用 Laravel 默认缓存驱动

## 📊 响应字段说明

| 字段名                           | 类型    | 描述                     |
| -------------------------------- | ------- | ------------------------ |
| code                             | int     | 响应状态码，200表示成功  |
| message                          | string  | 响应消息                 |
| data.request_params              | object  | 发送给抖音的请求参数     |
| data.douyin_response             | object  | 抖音API的原始响应        |
| data.token_info                  | object  | 解析后的token信息        |
| data.ticket_info                 | object  | ticket获取结果           |
| data.ticket_info.success         | boolean | ticket获取是否成功       |
| data.ticket_info.ticket_response | object  | 抖音ticket接口的原始响应 |
| data.ticket_info.request_url     | string  | ticket请求的URL          |
| data.ticket_info.request_headers | object  | ticket请求的头部信息     |
| data.from_cache                  | boolean | token是否来自缓存        |
| timestamp                        | int     | 响应时间戳               |

## 🚨 注意事项

1. **应用凭据安全**
   - 请妥善保管 client_key 和 client_secret
   - 建议将凭据配置在环境变量中

2. **缓存机制**
   - Token 会自动缓存，避免频繁请求
   - 缓存时间根据抖音返回的有效期设置

3. **错误处理**
   - 接口包含完善的错误处理机制
   - 所有错误都会记录到日志中

4. **请求限制**
   - 遵循抖音API的调用频率限制
   - 建议使用缓存的token，减少API调用

## 🔍 故障排除

### 常见问题

1. **401 Unauthorized**
   - 检查 client_key 和 client_secret 是否正确
   - 确认应用是否已在抖音开放平台注册

2. **网络超时**
   - 检查网络连接
   - 确认抖音API服务是否正常

3. **缓存问题**
   - 清除缓存：`php artisan cache:clear`
   - 检查缓存驱动配置

### 调试方法

1. **查看日志**
   ```bash
   tail -f storage/logs/laravel.log
   ```

2. **测试网络连接**
   ```bash
   curl -I https://open.douyin.com/oauth/client_token/
   ```

## 📝 更新日志

- **v1.0.0** - 初始版本，实现基础的client_token获取功能
- 支持缓存机制
- 完善的错误处理和日志记录
