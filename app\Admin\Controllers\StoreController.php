<?php

namespace App\Admin\Controllers;

use App\Models\Store;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;

class StoreController extends AdminController
{
    /**
     * 页面标题
     *
     * @var string
     */
    protected $title = '商铺管理';

    /**
     * 列表页面
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new Store());

        $grid->column('id', 'ID');
        $grid->column('name', '商铺名称');
        $grid->column('address', '地址');
        $grid->column('created_at', '创建时间');
        $grid->column('updated_at', '更新时间');

        return $grid;
    }

    /**
     * 详情页面
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(Store::findOrFail($id));

        $show->field('id', 'ID');
        $show->field('name', '商铺名称');
        $show->field('address', '地址');
        $show->field('created_at', '创建时间');
        $show->field('updated_at', '更新时间');

        return $show;
    }

    /**
     * 表单页面
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new Store());

        $form->text('name', '商铺名称')->required();
        $form->text('address', '地址')->required();

        return $form;
    }
}
