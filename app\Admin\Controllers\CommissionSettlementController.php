<?php

namespace App\Admin\Controllers;

use App\Models\CommissionSettlement;
use App\Models\Store;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Layout\Content;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Log;
use Exception;
use Illuminate\Http\JsonResponse;
use Encore\Admin\Facades\Admin;
use Encore\Admin\Show;

class CommissionSettlementController extends AdminController
{
    protected $title = '佣金结算管理';

    public function generateManual(Request $request)
    {
        try {
            Artisan::call('settlements:generate');
            $output = Artisan::output();
            return response()->json(['status' => true, 'message' => '结算单生成成功！', 'output' => $output]);
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => '生成失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 显示结算详情页面（资源路由的show方法）
     */
    public function show($id, Content $content)
    {
        // 重定向到详情页面
        return redirect()->route('admin.commission-settlements.detail', $id);
    }

    /**
     * 显示结算详情页面
     */
    public function detail($id)
    {
        return Admin::content(function (Content $content) use ($id) {
            $content->header('结算详情');
            $content->description('查看佣金结算详细信息');

            // 获取结算数据
            $settlement = CommissionSettlement::findOrFail($id);

            // 获取商铺数据
            $stores = collect();
            $agentRegion = null;
            if ($settlement->target_type === 'agent') {
                $stores = $settlement->getStoresData();

                // 获取代理商区域信息（用于显示计算公式）
                $agentRegion = \App\Models\AgentRegion::where('agent_id', $settlement->target_id)
                    ->where('region_status', 'active')
                    ->where('contract_status', 'signed')
                    ->first();
            }

            $content->body(view('admin.commission_settlements.detail', [
                'id' => $id
            ]));
        });
    }

    /**
     * 获取结算详情API数据
     */
    public function apiDetail($id)
    {
        try {
            // 记录请求日志
            Log::info('Loading commission settlement detail', [
                'id' => $id,
                'url' => request()->url(),
                'user' => auth()->user() ? auth()->user()->name : 'guest'
            ]);

            // 获取结算信息
            $settlement = CommissionSettlement::findOrFail($id);

            // 格式化结算数据 - 包含commission_settlements表的所有字段
            $settlementData = [
                'id' => $settlement->id,
                'settlement_no' => $settlement->settlement_no,
                'target_type' => $settlement->target_type,
                'target_id' => $settlement->target_id,
                'target_name' => $settlement->target_name,
                'settlement_period_start' => $settlement->settlement_period_start?->format('Y-m-d'),
                'settlement_period_end' => $settlement->settlement_period_end?->format('Y-m-d'),
                'total_amount' => $settlement->total_amount,
                'commission_amount' => $settlement->commission_amount,
                'deduction_amount' => $settlement->deduction_amount,
                'actual_amount' => $settlement->actual_amount,
                'store_count' => $settlement->store_count,
                'active_store_count' => $settlement->active_store_count,
                'status' => $settlement->status,
                'remark' => $settlement->remark,
                'detail_data' => $settlement->detail_data,
                'approved_at' => $settlement->approved_at?->format('Y-m-d H:i:s'),
                'approved_by' => $settlement->approved_by,
                'approver_name' => $settlement->approver_name,
                'paid_at' => $settlement->paid_at?->format('Y-m-d H:i:s'),
                'payment_method' => $settlement->payment_method,
                'payment_reference' => $settlement->payment_reference,
                'created_at' => $settlement->created_at?->format('Y-m-d H:i:s'),
                'updated_at' => $settlement->updated_at?->format('Y-m-d H:i:s')
            ];

            // 获取关联商铺数据和代理商区域信息
            $storesData = collect();
            $agentRegion = null;
            if ($settlement->target_type === 'agent') {
                $storesData = $settlement->getStoresData();

                // 获取代理商区域信息（用于显示计算公式）
                $agentRegion = \App\Models\AgentRegion::where('agent_id', $settlement->target_id)
                    ->where('region_status', 'active')
                    ->where('contract_status', 'signed')
                    ->first();
            }

            // 返回成功响应
            return response()->json([
                'status' => true,
                'message' => '获取数据成功',
                'settlement' => $settlementData,
                'stores' => $storesData, // 返回所有商铺数据
                'store_count' => $storesData->count(),
                'agent_region' => $agentRegion // 返回代理商区域信息用于计算公式
            ]);
        } catch (\Exception $e) {
            // 记录错误日志
            Log::error('Failed to load commission settlement detail', [
                'id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 返回错误响应
            return response()->json([
                'status' => false,
                'message' => '加载数据失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * API接口：获取关联商铺数据
     * 作者: Assistant
     * 严格遵循数据层与视图层分离原则
     */
    public function getStores($id)
    {
        try {
            $settlement = CommissionSettlement::findOrFail($id);
            $stores = $settlement->getStoresData();

            return response()->json([
                'stores' => $stores,
                'total' => $stores->count()
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => '获取商铺数据失败: ' . $e->getMessage()], 500);
        }
    }

    protected function grid()
    {
        $grid = new Grid(new CommissionSettlement());
        $grid->disableActions(); // 先禁用所有操作
        // 禁用创建按钮，结算应该由系统自动生成
        $grid->disableCreateButton();

        // 手动生成结算单按钮
        $grid->tools(function ($tools) {
            $route = route('admin.agent-commissions.generate');
            $tools->append(
                <<<HTML
                <button id="btn-generate-settlement" class="btn btn-success" style="margin-right:10px;">
                    <i class="fa fa-refresh"></i> 手动生成结算单
                </button>
                <script>
                $(function(){
                    $("#btn-generate-settlement").click(function(){
                        if(confirm("确定要手动生成结算单吗？")) {
                            $.post("{$route}", {_token: LA.token}, function(res){
                                if(res.status){
                                    $.admin.toastr.success(res.message);
                                    setTimeout(function(){ location.reload(); }, 1500);
                                }else{
                                    $.admin.toastr.error(res.message);
                                }
                            });
                        }
                    });
                });
                </script>
            HTML
            );
        });

        // 基础信息
        $grid->column('settlement_no', '结算单号');
        $grid->column('target_type', '结算对象类型')->display(function ($value) {
            $types = [
                'agent' => '代理商',
                'salesperson' => '业务员',
                'team_leader' => '团队长'
            ];
            return $types[$value] ?? $value;
        });
        $grid->column('target_name', '结算对象名称')->display(function ($value) {
            $type = $this->target_type ?? null;
            $id = $this->target_id ?? null;
            if ($type === 'agent') {
                $agent = \App\Models\Agent::find($id);
                return $agent ? ($agent->name . '（' . $agent->id . '）') : '未知代理商（' . $id . '）';
            } elseif ($type === 'salesperson') {
                $salesperson = \App\Models\Salesperson::find($id);
                return $salesperson ? ($salesperson->name . '（' . $salesperson->id . '）') : '未知业务员（' . $id . '）';
            } elseif ($type === 'team_leader') {
                $team = \App\Models\Team::find($id);
                return $team ? ($team->name . '（' . $team->id . '）') : '未知团队（' . $id . '）';
            }
            return '未知对象（' . $id . '）';
        });

        // 结算周期
        $grid->column('settlement_period_start', '结算开始日期');
        $grid->column('settlement_period_end', '结算结束日期');

        // 金额信息
        $grid->column('total_amount', '总金额')->display(function ($value) {
            return "￥" . number_format($value, 2);
        });
        $grid->column('actual_amount', '实际结算金额')->display(function ($value) {
            return "￥" . number_format($value, 2);
        });

        // 状态
        $grid->column('status', '状态')->display(function ($value) {
            $statusMap = [
                'pending' => '待处理',
                'processing' => '处理中',
                'completed' => '已完成',
                'failed' => '失败'
            ];
            return $statusMap[$value] ?? $value;
        })->sortable();

        // 时间信息
        $grid->column('created_at', '创建时间');
        $grid->column('paid_at', '支付时间');

        // 添加操作列
        $grid->column('操作')->display(function () {
            $id = $this->id ?? 0;
            if (!$id) {
                return '-';
            }

            $detailUrl = admin_url('commission-settlements/' . $id . '/detail');
            $editUrl = admin_url('commission-settlements/' . $id . '/edit');

            return '<a href="' . $detailUrl . '" class="btn btn-xs btn-primary" "><i class="fa fa-eye"></i> 详情</a>'
                . '<a href="' . $editUrl . '" class="btn btn-xs btn-info" "><i class="fa fa-edit"></i> 编辑</a>'
                . '<a href="javascript:void(0);" class="btn btn-xs btn-danger grid-row-delete" data-id="' . $id . '" ><i class="fa fa-trash"></i> 删除</a>';
        });

        // 配置操作列
        $grid->actions(function ($actions) {
            // 禁用所有默认按钮
            $actions->disableView();
            $actions->disableEdit();
            $actions->disableDelete();
        });

        // 添加删除操作的JavaScript代码
        Admin::script(
            <<<SCRIPT
            $('.grid-row-delete').unbind('click').click(function() {
                var id = $(this).data('id');
                
                swal({
                    title: "确认删除？",
                    text: "删除后数据将无法恢复，确定要删除吗？",
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#DD6B55",
                    confirmButtonText: "确认",
                    cancelButtonText: "取消",
                    closeOnConfirm: false
                }).then(function(result) {
                    if (result.value) {
                        $.ajax({
                            method: 'DELETE',
                            url: '/admin/commission-settlements/' + id,
                            data: {
                                _method:'delete',
                                _token: LA.token
                            },
                            success: function (data) {
                                $.pjax.reload('#pjax-container');
                                if (typeof data === 'object') {
                                    if (data.status) {
                                        swal(data.message, '', 'success');
                                    } else {
                                        swal(data.message, '', 'error');
                                    }
                                }
                            }
                        });
                    }
                });
            });
SCRIPT
        );

        // 筛选器
        $grid->filter(function ($filter) {
            $filter->disableIdFilter();
            $filter->like('settlement_no', '结算单号');
            $filter->equal('target_type', '结算对象类型')->select([
                'agent' => '代理商',
                'salesperson' => '业务员',
                'team_leader' => '团队长'
            ]);
            $filter->equal('status', '状态')->select([
                'pending' => '待审核',
                'approved' => '已审核',
                'paid' => '已支付',
                'rejected' => '已拒绝'
            ]);
            $filter->between('settlement_period_start', '结算开始日期')->date();
            $filter->between('settlement_period_end', '结算结束日期')->date();
            $filter->between('created_at', '创建时间')->datetime();
        });

        return $grid;
    }

    protected function form()
    {
        $form = new Form(new CommissionSettlement());

        // 基础信息
        $form->display('settlement_no', '结算单号');
        $form->display('target_type', '结算对象类型');
        $form->display('target_id', '结算对象ID');

        // 结算周期
        $form->display('settlement_period_start', '结算周期开始');
        $form->display('settlement_period_end', '结算周期结束');

        // 金额信息
        $form->display('total_amount', '总金额');
        $form->display('commission_amount', '佣金金额');
        $form->display('deduction_amount', '扣除金额');
        $form->display('actual_amount', '实际结算金额');

        // 状态和备注
        $form->radio('status', '状态')
            ->options([
                'pending' => '待审核',
                'approved' => '已审核',
                'paid' => '已支付',
                'rejected' => '已拒绝'
            ])
            ->when('approved', function (Form $form) {
                $form->datetime('approved_at', '审核时间')->default(now());
                $form->hidden('approved_by')->default(Auth::guard('admin')->id());
            })
            ->when('paid', function (Form $form) {
                $form->datetime('paid_at', '支付时间')->default(now());
                $form->text('payment_method', '支付方式');
                $form->file('payment_reference', '支付凭证');
            });

        $form->textarea('remark', '备注');

        // 禁用删除按钮
        $form->tools(function (Form\Tools $tools) {
            $tools->disableDelete();
        });

        return $form;
    }
}
