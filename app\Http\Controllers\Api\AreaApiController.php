<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Area;
use Illuminate\Http\Request;

/**
 * 地区API控制器
 * 提供省市区三级联动数据（无需认证）
 * 
 * <AUTHOR>
 */
class AreaApiController extends Controller
{
    /**
     * 获取所有省份
     * 
     * @api {get} /api/areas/provinces 获取省份列表
     * @apiName GetProvinces
     * @apiGroup Area
     * @apiVersion 1.0.0
     * @apiDescription 获取所有省份数据
     * 
     * @apiSuccess {Boolean} success 操作状态
     * @apiSuccess {Object} data 省份数据
     * @apiSuccessExample {json} 成功响应:
     * {
     *   "success": true,
     *   "data": {
     *     "110000": "北京",
     *     "120000": "天津"
     *   }
     * }
     */
    public function provinces()
    {
        try {
            $provinces = Area::where('level', 1)->pluck('name', 'id');
            
            return response()->json([
                'success' => true,
                'data' => $provinces
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取省份数据失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取指定省份的城市
     * 
     * @api {get} /api/areas/cities 获取城市列表
     * @apiName GetCities
     * @apiGroup Area
     * @apiVersion 1.0.0
     * @apiDescription 根据省份ID获取城市数据
     * 
     * @apiParam {Number} province_id 省份ID
     * 
     * @apiSuccess {Number} code 状态码
     * @apiSuccess {Array} data 城市数据数组
     * @apiSuccessExample {json} 成功响应:
     * {
     *   "code": 200,
     *   "data": [
     *     {"id": "110100", "name": "北京市"}
     *   ]
     * }
     * 
     * @apiError {String} message 错误信息
     * @apiErrorExample {json} 错误响应:
     * {
     *   "code": 400,
     *   "message": "省份ID不能为空"
     * }
     */
    public function cities(Request $request)
    {
        try {
            $provinceId = $request->get('province_id');
            
            if (empty($provinceId)) {
                return response()->json([
                    'code' => 400,
                    'message' => '省份ID不能为空'
                ], 400);
            }
            
            $cities = Area::where('pid', $provinceId)
                         ->where('level', 2)
                         ->get(['id', 'name'])
                         ->map(function($city) {
                             return [
                                 'id' => $city->id,
                                 'name' => $city->name
                             ];
                         });
            
            return response()->json([
                'code' => 200,
                'data' => $cities->values()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '获取城市数据失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取指定城市的区县
     * 
     * @api {get} /api/areas/districts 获取区县列表
     * @apiName GetDistricts
     * @apiGroup Area
     * @apiVersion 1.0.0
     * @apiDescription 根据城市ID获取区县数据
     * 
     * @apiParam {Number} city_id 城市ID
     * 
     * @apiSuccess {Number} code 状态码
     * @apiSuccess {Array} data 区县数据数组
     * @apiSuccessExample {json} 成功响应:
     * {
     *   "code": 200,
     *   "data": [
     *     {"id": "110101", "name": "东城区"},
     *     {"id": "110102", "name": "西城区"}
     *   ]
     * }
     * 
     * @apiError {String} message 错误信息
     * @apiErrorExample {json} 错误响应:
     * {
     *   "code": 400,
     *   "message": "城市ID不能为空"
     * }
     */
    public function districts(Request $request)
    {
        try {
            $cityId = $request->get('city_id');
            
            if (empty($cityId)) {
                return response()->json([
                    'code' => 400,
                    'message' => '城市ID不能为空'
                ], 400);
            }
            
            $districts = Area::where('pid', $cityId)
                           ->where('level', 3)
                           ->get(['id', 'name'])
                           ->map(function($district) {
                               return [
                                   'id' => $district->id,
                                   'name' => $district->name
                               ];
                           });
            
            return response()->json([
                'code' => 200,
                'data' => $districts->values()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '获取区县数据失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 根据地区代码获取地区信息
     * 
     * @api {get} /api/areas/info 获取地区信息
     * @apiName GetAreaInfo
     * @apiGroup Area
     * @apiVersion 1.0.0
     * @apiDescription 根据地区ID获取详细信息
     * 
     * @apiParam {Number} area_id 地区ID
     * 
     * @apiSuccess {Boolean} success 操作状态
     * @apiSuccess {Object} data 地区信息
     * @apiSuccessExample {json} 成功响应:
     * {
     *   "success": true,
     *   "data": {
     *     "id": 110000,
     *     "name": "北京",
     *     "level": 1,
     *     "pid": 100000,
     *     "full_name": "北京"
     *   }
     * }
     */
    public function info(Request $request)
    {
        try {
            $areaId = $request->get('area_id');
            
            if (empty($areaId)) {
                return response()->json([
                    'success' => false,
                    'message' => '地区ID不能为空'
                ], 400);
            }
            
            $area = Area::find($areaId);
            
            if (!$area) {
                return response()->json([
                    'success' => false,
                    'message' => '地区不存在'
                ], 404);
            }
            
            // 构建完整地区名称
            $fullName = $this->buildFullName($area);
            
            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $area->id,
                    'name' => $area->name,
                    'level' => $area->level,
                    'pid' => $area->pid,
                    'full_name' => $fullName
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '获取地区信息失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 构建完整地区名称
     */
    private function buildFullName($area)
    {
        $names = [$area->name];
        $current = $area;
        
        while ($current->pid && $current->pid != 100000) {
            $parent = Area::find($current->pid);
            if ($parent) {
                array_unshift($names, $parent->name);
                $current = $parent;
            } else {
                break;
            }
        }
        
        return implode(' ', $names);
    }
} 