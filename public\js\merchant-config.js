/**
 * 商铺配置页面JavaScript
 */
$(document).ready(function () {
    const container = $('#merchant-config-container');
    const storeId = container.data('store-id');
    const apiUrl = container.data('api-url');

    // 页面加载时获取商铺详情
    loadStoreDetails();

    // 表单提交事件
    $('#store-config-form').on('submit', function (e) {
        e.preventDefault();
        saveStoreConfig();
    });

    /**
     * 加载商铺详情
     */
    function loadStoreDetails() {
        $.ajax({
            url: apiUrl,
            type: 'GET',
            dataType: 'json',
            success: function (response) {
                if (response.success) {
                    populateForm(response.data);
                    $('#config-loading').hide();
                    $('#config-form').show();
                } else {
                    showError(response.message || '加载商铺信息失败');
                }
            },
            error: function (xhr, status, error) {
                console.error('加载商铺详情失败:', error);
                showError('网络错误，请检查网络连接');
            }
        });
    }

    /**
     * 填充表单数据
     */
    function populateForm(data) {
        // 基本信息
        $('#store-name').val(data.name || '');
        $('#agent-name').val(data.agent ? data.agent.name : '');

        // 权限配置
        const permissions = data.permissions || [];
        $('input[name="permissions[]"]').each(function () {
            const permission = $(this).val();
            if (permissions.includes(permission)) {
                $(this).prop('checked', true);
            }
        });

        // 状态配置
        $('#audit-status').val(data.audit_status || 'pending');
        $('#store-status').val(data.status || 'active');
        $('#audit-remark').val(data.audit_remark || '');
    }

    /**
     * 保存商铺配置
     */
    function saveStoreConfig() {
        const formData = new FormData($('#store-config-form')[0]);

        // 添加商铺ID
        formData.append('store_id', storeId);

        // 显示加载状态
        const submitBtn = $('#store-config-form button[type="submit"]');
        const originalText = submitBtn.html();
        submitBtn.html('<i class="fa fa-spinner fa-spin"></i> 保存中...').prop('disabled', true);

        $.ajax({
            url: '/admin/merchant/api/update-config',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            dataType: 'json',
            success: function (response) {
                if (response.success) {
                    // 显示成功消息
                    toastr.success('商铺配置保存成功');

                    // 可选：跳转回列表页
                    setTimeout(function () {
                        window.location.href = '/admin/merchant/list';
                    }, 1000);
                } else {
                    toastr.error(response.message || '保存失败');
                }
            },
            error: function (xhr, status, error) {
                console.error('保存商铺配置失败:', error);

                // 尝试解析错误信息
                let errorMessage = '保存失败，请重试';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.responseText) {
                    try {
                        const errorData = JSON.parse(xhr.responseText);
                        errorMessage = errorData.message || errorMessage;
                    } catch (e) {
                        // 解析失败，使用默认错误信息
                    }
                }

                toastr.error(errorMessage);
            },
            complete: function () {
                // 恢复按钮状态
                submitBtn.html(originalText).prop('disabled', false);
            }
        });
    }

    /**
     * 显示错误信息
     */
    function showError(message) {
        $('#config-loading').hide();
        $('#config-error-message').text(message);
        $('#config-error').show();
    }

    /**
     * 权限配置交互
     */

    // 权限复选框变化时的处理
    $('input[name="permissions[]"]').on('change', function () {
        const checkedCount = $('input[name="permissions[]"]:checked').length;
        if (checkedCount === 0) {
            toastr.warning('请至少选择一个权限');
        }
    });

    // 状态变化时的处理
    $('#audit-status').on('change', function () {
        const status = $(this).val();
        if (status === 'rejected') {
            $('#audit-remark').attr('placeholder', '请填写拒绝原因').focus();
        } else {
            $('#audit-remark').attr('placeholder', '审核备注（可选）');
        }
    });
}); 