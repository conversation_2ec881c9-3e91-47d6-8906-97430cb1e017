@extends('admin::layouts.content')

@section('content')
<div class="row">
    <div class="col-md-12">
        <div class="box box-primary">
            <div class="box-header with-border">
                <h3 class="box-title">
                    <i class="fa fa-sitemap"></i> {{ $agent->name }} - 层级详情
                </h3>
                <div class="box-tools pull-right">
                    <a href="{{ admin_url('agents/hierarchy') }}" class="btn btn-sm btn-default">
                        <i class="fa fa-arrow-left"></i> 返回层级管理
                    </a>
                </div>
            </div>
            <div class="box-body">
                <!-- 基本信息 -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="info-box">
                            <span class="info-box-icon bg-blue"><i class="fa fa-user"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">代理商信息</span>
                                <span class="info-box-number">{{ $agent->name }}</span>
                                <div class="progress">
                                    <div class="progress-bar" style="width: 100%"></div>
                                </div>
                                <span class="progress-description">
                                    {{ $agent->contact_person }} | {{ $agent->phone }}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-box">
                            <span class="info-box-icon bg-green"><i class="fa fa-level-up"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">代理商等级</span>
                                <span class="info-box-number">
                                    @if($agent->level == 1)
                                    一级代理商
                                    @elseif($agent->level == 2)
                                    二级代理商
                                    @else
                                    未设置
                                    @endif
                                </span>
                                <div class="progress">
                                    <div class="progress-bar" style="width: 100%"></div>
                                </div>
                                <span class="progress-description">
                                    状态:
                                    @if($agent->status == 'active')
                                    <span class="label label-success">正常</span>
                                    @else
                                    <span class="label label-danger">异常</span>
                                    @endif
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 层级路径 -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="box box-info">
                            <div class="box-header with-border">
                                <h3 class="box-title">
                                    <i class="fa fa-road"></i> 层级路径
                                </h3>
                            </div>
                            <div class="box-body">
                                <div class="hierarchy-path">
                                    @php
                                    $path = $agent->getHierarchyPath();
                                    @endphp
                                    @foreach($path as $index => $pathAgent)
                                    <div class="path-item {{ $pathAgent['id'] == $agent->id ? 'current' : '' }}">
                                        <div class="path-icon">
                                            <i class="fa fa-user-circle"></i>
                                        </div>
                                        <div class="path-info">
                                            <h4>{{ $pathAgent['name'] }}</h4>
                                            <p>{{ $pathAgent['level_label'] }}</p>
                                        </div>
                                    </div>
                                    @if($index < count($path) - 1)
                                        <div class="path-arrow">
                                        <i class="fa fa-arrow-right"></i>
                                </div>
                                @endif
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 统计信息 -->
            <div class="row">
                <div class="col-md-6">
                    <div class="box box-success">
                        <div class="box-header with-border">
                            <h3 class="box-title">
                                <i class="fa fa-users"></i> 下级代理商管理
                            </h3>
                            <div class="box-tools pull-right">
                                @if($agent->level == 1 && $agent->canAddSubAgent())
                                <a href="{{ admin_url('agents/' . $agent->id . '/create-sub-agent') }}"
                                    class="btn btn-sm btn-success">
                                    <i class="fa fa-plus"></i> 添加下级代理商
                                </a>
                                @endif
                            </div>
                        </div>
                        <div class="box-body">
                            <div class="progress-group">
                                <span class="progress-text">
                                    当前下级代理商数量: {{ $agent->current_sub_agents }}/{{ $agent->max_sub_agents }}
                                </span>
                                <span class="float-right">
                                    <b>{{ $agent->max_sub_agents > 0 ? round(($agent->current_sub_agents / $agent->max_sub_agents) * 100, 1) : 0 }}%</b>
                                </span>
                                <div class="progress progress-sm">
                                    <div class="progress-bar progress-bar-green"
                                        style="width: {{ $agent->max_sub_agents > 0 ? ($agent->current_sub_agents / $agent->max_sub_agents) * 100 : 0 }}%">
                                    </div>
                                </div>
                            </div>

                            <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>代理商名称</th>
                                            <th>联系人</th>
                                            <th>电话</th>
                                            <th>状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse($agent->subAgents as $subAgent)
                                        <tr>
                                            <td>{{ $subAgent->name }}</td>
                                            <td>{{ $subAgent->contact_person }}</td>
                                            <td>{{ $subAgent->phone }}</td>
                                            <td>
                                                @if($subAgent->status == 'active')
                                                <span class="label label-success">正常</span>
                                                @else
                                                <span class="label label-danger">异常</span>
                                                @endif
                                            </td>
                                            <td>
                                                <a href="{{ admin_url('agents/' . $subAgent->id . '/hierarchy-detail') }}"
                                                    class="btn btn-xs btn-primary">
                                                    <i class="fa fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                        @empty
                                        <tr>
                                            <td colspan="5" class="text-center text-muted">暂无下级代理商</td>
                                        </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="box box-warning">
                        <div class="box-header with-border">
                            <h3 class="box-title">
                                <i class="fa fa-shopping-bag"></i> 直推商铺管理
                            </h3>
                            <div class="box-tools pull-right">
                                <a href="{{ admin_url('agents/' . $agent->id . '/edit-limits') }}"
                                    class="btn btn-sm btn-warning">
                                    <i class="fa fa-edit"></i> 编辑限制
                                </a>
                            </div>
                        </div>
                        <div class="box-body">
                            <div class="progress-group">
                                <span class="progress-text">
                                    当前直推商铺数量: {{ $agent->current_direct_stores }}/{{ $agent->max_direct_stores }}
                                </span>
                                <span class="float-right">
                                    <b>{{ $agent->max_direct_stores > 0 ? round(($agent->current_direct_stores / $agent->max_direct_stores) * 100, 1) : 0 }}%</b>
                                </span>
                                <div class="progress progress-sm">
                                    <div class="progress-bar progress-bar-yellow"
                                        style="width: {{ $agent->max_direct_stores > 0 ? ($agent->current_direct_stores / $agent->max_direct_stores) * 100 : 0 }}%">
                                    </div>
                                </div>
                            </div>

                            <!-- 商铺列表 -->
                            <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>商铺名称</th>
                                            <th>状态</th>
                                            <th>创建时间</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @php
                                        // 这里需要根据实际的商铺关联关系来获取数据
                                        $stores = collect(); // 暂时使用空集合
                                        @endphp
                                        @forelse($stores as $store)
                                        <tr>
                                            <td>{{ $store->name }}</td>
                                            <td>
                                                <span class="label label-success">正常</span>
                                            </td>
                                            <td>{{ $store->created_at->format('Y-m-d') }}</td>
                                        </tr>
                                        @empty
                                        <tr>
                                            <td colspan="3" class="text-center text-muted">暂无直推商铺</td>
                                        </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="row">
                <div class="col-md-12">
                    <div class="box box-default">
                        <div class="box-header with-border">
                            <h3 class="box-title">
                                <i class="fa fa-cogs"></i> 管理操作
                            </h3>
                        </div>
                        <div class="box-body">
                            <div class="btn-group">
                                <a href="{{ admin_url('agents/' . $agent->id . '/edit') }}"
                                    class="btn btn-primary">
                                    <i class="fa fa-edit"></i> 编辑代理商信息
                                </a>
                                <a href="{{ admin_url('agents/' . $agent->id . '/edit-limits') }}"
                                    class="btn btn-warning">
                                    <i class="fa fa-sliders"></i> 编辑数量限制
                                </a>
                                @if($agent->level == 1 && $agent->canAddSubAgent())
                                <a href="{{ admin_url('agents/' . $agent->id . '/create-sub-agent') }}"
                                    class="btn btn-success">
                                    <i class="fa fa-plus"></i> 添加下级代理商
                                </a>
                                @endif
                                <button type="button" class="btn btn-info" onclick="updateCounts({{ $agent->id }})">
                                    <i class="fa fa-refresh"></i> 更新统计数据
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
@endsection

@section('script')
<script>
    function updateCounts(agentId) {
        $.ajax({
            url: '{{ admin_url("agents") }}/' + agentId + '/update-counts',
            type: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    toastr.success('统计数据更新成功');
                    location.reload();
                } else {
                    toastr.error(response.message || '更新失败');
                }
            },
            error: function() {
                toastr.error('更新失败，请重试');
            }
        });
    }
</script>

<style>
    .hierarchy-path {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-wrap: wrap;
        padding: 20px;
    }

    .path-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        min-width: 120px;
        margin: 0 10px;
    }

    .path-item.current {
        background-color: #f0f8ff;
        border-radius: 8px;
        padding: 10px;
        border: 2px solid #007bff;
    }

    .path-icon {
        font-size: 24px;
        color: #007bff;
        margin-bottom: 5px;
    }

    .path-info h4 {
        margin: 0;
        font-size: 14px;
        font-weight: bold;
    }

    .path-info p {
        margin: 0;
        font-size: 12px;
        color: #666;
    }

    .path-arrow {
        font-size: 20px;
        color: #007bff;
        margin: 0 15px;
    }

    .progress-group {
        margin-bottom: 15px;
    }

    .progress-text {
        font-size: 13px;
        font-weight: bold;
    }

    .float-right {
        float: right;
    }
</style>
@endsection