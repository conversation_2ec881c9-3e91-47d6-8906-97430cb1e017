<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            <label for="contract_status" class="required">合约状态 *</label>
            <select class="form-control" name="contract_status" id="contract_status" required>
                <option value="">请选择合约状态</option>
                <option value="draft" {{ (isset($id) && $agent_region && $agent_region->contract_status == 'draft') ? 'selected' : '' }}>草稿</option>
                <option value="reviewing" {{ (isset($id) && $agent_region && $agent_region->contract_status == 'reviewing') ? 'selected' : '' }}>审核中</option>
                <option value="signed" {{ (isset($id) && $agent_region && $agent_region->contract_status == 'signed') ? 'selected' : '' }}>已签署</option>
                <option value="expired" {{ (isset($id) && $agent_region && $agent_region->contract_status == 'expired') ? 'selected' : '' }}>已过期</option>
                <option value="terminated" {{ (isset($id) && $agent_region && $agent_region->contract_status == 'terminated') ? 'selected' : '' }}>已终止</option>
            </select>
            <small class="text-muted">合约的当前签署状态</small>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="form-group">
            <label for="contract_no">合约编号</label>
            <input type="text" class="form-control" name="contract_no" id="contract_no" placeholder="请输入合约编号" 
                   value="{{ isset($id) && $agent_region ? $agent_region->contract_no : '' }}">
            <small class="text-muted">已签署状态下必须填写合约编号</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            <label for="signed_at">签署日期</label>
            <input type="date" class="form-control" name="signed_at" id="signed_at" 
                   value="{{ isset($id) && $agent_region && $agent_region->signed_at ? $agent_region->signed_at->format('Y-m-d') : '' }}">
            <small class="text-muted">合约正式签署的日期</small>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="form-group">
            <label for="contract_end_date">合约到期日</label>
            <input type="date" class="form-control" name="contract_end_date" id="contract_end_date" 
                   value="{{ isset($id) && $agent_region && $agent_region->contract_end_date ? $agent_region->contract_end_date->format('Y-m-d') : '' }}">
            <small class="text-muted">合约有效期截止日期</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            <label for="contract_title">合约标题</label>
            <input type="text" class="form-control" name="contract_title" id="contract_title" placeholder="请输入合约标题" 
                   value="{{ isset($id) && $agent_region ? $agent_region->contract_title : '' }}">
            <small class="text-muted">合约的主要标题或名称</small>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="form-group">
            <label for="contract_start_date">合约开始日期</label>
            <input type="date" class="form-control" name="contract_start_date" id="contract_start_date" 
                   value="{{ isset($id) && $agent_region && $agent_region->contract_start_date ? $agent_region->contract_start_date->format('Y-m-d') : '' }}">
            <small class="text-muted">合约生效的开始日期</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label for="contract_file_url">合约文件</label>
            <div class="input-group">
                <input type="text" class="form-control" name="contract_file_url" id="contract_file_url" placeholder="合约文件URL或路径" 
                       value="{{ isset($id) && $agent_region ? $agent_region->contract_file_url : '' }}" readonly>
                <span class="input-group-btn">
                    <button type="button" class="btn btn-primary" id="uploadContractBtn">
                        <i class="fa fa-upload"></i> 上传文件
                    </button>
                    @if(isset($id) && $agent_region && $agent_region->contract_file_url)
                    <a href="{{ $agent_region->contract_file_url }}" target="_blank" class="btn btn-info">
                        <i class="fa fa-eye"></i> 查看
                    </a>
                    @endif
                </span>
            </div>
            <small class="text-muted">支持PDF、DOC、DOCX格式，文件大小不超过10MB</small>
            <input type="file" id="contractFileInput" style="display: none;" accept=".pdf,.doc,.docx">
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label for="contract_notes">合约备注</label>
            <textarea class="form-control" name="contract_notes" id="contract_notes" rows="4" 
                      placeholder="请输入合约相关备注信息...">{{ isset($id) && $agent_region ? $agent_region->contract_notes : '' }}</textarea>
            <small class="text-muted">记录合约的特殊条款、变更记录等信息</small>
        </div>
    </div>
</div>

<div class="alert alert-info" style="background-color: #d9edf7; border-color: #bce8f1; color: #31708f; border-radius: 6px; padding: 15px; margin-top: 20px; border: 1px solid transparent;">
    <i class="fa fa-file-text" style="margin-right: 8px;"></i>
    <strong>合约管理说明：</strong>
    <ul style="margin: 10px 0 0 0; padding-left: 20px;">
        <li>合约状态为"已签署"时，必须填写合约编号和签署日期</li>
        <li>建议上传电子版合约文件，便于后期查阅和管理</li>
        <li>合约到期前30天系统会自动提醒续签</li>
        <li>合约变更请及时更新状态和备注信息</li>
        @if(isset($id) && $agent_region)
        <li style="color: #5bc0de;">当前编辑已有合约，数据已自动填充到表单中</li>
        @endif
    </ul>
</div> 