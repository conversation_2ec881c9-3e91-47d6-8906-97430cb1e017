<?php

namespace App\Admin\Controllers;

use App\Models\Agent;
use App\Models\Store;
use Encore\Admin\Layout\Content;
use Encore\Admin\Layout\Row;
use Encore\Admin\Widgets\Box;
use Encore\Admin\Widgets\Table;
use Encore\Admin\Widgets\InfoBox;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;

/**
 * 代理商数据统计控制器
 * 
 * 负责代理商数据统计和报表展示
 * 
 * <AUTHOR>
 */
class AgentStatisticsController extends Controller
{
    /**
     * 统计首页
     *
     * @param Content $content
     * @return Content
     */
    public function index(Content $content)
    {
        return $content
            ->header('代理商数据统计')
            ->description('查看代理商的推广数据和佣金收入')
            ->row(function (Row $row) {
                $row->column(3, $this->totalAgentsBox());
                $row->column(3, $this->totalStoresBox());
                $row->column(3, $this->totalCommissionBox());
                $row->column(3, $this->totalActiveRateBox());
            })
            ->row(function (Row $row) {
                $row->column(8, $this->agentPerformanceChart());
                $row->column(4, $this->topAgentsBox());
            })
            ->row(function (Row $row) {
                $row->column(6, $this->merchantGrowthBox());
                $row->column(6, $this->commissionTrendBox());
            });
    }

    /**
     * 获取统计数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getData(Request $request)
    {
        $startDate = $request->input('start_date', date('Y-m-d', strtotime('-30 days')));
        $endDate = $request->input('end_date', date('Y-m-d'));
        $agentId = $request->input('agent_id');

        // 构建查询条件
        $query = DB::table('agents')
            ->leftJoin('stores', 'agents.id', '=', 'stores.agent_id')
            ->select(
                'agents.id',
                'agents.name',
                DB::raw('COUNT(stores.id) as store_count'),
                DB::raw('SUM(CASE WHEN stores.store_status = "active" THEN 1 ELSE 0 END) as active_store_count')
            )
            ->whereBetween('stores.created_at', [$startDate, $endDate . ' 23:59:59'])
            ->groupBy('agents.id', 'agents.name');

        if ($agentId) {
            $query->where('agents.id', $agentId);
        }

        $data = $query->get();

        return response()->json([
            'code' => 0,
            'msg' => 'success',
            'data' => $data
        ]);
    }

    /**
     * 导出数据
     *
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function export(Request $request)
    {
        $startDate = $request->input('start_date', date('Y-m-d', strtotime('-30 days')));
        $endDate = $request->input('end_date', date('Y-m-d'));
        $agentId = $request->input('agent_id');

        // 构建查询条件
        $query = DB::table('agents')
            ->leftJoin('stores', 'agents.id', '=', 'stores.agent_id')
            ->select(
                'agents.id',
                'agents.name',
                'agents.contact_person',
                'agents.phone',
                DB::raw('COUNT(stores.id) as store_count'),
                DB::raw('SUM(CASE WHEN stores.store_status = "active" THEN 1 ELSE 0 END) as active_store_count')
            )
            ->whereBetween('stores.created_at', [$startDate, $endDate . ' 23:59:59'])
            ->groupBy('agents.id', 'agents.name', 'agents.contact_person', 'agents.phone');

        if ($agentId) {
            $query->where('agents.id', $agentId);
        }

        $data = $query->get()->toArray();

        // 构建Excel数据
        $headers = [
            'ID', '代理商名称', '联系人', '联系电话', '商铺数量', '活跃商铺数量', '活跃率'
        ];

        $rows = [];
        foreach ($data as $item) {
            $activeRate = $item->store_count > 0 ? round(($item->active_store_count / $item->store_count) * 100, 2) : 0;
            $rows[] = [
                $item->id,
                $item->name,
                $item->contact_person,
                $item->phone,
                $item->store_count,
                $item->active_store_count,
                $activeRate . '%'
            ];
        }

        $filename = '代理商数据统计_' . date('YmdHis') . '.csv';
        $path = storage_path('app/public/' . $filename);

        $file = fopen($path, 'w');
        // 添加UTF-8 BOM头，解决中文乱码
        fputs($file, chr(0xEF) . chr(0xBB) . chr(0xBF));
        fputcsv($file, $headers);
        foreach ($rows as $row) {
            fputcsv($file, $row);
        }
        fclose($file);

        return response()->download($path, $filename)->deleteFileAfterSend(true);
    }

    /**
     * 代理商总数统计盒子
     *
     * @return InfoBox
     */
    protected function totalAgentsBox()
    {
        $count = Agent::where('status', 'active')->count();
        return new InfoBox('代理商总数', 'users', 'aqua', '/admin/agents', $count);
    }

    /**
     * 商铺总数统计盒子
     *
     * @return InfoBox
     */
    protected function totalStoresBox()
    {
        $count = Store::count();
        return new InfoBox('商铺总数', 'building', 'green', '/admin/stores', $count);
    }

    /**
     * 佣金总额统计盒子
     *
     * @return InfoBox
     */
    protected function totalCommissionBox()
    {
        // 这里应该从佣金表中查询实际数据
        $amount = '￥' . number_format(12345.67, 2);
        return new InfoBox('佣金总额', 'money', 'yellow', '/admin/agent-commissions', $amount);
    }

    /**
     * 商铺活跃率统计盒子
     *
     * @return InfoBox
     */
    protected function totalActiveRateBox()
    {
        $total = Store::count();
        $active = Store::where('store_status', 'active')->count();
        $rate = $total > 0 ? round(($active / $total) * 100, 2) : 0;
        return new InfoBox('商铺活跃率', 'line-chart', 'red', '/admin/stores', $rate . '%');
    }

    /**
     * 代理商业绩图表
     *
     * @return Box
     */
    protected function agentPerformanceChart()
    {
        $html = '<div id="agent-performance-chart" style="height: 350px;"></div>';
        $html .= '<script>
            $(function () {
                // 这里应该通过AJAX获取实际数据
                var agentNames = ["代理商A", "代理商B", "代理商C", "代理商D", "代理商E"];
                var storeCounts = [120, 80, 60, 45, 30];
                var commissionAmounts = [8000, 6000, 4500, 3000, 2000];
                
                var chart = echarts.init(document.getElementById("agent-performance-chart"));
                var option = {
                    tooltip: {
                        trigger: "axis",
                        axisPointer: {
                            type: "shadow"
                        }
                    },
                    legend: {
                        data: ["商铺数量", "佣金金额"]
                    },
                    xAxis: {
                        type: "category",
                        data: agentNames
                    },
                    yAxis: [
                        {
                            type: "value",
                            name: "商铺数量",
                            position: "left"
                        },
                        {
                            type: "value",
                            name: "佣金金额",
                            position: "right",
                            axisLabel: {
                                formatter: "￥{value}"
                            }
                        }
                    ],
                    series: [
                        {
                            name: "商铺数量",
                            type: "bar",
                            data: storeCounts
                        },
                        {
                            name: "佣金金额",
                            type: "line",
                            yAxisIndex: 1,
                            data: commissionAmounts
                        }
                    ]
                };
                chart.setOption(option);
                
                $(window).resize(function() {
                    chart.resize();
                });
            });
        </script>';

        return new Box('代理商业绩对比', $html);
    }

    /**
     * 顶级代理商盒子
     *
     * @return Box
     */
    protected function topAgentsBox()
    {
        $headers = ['排名', '代理商', '商铺数', '活跃率'];
        
        // 这里应该从数据库获取实际数据
        $rows = [
            ['1', '代理商A', '120', '85%'],
            ['2', '代理商B', '80', '78%'],
            ['3', '代理商C', '60', '92%'],
            ['4', '代理商D', '45', '67%'],
            ['5', '代理商E', '30', '73%']
        ];

        $table = new Table($headers, $rows);
        
        return new Box('顶级代理商', $table->render());
    }

    /**
     * 商铺增长趋势盒子
     *
     * @return Box
     */
    protected function merchantGrowthBox()
    {
        $html = '<div id="merchant-growth-chart" style="height: 300px;"></div>';
        $html .= '<script>
            $(function () {
                // 这里应该通过AJAX获取实际数据
                var dates = ["1月", "2月", "3月", "4月", "5月", "6月"];
                var newStores = [10, 15, 20, 25, 30, 35];
                var totalStores = [50, 65, 85, 110, 140, 175];
                
                var chart = echarts.init(document.getElementById("merchant-growth-chart"));
                var option = {
                    tooltip: {
                        trigger: "axis"
                    },
                    legend: {
                        data: ["新增商铺", "商铺总数"]
                    },
                    xAxis: {
                        type: "category",
                        data: dates
                    },
                    yAxis: {
                        type: "value"
                    },
                    series: [
                        {
                            name: "新增商铺",
                            type: "bar",
                            data: newStores
                        },
                        {
                            name: "商铺总数",
                            type: "line",
                            data: totalStores
                        }
                    ]
                };
                chart.setOption(option);
                
                $(window).resize(function() {
                    chart.resize();
                });
            });
        </script>';

        return new Box('商铺增长趋势', $html);
    }

    /**
     * 佣金趋势盒子
     *
     * @return Box
     */
    protected function commissionTrendBox()
    {
        $html = '<div id="commission-trend-chart" style="height: 300px;"></div>';
        $html .= '<script>
            $(function () {
                // 这里应该通过AJAX获取实际数据
                var dates = ["1月", "2月", "3月", "4月", "5月", "6月"];
                var commissions = [5000, 7500, 10000, 12500, 15000, 18000];
                
                var chart = echarts.init(document.getElementById("commission-trend-chart"));
                var option = {
                    tooltip: {
                        trigger: "axis"
                    },
                    xAxis: {
                        type: "category",
                        data: dates
                    },
                    yAxis: {
                        type: "value",
                        axisLabel: {
                            formatter: "￥{value}"
                        }
                    },
                    series: [
                        {
                            name: "佣金金额",
                            type: "line",
                            data: commissions,
                            areaStyle: {}
                        }
                    ]
                };
                chart.setOption(option);
                
                $(window).resize(function() {
                    chart.resize();
                });
            });
        </script>';

        return new Box('佣金趋势', $html);
    }
} 