/**
 * 商铺统计页面JavaScript
 */
$(document).ready(function () {
    // 图表实例
    let regionChart = null;
    let statusChart = null;
    let growthChart = null;

    // 页面加载时初始化
    init();

    /**
     * 初始化页面
     */
    function init() {
        // 加载筛选条件数据
        loadFilterOptions();

        // 加载统计数据
        loadStatisticsData();

        // 绑定事件
        bindEvents();
    }

    /**
     * 绑定事件
     */
    function bindEvents() {
        // 筛选表单提交
        $('#statistics-filter-form').on('submit', function (e) {
            e.preventDefault();
            loadStatisticsData();
        });

        // 重置筛选条件
        $('#reset-filter').on('click', function () {
            resetFilters();
        });

        // 导出数据
        $('#export-data').on('click', function () {
            exportStatisticsData();
        });

        // 省份变化时更新城市
        $('#province-select').on('change', function () {
            loadCities($(this).val());
        });
    }

    /**
     * 加载筛选条件数据
     */
    function loadFilterOptions() {
        // 加载代理商列表
        $.ajax({
            url: '/admin/merchant/api/agents',
            type: 'GET',
            dataType: 'json',
            success: function (response) {
                if (response.success) {
                    const agentSelect = $('#agent-select');
                    agentSelect.empty().append('<option value="">全部代理商</option>');

                    response.data.forEach(function (agent) {
                        agentSelect.append(`<option value="${agent.id}">${agent.name}</option>`);
                    });
                }
            }
        });

        // 加载省份列表
        $.ajax({
            url: '/admin/merchant/api/provinces',
            type: 'GET',
            dataType: 'json',
            success: function (response) {
                if (response.success) {
                    const provinceSelect = $('#province-select');
                    provinceSelect.empty().append('<option value="">全部省份</option>');

                    response.data.forEach(function (province) {
                        provinceSelect.append(`<option value="${province.id}">${province.name}</option>`);
                    });
                }
            }
        });
    }

    /**
     * 加载城市列表
     */
    function loadCities(provinceId) {
        const citySelect = $('#city-select');
        citySelect.empty().append('<option value="">全部城市</option>');

        if (!provinceId) {
            return;
        }

        $.ajax({
            url: `/admin/merchant/api/cities/${provinceId}`,
            type: 'GET',
            dataType: 'json',
            success: function (response) {
                if (response.success) {
                    response.data.forEach(function (city) {
                        citySelect.append(`<option value="${city.id}">${city.name}</option>`);
                    });
                }
            }
        });
    }

    /**
     * 加载统计数据
     */
    function loadStatisticsData() {
        const filterData = $('#statistics-filter-form').serialize();

        $.ajax({
            url: '/admin/merchant/api/statistics',
            type: 'GET',
            data: filterData,
            dataType: 'json',
            success: function (response) {
                if (response.success) {
                    updateOverview(response.data.overview);
                    updateCharts(response.data.charts);
                    updateTable(response.data.details);
                } else {
                    toastr.error(response.message || '加载统计数据失败');
                }
            },
            error: function (xhr, status, error) {
                console.error('加载统计数据失败:', error);
                toastr.error('网络错误，请重试');
            }
        });
    }

    /**
     * 更新概览数据
     */
    function updateOverview(overview) {
        $('#total-stores').text(overview.total || 0);
        $('#active-stores').text(overview.active || 0);
        $('#pending-stores').text(overview.pending || 0);
        $('#inactive-stores').text(overview.inactive || 0);
    }

    /**
     * 更新图表
     */
    function updateCharts(chartData) {
        // 更新地区分布图
        updateRegionChart(chartData.region);

        // 更新状态分布图
        updateStatusChart(chartData.status);

        // 更新增长趋势图
        updateGrowthChart(chartData.growth);
    }

    /**
     * 更新地区分布图
     */
    function updateRegionChart(data) {
        const ctx = document.getElementById('region-chart').getContext('2d');

        if (regionChart) {
            regionChart.destroy();
        }

        regionChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: data.labels || [],
                datasets: [{
                    data: data.values || [],
                    backgroundColor: [
                        '#FF6384',
                        '#36A2EB',
                        '#FFCE56',
                        '#4BC0C0',
                        '#9966FF',
                        '#FF9F40'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                legend: {
                    position: 'bottom'
                }
            }
        });
    }

    /**
     * 更新状态分布图
     */
    function updateStatusChart(data) {
        const ctx = document.getElementById('status-chart').getContext('2d');

        if (statusChart) {
            statusChart.destroy();
        }

        statusChart = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: ['营业中', '已关闭', '已暂停', '待审核'],
                datasets: [{
                    data: [
                        data.active || 0,
                        data.inactive || 0,
                        data.suspended || 0,
                        data.pending || 0
                    ],
                    backgroundColor: [
                        '#28a745',
                        '#dc3545',
                        '#ffc107',
                        '#17a2b8'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                legend: {
                    position: 'bottom'
                }
            }
        });
    }

    /**
     * 更新增长趋势图
     */
    function updateGrowthChart(data) {
        const ctx = document.getElementById('growth-chart').getContext('2d');

        if (growthChart) {
            growthChart.destroy();
        }

        growthChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.labels || [],
                datasets: [{
                    label: '新增商铺',
                    data: data.values || [],
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    /**
     * 更新详细数据表格
     */
    function updateTable(details) {
        const tbody = $('#statistics-tbody');
        tbody.empty();

        if (!details || details.length === 0) {
            tbody.append(`
                <tr>
                    <td colspan="9" class="text-center">暂无数据</td>
                </tr>
            `);
            return;
        }

        details.forEach(function (item) {
            tbody.append(`
                <tr>
                    <td>${item.agent_name || '-'}</td>
                    <td>${item.province_name || '-'}</td>
                    <td>${item.city_name || '-'}</td>
                    <td>${item.total || 0}</td>
                    <td>${item.active || 0}</td>
                    <td>${item.inactive || 0}</td>
                    <td>${item.suspended || 0}</td>
                    <td>${item.pending || 0}</td>
                    <td>${item.monthly_new || 0}</td>
                </tr>
            `);
        });
    }

    /**
     * 重置筛选条件
     */
    function resetFilters() {
        $('#statistics-filter-form')[0].reset();
        $('#city-select').empty().append('<option value="">全部城市</option>');
        loadStatisticsData();
    }

    /**
     * 导出统计数据
     */
    function exportStatisticsData() {
        const filterData = $('#statistics-filter-form').serialize();

        // 创建下载链接
        const downloadUrl = `/admin/merchant/api/export-statistics?${filterData}`;

        // 创建隐藏的下载链接并触发下载
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = `商铺统计数据_${new Date().toISOString().split('T')[0]}.xlsx`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        toastr.success('导出任务已开始，请稍候...');
    }
}); 