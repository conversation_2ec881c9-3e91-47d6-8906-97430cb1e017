@extends('admin::layouts.content')

@section('style')
<link rel="stylesheet" href="{{ asset('css/hierarchy-management.css') }}">
@endsection

@section('content')
<div class="row">
    <div class="col-md-12">
        <div class="box box-primary">
            <div class="box-header with-border">
                <h3 class="box-title">
                    <i class="fa fa-sitemap"></i> 代理商层级管理
                </h3>
                <div class="box-tools pull-right">
                    <button type="button" class="btn btn-box-tool" data-widget="collapse">
                        <i class="fa fa-minus"></i>
                    </button>
                </div>
            </div>
            <div class="box-body">
                <!-- 统计概览 -->
                <div class="row">
                    <div class="col-lg-3 col-xs-6">
                        <div class="small-box bg-aqua">
                            <div class="inner">
                                <h3 id="total-agents">{{ $totalAgents }}</h3>
                                <p>总代理商数量</p>
                            </div>
                            <div class="icon">
                                <i class="fa fa-users"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-xs-6">
                        <div class="small-box bg-green">
                            <div class="inner">
                                <h3 id="primary-agents">{{ $primaryAgents }}</h3>
                                <p>一级代理商</p>
                            </div>
                            <div class="icon">
                                <i class="fa fa-user-circle"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-xs-6">
                        <div class="small-box bg-yellow">
                            <div class="inner">
                                <h3 id="secondary-agents">{{ $secondaryAgents }}</h3>
                                <p>二级代理商</p>
                            </div>
                            <div class="icon">
                                <i class="fa fa-user"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-xs-6">
                        <div class="small-box bg-red">
                            <div class="inner">
                                <h3 id="total-stores">{{ $totalStores }}</h3>
                                <p>管理商铺总数</p>
                            </div>
                            <div class="icon">
                                <i class="fa fa-shopping-bag"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 层级结构图 -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="box box-info">
                            <div class="box-header with-border">
                                <h3 class="box-title">
                                    <i class="fa fa-tree"></i> 层级结构图
                                </h3>
                                <div class="box-tools pull-right">
                                    <button type="button" class="btn btn-sm btn-primary" onclick="refreshHierarchy()">
                                        <i class="fa fa-refresh"></i> 刷新
                                    </button>
                                </div>
                            </div>
                            <div class="box-body">
                                <div id="hierarchy-tree" style="height: 400px; width: 100%;"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 层级管理表格 -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="box box-success">
                            <div class="box-header with-border">
                                <h3 class="box-title">
                                    <i class="fa fa-table"></i> 层级管理表格
                                </h3>
                                <div class="box-tools pull-right">
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-sm btn-default dropdown-toggle" data-toggle="dropdown">
                                            <i class="fa fa-filter"></i> 筛选 <span class="caret"></span>
                                        </button>
                                        <ul class="dropdown-menu" role="menu">
                                            <li><a href="#" onclick="filterByLevel('all')">全部</a></li>
                                            <li><a href="#" onclick="filterByLevel(1)">一级代理商</a></li>
                                            <li><a href="#" onclick="filterByLevel(2)">二级代理商</a></li>
                                            <li class="divider"></li>
                                            <li><a href="#" onclick="filterByStatus('active')">仅活跃</a></li>
                                            <li><a href="#" onclick="filterByStatus('all')">全部状态</a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="box-body">
                                <div class="table-responsive">
                                    <table id="hierarchy-table" class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th>代理商信息</th>
                                                <th>等级</th>
                                                <th>上级代理商</th>
                                                <th>下级代理商</th>
                                                <th>直推商铺</th>
                                                <th>状态</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($hierarchyData as $agent)
                                            <tr data-level="{{ $agent->level }}" data-status="{{ $agent->status }}">
                                                <td>
                                                    <div class="agent-info">
                                                        <strong>{{ $agent->name }}</strong><br>
                                                        <small class="text-muted">{{ $agent->contact_person }}</small><br>
                                                        <small class="text-muted">{{ $agent->phone }}</small>
                                                    </div>
                                                </td>
                                                <td>
                                                    @if($agent->level == 1)
                                                    <span class="label label-primary">一级代理商</span>
                                                    @elseif($agent->level == 2)
                                                    <span class="label label-info">二级代理商</span>
                                                    @else
                                                    <span class="label label-default">未设置</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    @if($agent->parent_agent_id)
                                                    <small>{{ $agent->parentAgent->name ?? '已删除' }}</small>
                                                    @else
                                                    <span class="text-muted">-</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    <div class="progress-group">
                                                        <span class="progress-text">{{ $agent->current_sub_agents }}/{{ $agent->max_sub_agents }}</span>
                                                        <div class="progress progress-sm">
                                                            <div class="progress-bar progress-bar-green"
                                                                style="width: {{ $agent->max_sub_agents > 0 ? ($agent->current_sub_agents / $agent->max_sub_agents) * 100 : 0 }}%">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="progress-group">
                                                        <span class="progress-text">{{ $agent->current_direct_stores }}/{{ $agent->max_direct_stores }}</span>
                                                        <div class="progress progress-sm">
                                                            <div class="progress-bar progress-bar-blue"
                                                                style="width: {{ $agent->max_direct_stores > 0 ? ($agent->current_direct_stores / $agent->max_direct_stores) * 100 : 0 }}%">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    @if($agent->status == 'active')
                                                    <span class="label label-success">正常</span>
                                                    @elseif($agent->status == 'inactive')
                                                    <span class="label label-default">禁用</span>
                                                    @else
                                                    <span class="label label-danger">暂停</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    <div class="btn-group">
                                                        <a href="{{ admin_url('agents/' . $agent->id . '/hierarchy-detail') }}"
                                                            class="btn btn-xs btn-primary" title="层级详情">
                                                            <i class="fa fa-eye"></i>
                                                        </a>
                                                        @if($agent->level == 1 && $agent->canAddSubAgent())
                                                        <a href="{{ admin_url('agents/' . $agent->id . '/create-sub-agent') }}"
                                                            class="btn btn-xs btn-success" title="添加下级">
                                                            <i class="fa fa-plus"></i>
                                                        </a>
                                                        @endif
                                                        <a href="{{ admin_url('agents/' . $agent->id . '/edit-limits') }}"
                                                            class="btn btn-xs btn-warning" title="编辑限制">
                                                            <i class="fa fa-edit"></i>
                                                        </a>
                                                        <a href="{{ admin_url('agents/' . $agent->id . '/edit') }}"
                                                            class="btn btn-xs btn-default" title="编辑代理商">
                                                            <i class="fa fa-pencil"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 层级详情模态框 -->
<div class="modal fade" id="hierarchy-detail-modal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">
                    <i class="fa fa-sitemap"></i> 层级详情
                </h4>
            </div>
            <div class="modal-body" id="hierarchy-detail-content">
                <!-- 动态加载内容 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

@endsection

@section('script')
<script src="{{ asset('js/hierarchy-management.js') }}"></script>
<script>
    $(document).ready(function() {
        // 初始化层级管理功能
        HierarchyManagement.init({
            hierarchyData: @json($hierarchyData),
            treeContainer: '#hierarchy-tree',
            tableContainer: '#hierarchy-table'
        });
    });
</script>
@endsection