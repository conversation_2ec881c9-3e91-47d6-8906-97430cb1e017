<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * 代理商模型 - 基础信息管理
 * 
 * 📋 架构说明：
 * - 此模型只保留代理商基础信息，业务配置已迁移到AgentRegion模型
 * - 通过agentRegions关联获取业务配置（返佣、结算、统计等）
 * - 支持一个代理商管理多个地区的业务架构
 * 
 * @property int $id
 * @property string $name 代理商姓名
 * @property string $phone 手机号
 * @property string|null $email 邮箱
 * @property string|null $id_card 身份证号
 * @property string|null $address 地址
 * @property string $status 状态
 * @property string|null $contact_person 联系人
 * @property string|null $promotion_code 推广代码(可选)
 * @property string|null $qr_code_url 二维码URL
 * @property string|null $remark 备注
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 */
class Agent extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * ✅ 保留的基础信息字段
     * 🚨 注意：业务配置字段已迁移到agent_regions表，不要在此添加废弃字段
     */
    protected $fillable = [
        // 基础信息
        'name',               // 代理商姓名
        'contact_person',     // 联系人
        'phone',              // 联系电话
        'email',              // 邮箱地址
        'id_card',            // 身份证号
        'address',            // 通讯地址
        'status',             // 代理商状态
        'promotion_code',     // 推广代码(可选)
        'qr_code_url',        // 二维码URL
        'remark',             // 备注信息

        // 🆕 层级管理字段
        'parent_agent_id',      // 上级代理商ID
        'level',                // 代理商等级：1=一级，2=二级
        'max_sub_agents',       // 最大下级代理数量限制
        'max_direct_stores',    // 最大直推商铺数量限制
        'current_sub_agents',   // 当前下级代理数量
        'current_direct_stores', // 当前直推商铺数量

        // ⚠️ 兼容性字段(临时保留，后续将清理)
        'commission_rate',    // 返佣比例(已废弃，优先使用agent_regions表)
        'settlement_cycle',   // 结算周期(已废弃，优先使用agent_regions表)
        'signed_at',          // 签约日期(已废弃，优先使用agent_regions表)
        'next_settlement_at', // 下次结算日期(已废弃，优先使用agent_regions表)
        'settlement_days',    // 结算天数(已废弃，优先使用agent_regions表)
        'region_code',        // 区域代码(已废弃，使用agent_regions关联)
        'region_name',        // 区域名称(已废弃，使用agent_regions关联)
        'contract_file',      // 合同文件(已废弃，优先使用agent_regions表)
    ];

    protected $casts = [
        'commission_rate' => 'decimal:2',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
        'signed_at' => 'date',
        'next_settlement_at' => 'date',
        // 🆕 层级管理字段类型转换
        'level' => 'integer',
        'max_sub_agents' => 'integer',
        'max_direct_stores' => 'integer',
        'current_sub_agents' => 'integer',
        'current_direct_stores' => 'integer',
        'parent_agent_id' => 'integer',
    ];

    /**
     * 状态常量
     */
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';
    const STATUS_SUSPENDED = 'suspended';

    /**
     * 🆕 代理商等级常量
     */
    const LEVEL_PRIMARY = 1;     // 一级代理商
    const LEVEL_SECONDARY = 2;   // 二级代理商

    /**
     * 获取状态选项
     */
    public static function getStatusOptions()
    {
        return [
            self::STATUS_ACTIVE => '正常',
            self::STATUS_INACTIVE => '停用',
            self::STATUS_SUSPENDED => '暂停',
        ];
    }

    /**
     * 🆕 获取等级选项
     */
    public static function getLevelOptions()
    {
        return [
            self::LEVEL_PRIMARY => '一级代理商',
            self::LEVEL_SECONDARY => '二级代理商',
        ];
    }

    /**
     * 获取状态标签
     */
    public function getStatusLabelAttribute()
    {
        return self::getStatusOptions()[$this->status] ?? $this->status;
    }

    /**
     * 🆕 获取等级标签
     */
    public function getLevelLabelAttribute()
    {
        return self::getLevelOptions()[$this->level] ?? '未知等级';
    }

    // ==================== 关联关系 ====================

    /**
     * 关联代理商区域配置 - 核心业务配置
     */
    public function agentRegions()
    {
        return $this->hasMany(AgentRegion::class);
    }

    /**
     * 关联商铺 - 通过区域配置间接关联
     */
    public function stores()
    {
        return $this->hasMany(Store::class);
    }

    /**
     * 关联业务员
     */
    public function salespersons()
    {
        return $this->hasMany(Salesperson::class);
    }

    /**
     * 关联招募申请
     */
    public function recruitments()
    {
        return $this->hasMany(Recruitment::class, 'intended_agent_id');
    }

    /**
     * 🆕 关联上级代理商
     */
    public function parentAgent()
    {
        return $this->belongsTo(Agent::class, 'parent_agent_id');
    }

    /**
     * 🆕 关联下级代理商
     */
    public function subAgents()
    {
        return $this->hasMany(Agent::class, 'parent_agent_id');
    }

    /**
     * 🆕 关联活跃的下级代理商
     */
    public function activeSubAgents()
    {
        return $this->hasMany(Agent::class, 'parent_agent_id')->where('status', self::STATUS_ACTIVE);
    }

    // ==================== 查询作用域 ====================

    /**
     * 作用域：正常状态
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    /**
     * 作用域：按状态筛选
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 🆕 作用域：按等级筛选
     */
    public function scopeByLevel($query, $level)
    {
        return $query->where('level', $level);
    }

    /**
     * 🆕 作用域：一级代理商
     */
    public function scopePrimary($query)
    {
        return $query->where('level', self::LEVEL_PRIMARY);
    }

    /**
     * 🆕 作用域：二级代理商
     */
    public function scopeSecondary($query)
    {
        return $query->where('level', self::LEVEL_SECONDARY);
    }

    /**
     * 🆕 作用域：顶级代理商（无上级）
     */
    public function scopeTopLevel($query)
    {
        return $query->whereNull('parent_agent_id');
    }

    /**
     * 🆕 作用域：有上级的代理商
     */
    public function scopeHasParent($query)
    {
        return $query->whereNotNull('parent_agent_id');
    }

    // ==================== 业务方法（基于新架构） ====================

    /**
     * 获取代理商的所有活跃区域配置
     */
    public function getActiveRegions()
    {
        return $this->agentRegions()->where('region_status', 'active')->get();
    }

    /**
     * 获取代理商的主要区域(第一个配置的区域)
     */
    public function getPrimaryRegion()
    {
        return $this->agentRegions()->oldest()->first();
    }

    /**
     * 获取代理商在指定区域的配置
     */
    public function getRegionConfig($areaId)
    {
        return $this->agentRegions()->where('area_id', $areaId)->first();
    }

    /**
     * 检查代理商是否负责指定区域
     */
    public function managesArea($areaId)
    {
        return $this->agentRegions()->where('area_id', $areaId)->where('region_status', 'active')->exists();
    }

    /**
     * 获取代理商负责的所有区域名称
     */
    public function getRegionNamesAttribute()
    {
        return $this->agentRegions->pluck('full_region')->implode(', ') ?: '暂未配置区域';
    }

    /**
     * 获取代理商的平均返佣比例
     */
    public function getAverageCommissionRate()
    {
        $regions = $this->agentRegions()->whereNotNull('commission_rate')->get();
        if ($regions->isEmpty()) {
            return null;
        }
        return $regions->avg('commission_rate');
    }

    /**
     * 获取代理商的总商铺数量（通过区域配置统计）
     */
    public function getTotalStoresCount()
    {
        return $this->agentRegions()->sum('total_stores') ?: $this->stores()->count();
    }

    /**
     * 获取代理商的活跃商铺数量
     */
    public function getActiveStoresCount()
    {
        return $this->agentRegions()->sum('active_stores') ?: $this->stores()->where('status', 'active')->count();
    }

    /**
     * 获取代理商的总收入
     */
    public function getTotalRevenue()
    {
        return $this->agentRegions()->sum('total_revenue') ?: 0;
    }

    /**
     * 获取待结算金额
     */
    public function getPendingSettlementAmount()
    {
        return $this->agentRegions()->sum('pending_settlement_amount') ?: 0;
    }

    // ==================== 🆕 层级管理业务方法 ====================

    /**
     * 检查是否可以添加下级代理商
     */
    public function canAddSubAgent()
    {
        // 只有一级代理商可以添加二级代理商
        if ($this->level !== self::LEVEL_PRIMARY) {
            return false;
        }

        // 检查数量限制
        return $this->current_sub_agents < $this->max_sub_agents;
    }

    /**
     * 检查是否可以添加直推商铺
     */
    public function canAddDirectStore()
    {
        return $this->current_direct_stores < $this->max_direct_stores;
    }

    /**
     * 获取可添加的下级代理商数量
     */
    public function getAvailableSubAgentSlots()
    {
        if ($this->level !== self::LEVEL_PRIMARY) {
            return 0;
        }
        return max(0, $this->max_sub_agents - $this->current_sub_agents);
    }

    /**
     * 获取可添加的直推商铺数量
     */
    public function getAvailableDirectStoreSlots()
    {
        return max(0, $this->max_direct_stores - $this->current_direct_stores);
    }

    /**
     * 更新当前下级代理商数量
     */
    public function updateSubAgentCount()
    {
        $count = $this->subAgents()->where('status', self::STATUS_ACTIVE)->count();
        $this->update(['current_sub_agents' => $count]);
        return $count;
    }

    /**
     * 更新当前直推商铺数量
     */
    public function updateDirectStoreCount()
    {
        $count = $this->stores()->whereIn('audit_status', ['approved'])->count();
        $this->update(['current_direct_stores' => $count]);
        return $count;
    }

    /**
     * 检查是否为一级代理商
     */
    public function isPrimaryAgent()
    {
        return $this->level === self::LEVEL_PRIMARY;
    }

    /**
     * 检查是否为二级代理商
     */
    public function isSecondaryAgent()
    {
        return $this->level === self::LEVEL_SECONDARY;
    }

    /**
     * 检查是否有上级代理商
     */
    public function hasParentAgent()
    {
        return !is_null($this->parent_agent_id);
    }

    /**
     * 检查是否有下级代理商
     */
    public function hasSubAgents()
    {
        return $this->subAgents()->exists();
    }

    /**
     * 获取层级路径（从顶级到当前代理商）
     */
    public function getHierarchyPath()
    {
        $path = [];
        $current = $this;

        // 向上追溯到顶级代理商
        while ($current) {
            array_unshift($path, [
                'id' => $current->id,
                'name' => $current->name,
                'level' => $current->level,
                'level_label' => $current->level_label
            ]);
            $current = $current->parentAgent;
        }

        return $path;
    }

    /**
     * 获取下级代理商树形结构
     */
    public function getSubAgentTree()
    {
        return $this->subAgents()->with(['subAgents' => function ($query) {
            $query->with('subAgents');
        }])->get();
    }

    /**
     * 获取层级统计信息
     */
    public function getHierarchyStats()
    {
        return [
            'level' => $this->level,
            'level_label' => $this->level_label,
            'parent_agent' => $this->parentAgent ? [
                'id' => $this->parentAgent->id,
                'name' => $this->parentAgent->name,
                'level' => $this->parentAgent->level
            ] : null,
            'sub_agents' => [
                'current' => $this->current_sub_agents,
                'max' => $this->max_sub_agents,
                'available' => $this->getAvailableSubAgentSlots(),
                'percentage' => $this->max_sub_agents > 0 ? round(($this->current_sub_agents / $this->max_sub_agents) * 100, 2) : 0
            ],
            'direct_stores' => [
                'current' => $this->current_direct_stores,
                'max' => $this->max_direct_stores,
                'available' => $this->getAvailableDirectStoreSlots(),
                'percentage' => $this->max_direct_stores > 0 ? round(($this->current_direct_stores / $this->max_direct_stores) * 100, 2) : 0
            ]
        ];
    }

    /**
     * 创建下级代理商
     */
    public function createSubAgent(array $data)
    {
        // 检查是否可以添加下级代理商
        if (!$this->canAddSubAgent()) {
            throw new \InvalidArgumentException('无法添加下级代理商：已达到最大数量限制或权限不足');
        }

        // 设置下级代理商的基本信息
        $data['parent_agent_id'] = $this->id;
        $data['level'] = self::LEVEL_SECONDARY;
        $data['current_sub_agents'] = 0;
        $data['current_direct_stores'] = 0;

        // 创建下级代理商
        $subAgent = static::create($data);

        // 更新当前代理商的下级数量
        $this->updateSubAgentCount();

        return $subAgent;
    }

    /**
     * 移除下级代理商关联
     */
    public function removeSubAgent($subAgentId)
    {
        $subAgent = $this->subAgents()->findOrFail($subAgentId);

        // 检查下级代理商是否还有商铺
        if ($subAgent->getTotalStoresCount() > 0) {
            throw new \InvalidArgumentException('无法移除下级代理商：该代理商下还有商铺');
        }

        // 移除关联
        $subAgent->update(['parent_agent_id' => null]);

        // 更新当前代理商的下级数量
        $this->updateSubAgentCount();

        return true;
    }

    /**
     * 获取层级限制配置
     */
    public function getLimitsConfig()
    {
        return [
            'max_sub_agents' => $this->max_sub_agents,
            'max_direct_stores' => $this->max_direct_stores,
            'can_add_sub_agent' => $this->canAddSubAgent(),
            'can_add_direct_store' => $this->canAddDirectStore(),
            'available_sub_agent_slots' => $this->getAvailableSubAgentSlots(),
            'available_direct_store_slots' => $this->getAvailableDirectStoreSlots()
        ];
    }

    // ==================== 兼容性方法（临时保留） ====================

    /**
     * @deprecated 已废弃，请使用 getAverageCommissionRate() 或具体区域的返佣比例
     */
    public function getFormattedCommissionRateAttribute()
    {
        // 优先使用区域配置的平均返佣比例
        $avgRate = $this->getAverageCommissionRate();
        if ($avgRate !== null) {
            return round($avgRate, 2) . '%';
        }

        // 兼容性回退：使用agents表的字段
        return $this->commission_rate ? $this->commission_rate . '%' : '未设置';
    }

    /**
     * @deprecated 已废弃，请使用 getTotalStoresCount()
     */
    public function getStoreCountAttribute()
    {
        return $this->getTotalStoresCount();
    }

    /**
     * 获取业务员数量
     */
    public function getSalespersonCountAttribute()
    {
        return $this->salespersons()->count();
    }

    /**
     * 检查是否可以删除
     */
    public function canDelete()
    {
        return $this->getTotalStoresCount() === 0 &&
            $this->salespersons()->count() === 0 &&
            $this->agentRegions()->count() === 0 &&
            $this->subAgents()->count() === 0; // 🆕 不能删除有下级代理商的代理商
    }

    /**
     * 获取完整地址信息
     */
    public function getFullInfoAttribute()
    {
        $info = $this->name;
        if ($this->contact_person) {
            $info .= " ({$this->contact_person})";
        }
        if ($this->phone) {
            $info .= " - {$this->phone}";
        }
        return $info;
    }

    /**
     * 获取代理商概要信息
     */
    public function getSummaryAttribute()
    {
        $regionCount = $this->agentRegions()->count();
        $storeCount = $this->getTotalStoresCount();

        return [
            'name' => $this->name,
            'status' => $this->status_label,
            'regions_count' => $regionCount,
            'stores_count' => $storeCount,
            'average_commission_rate' => $this->getAverageCommissionRate(),
            'total_revenue' => $this->getTotalRevenue(),
            'pending_amount' => $this->getPendingSettlementAmount(),
        ];
    }
}
