<?php

namespace App\Admin\Controllers;

use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;
use Encore\Admin\Layout\Content;
use Encore\Admin\Facades\Admin;
use App\Models\Store;
use App\Models\Agent;
use App\Models\Area;
use App\Models\Salesperson;
use App\Services\PermissionService;
use Illuminate\Http\Request;
use Exception;

class MerchantController extends AdminController
{
    /**
     * 页面标题
     *
     * @var string
     */
    protected $title = '商铺管理';

    /**
     * 商铺列表页面
     *
     * @param Content $content
     * @return Content
     */
    public function index(Content $content)
    {
        // 检查权限
        if (!PermissionService::hasPermission('merchant.list')) {
            abort(403, '您没有权限访问商铺列表');
        }

        // 引入外部JavaScript文件，添加时间戳强制更新
        Admin::js('/js/merchant-actions.js?v=' . time());

        return $content
            ->title($this->title)
            ->description('商铺列表')
            ->body($this->grid());
    }

    /**
     * 商铺列表Grid
     *
     * @return Grid
     */
    protected function grid()
    {
        $grid = new Grid(new Store());

        // 🔥 第一步：获取当前用户权限
        $canView = PermissionService::hasPermission('merchant.show');
        $canEdit = PermissionService::hasPermission('store.edit');
        $canDelete = PermissionService::hasPermission('store.delete');
        $canCreate = PermissionService::hasPermission('store.create');
        $canConfig = PermissionService::hasPermission('store.config'); // 使用专门的配置权限
        $canBatchOperate = PermissionService::hasPermission('merchant.batch');

        // 🔥 关键修复：根据用户角色过滤数据
        $grid->model()->whereIn('agent_id', PermissionService::getAccessibleAgentIds());

        $grid->column('id', 'ID')->sortable();
        $grid->column('name', '商铺名称')->limit(30);
        $grid->column('contact_person', '联系人');
        $grid->column('phone', '联系电话');
        $grid->column('agent.name', '所属代理商')->limit(20);

        // 地区信息
        $grid->column('province_name', '省份');
        $grid->column('city_name', '城市');
        $grid->column('district_name', '区县');

        // 状态显示
        $grid->column('audit_status', '审核状态')->using([
            'pending' => '待审核',
            'approved' => '已通过',
            'rejected' => '已拒绝'
        ])->label([
            'pending' => 'warning',
            'approved' => 'success',
            'rejected' => 'danger'
        ]);

        $grid->column('store_status', '商铺状态')->using([
            'active' => '营业中',
            'inactive' => '已关闭',
            'suspended' => '已暂停'
        ])->label([
            'active' => 'success',
            'inactive' => 'default',
            'suspended' => 'warning'
        ]);

        $grid->column('nfc_chip_id', 'NFC芯片ID')->limit(15);
        $grid->column('product_amount', '商品金额')->display(function ($value) {
            return '¥' . number_format($value, 2);
        })->sortable();
        $grid->column('is_settled', '结算状态')->using([
            0 => '未结算',
            1 => '已结算'
        ])->label([
            0 => 'warning',
            1 => 'success'
        ]);
        $grid->column('created_at', '创建时间')->sortable();

        // 筛选器
        $grid->filter(function ($filter) {
            $filter->disableIdFilter();
            $filter->like('name', '商铺名称');
            $filter->like('contact_person', '联系人');
            $filter->like('phone', '联系电话');
            $filter->equal('agent_id', '所属代理商')->select(Agent::all()->pluck('name', 'id'));
            $filter->equal('audit_status', '审核状态')->select([
                'pending' => '待审核',
                'approved' => '已通过',
                'rejected' => '已拒绝'
            ]);
            $filter->equal('store_status', '商铺状态')->select([
                'active' => '营业中',
                'inactive' => '已关闭',
                'suspended' => '已暂停'
            ]);
            $filter->between('created_at', '创建时间')->datetime();
        });

        // 批量操作 - 根据权限显示
        if ($canBatchOperate) {
            $grid->batchActions(function ($batch) {
                $batch->add(new \App\Admin\Actions\BatchApproveStores());
                $batch->add(new \App\Admin\Actions\BatchDisableStores());
            });
        } else {
            $grid->disableBatchActions();
        }

        // 禁用默认的操作列
        $grid->disableActions();

        // 操作列配置 - 根据权限动态显示按钮
        $grid->column('操作')->display(function () use ($canView, $canEdit, $canDelete, $canConfig) {
            $store = $this;  // 当前行数据
            $buttons = [];

            // 查看按钮 - 有查看权限就显示
            if ($canView) {
                $buttons[] = '<a href="' . admin_url('merchants/' . $store->id) . '" class="btn btn-xs btn-primary"><i class="fa fa-eye"></i> 查看</a>';
            }

            // 编辑按钮 - 只有平台管理员才有编辑权限
            if ($canEdit) {
                $buttons[] = '<a href="' . admin_url('merchants/' . $store->id . '/edit') . '" class="btn btn-xs btn-success"><i class="fa fa-edit"></i> 编辑</a>';
            }

            // 配置按钮 - 只有平台管理员才有配置权限
            if ($canConfig) {
                $buttons[] = '<a href="' . admin_url('merchant/config/' . $store->id) . '" class="btn btn-xs btn-warning"><i class="fa fa-cog"></i> 配置</a>';
            }

            // 删除按钮 - 只有平台管理员才有删除权限
            if ($canDelete) {
                $buttons[] = '<a href="javascript:void(0)" onclick="deleteStore(' . $store->id . ')" class="btn btn-xs btn-danger"><i class="fa fa-trash"></i> 删除</a>';
            }

            // 解绑代理商按钮 - 只有平台管理员才能解绑
            if ($canEdit && $store->agent_id) {
                $buttons[] = '<a href="' . admin_url('merchant/unbind-agent/' . $store->id) . '" class="btn btn-xs btn-warning"><i class="fa fa-unlink"></i> 解绑</a>';
            }

            return implode(' ', $buttons);
        });

        // 工具栏配置 - 根据权限显示新增按钮
        $grid->tools(function ($tools) use ($canCreate) {
            if ($canCreate) {
                // 只有平台管理员才能新增商铺
                $tools->append('<a href="' . admin_url('merchants/create') . '" class="btn btn-sm btn-success">
                    <i class="fa fa-plus"></i>&nbsp;&nbsp;新增商铺
                </a>');
            }

            $tools->batch(function ($batch) {
                // 保留我们的自定义批量操作
                // $batch->disableDelete(); // 暂时不禁用，看看是否影响操作列显示
            });
        });

        // 禁用默认的新增按钮，使用我们自定义的
        $grid->disableCreateButton();

        return $grid;
    }

    /**
     * 商铺详情页面
     *
     * @param mixed $id
     * @param Content $content
     * @return Content
     */
    public function show($id, Content $content)
    {
        // 检查权限
        if (!PermissionService::hasPermission('merchant.show')) {
            abort(403, '您没有权限查看商铺详情');
        }

        return $content
            ->title('商铺详情')
            ->description('查看商铺详细信息')
            ->body($this->detail($id));
    }

    /**
     * 商铺详情Show配置
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        $show = new Show(Store::findOrFail($id));

        $show->field('id', 'ID');
        $show->field('name', '商铺名称');
        $show->field('logo_url', '商铺Logo')->image();
        $show->field('banner_url', '横幅图片')->image();
        $show->field('description', '商铺描述');
        $show->field('contact_person', '联系人');
        $show->field('phone', '联系电话');
        $show->field('email', '邮箱');
        $show->field('address', '详细地址');

        // 地理位置
        $show->field('latitude', '纬度');
        $show->field('longitude', '经度');

        // 地区信息
        $show->field('province_name', '省份');
        $show->field('city_name', '城市');
        $show->field('district_name', '区县');

        // 关联信息
        $show->field('agent.name', '所属代理商');
        $show->field('nfc_chip_id', 'NFC芯片ID');

        // 状态信息
        $show->field('audit_status', '审核状态')->using([
            'pending' => '待审核',
            'approved' => '已通过',
            'rejected' => '已拒绝'
        ]);

        $show->field('store_status', '商铺状态')->using([
            'active' => '营业中',
            'inactive' => '已关闭',
            'suspended' => '已暂停'
        ]);

        $show->field('nfc_chip_status', 'NFC芯片状态')->using([
            'active' => '正常',
            'inactive' => '未激活',
            'lost' => '丢失',
            'damaged' => '损坏'
        ]);

        // 业务数据
        $show->field('total_views', '总浏览量');
        $show->field('total_promotions', '总推广次数');
        $show->field('product_amount', '商品金额')->as(function ($value) {
            return '¥' . number_format($value, 2);
        });
        $show->field('is_settled', '结算状态')->using([
            0 => '未结算',
            1 => '已结算'
        ]);
        $show->field('last_active_at', '最后活跃时间');

        // 权限配置
        $show->field('promotion_permissions', '推广权限')->as(function ($value) {
            return $value ? json_encode(json_decode($value), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) : '无';
        });
        $show->field('feature_config', '功能配置')->as(function ($value) {
            return $value ? json_encode(json_decode($value), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) : '无';
        });

        // 审核信息
        $show->field('audit_remark', '审核备注');
        $show->field('audit_time', '审核时间');
        $show->field('auditor.name', '审核人');

        // 时间信息
        $show->field('created_at', '创建时间');
        $show->field('updated_at', '更新时间');

        return $show;
    }

    /**
     * 商铺表单页面
     *
     * @return Form
     */
    protected function form()
    {
        $form = new Form(new Store());

        // 基本信息
        $form->text('name', '商铺名称')->required()->help('请输入商铺名称');
        $form->image('logo_url', '商铺Logo')->help('建议尺寸：200x200px');
        $form->image('banner_url', '横幅图片')->help('建议尺寸：800x300px');
        $form->textarea('description', '商铺描述')->rows(3);

        // 联系信息
        $form->text('contact_person', '联系人')->required();
        $form->mobile('phone', '联系电话')->required();
        $form->email('email', '邮箱');
        $form->textarea('address', '详细地址')->required()->rows(2);

        // 地理位置
        $form->decimal('latitude', '纬度')->help('可选，用于地图定位');
        $form->decimal('longitude', '经度')->help('可选，用于地图定位');

        // 营业时间
        $form->textarea('business_hours', '营业时间')->rows(3)->help('JSON格式，例如：{"monday":"09:00-22:00","tuesday":"09:00-22:00"}');

        // 关联配置
        $form->select('agent_id', '所属代理商')->options(Agent::all()->pluck('name', 'id'))->required();

        // 地区选择（三级联动）
        $form->select('province_id', '省份')->options(Area::where('level', 1)->pluck('name', 'id'));
        $form->select('city_id', '城市');
        $form->select('district_id', '区县');

        // NFC芯片
        $form->text('nfc_chip_id', 'NFC芯片ID')->required()->help('每个商铺的唯一标识');
        $form->select('nfc_chip_status', 'NFC芯片状态')->options([
            'active' => '正常',
            'inactive' => '未激活',
            'lost' => '丢失',
            'damaged' => '损坏'
        ])->default('inactive');

        // 状态配置
        $form->select('audit_status', '审核状态')->options([
            'pending' => '待审核',
            'approved' => '已通过',
            'rejected' => '已拒绝'
        ])->default('pending');

        $form->select('store_status', '商铺状态')->options([
            'active' => '营业中',
            'inactive' => '已关闭',
            'suspended' => '已暂停'
        ])->default('inactive');

        // 推广权限
        $form->checkbox('promotion_permissions', '推广权限')->options([
            'can_publish_video' => '发视频权限',
            'can_use_review' => '点评打卡权限',
            'can_use_wechat_marketing' => '微信营销权限',
            'can_use_group_buying' => '团购权限',
            'can_use_lottery' => '抽奖权限',
            'can_access_materials' => '素材库访问权限'
        ])->help('选择该商铺允许的推广功能');

        // 功能配置
        $form->textarea('feature_config', '功能配置')->rows(3)->help('JSON格式的功能配置');

        // 商品金额和结算状态
        $form->currency('product_amount', '商品金额')->symbol('¥')->default(0.00);
        $form->switch('is_settled', '结算状态')->states([
            'on' => ['value' => 1, 'text' => '已结算', 'color' => 'success'],
            'off' => ['value' => 0, 'text' => '未结算', 'color' => 'default']
        ])->default(0);

        // 审核信息
        $form->textarea('audit_remark', '审核备注')->rows(2);
        $form->textarea('remark', '备注')->rows(2);

        // 表单事件
        $form->saving(function (Form $form) {
            // 保存时的数据处理
            if ($form->promotion_permissions) {
                $form->promotion_permissions = json_encode($form->promotion_permissions);
            }
        });

        return $form;
    }

    /**
     * 创建商铺页面
     *
     * @param Content $content
     * @return Content
     */
    public function create(Content $content)
    {
        return $content
            ->title('新增商铺')
            ->description('创建新的商铺')
            ->body($this->form());
    }

    /**
     * 编辑商铺页面
     *
     * @param mixed $id
     * @param Content $content
     * @return Content
     */
    public function edit($id, Content $content)
    {
        return $content
            ->title('编辑商铺')
            ->description('修改商铺信息')
            ->body($this->form()->edit($id));
    }

    /**
     * 存储新商铺
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store()
    {
        return $this->form()->store();
    }

    /**
     * 更新商铺信息
     *
     * @param mixed $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update($id)
    {
        return $this->form()->update($id);
    }

    /**
     * 删除商铺
     *
     * @param mixed $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        return $this->form()->destroy($id);
    }

    /**
     * 商铺配置页面
     *
     * @param int $id
     * @param Content $content
     * @return Content
     */
    public function config($id, Content $content)
    {
        return $content
            ->title('商铺配置')
            ->description('配置商铺的推广权限和功能设置')
            ->body(view('admin.merchant.config', compact('id')));
    }

    /**
     * 数据统计页面
     *
     * @param Content $content
     * @return Content
     */
    public function statistics(Content $content)
    {
        return $content
            ->title('商铺数据统计')
            ->description('查看商铺推广数据和统计信息')
            ->body(view('admin.merchant.statistics'));
    }

    /**
     * 批量操作页面
     *
     * @param Content $content
     * @return Content
     */
    public function batch(Content $content)
    {
        return $content
            ->title('批量操作')
            ->description('批量导入导出和编辑商铺信息')
            ->body(view('admin.merchant.batch'));
    }

    /**
     * API接口：获取商铺列表
     */
    public function apiList(Request $request)
    {
        try {
            $query = Store::with(['agent']);

            // 搜索条件
            if ($request->has('name')) {
                $query->where('name', 'like', '%' . $request->name . '%');
            }

            if ($request->has('agent_id')) {
                $query->where('agent_id', $request->agent_id);
            }

            if ($request->has('audit_status')) {
                $query->where('audit_status', $request->audit_status);
            }

            if ($request->has('store_status')) {
                $query->where('store_status', $request->store_status);
            }

            // 分页
            $perPage = $request->get('per_page', 15);
            $stores = $query->paginate($perPage);

            return response()->json([
                'status' => true,
                'message' => '获取成功',
                'data' => $stores
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => '获取失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * API接口：获取商铺详情
     */
    public function apiDetail($id)
    {
        try {
            $store = Store::with(['agent'])->findOrFail($id);

            return response()->json([
                'status' => true,
                'message' => '获取成功',
                'data' => $store
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => '获取失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * API接口：获取统计数据
     */
    public function apiStatistics(Request $request)
    {
        try {
            $data = [
                'total_stores' => Store::count(),
                'active_stores' => Store::where('store_status', 'active')->count(),
                'pending_audit' => Store::where('audit_status', 'pending')->count(),
                'approved_stores' => Store::where('audit_status', 'approved')->count(),
                'by_agent' => Store::selectRaw('agent_id, count(*) as count')
                    ->with('agent:id,name')
                    ->groupBy('agent_id')
                    ->get(),
                'by_status' => Store::selectRaw('store_status, count(*) as count')
                    ->groupBy('store_status')
                    ->get(),
                'recent_stores' => Store::with('agent:id,name')
                    ->orderBy('created_at', 'desc')
                    ->limit(10)
                    ->get()
            ];

            return response()->json([
                'status' => true,
                'message' => '获取成功',
                'data' => $data
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => '获取失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 处理批量审核通过
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchApproveStores(Request $request)
    {
        try {
            $ids = $request->get('selected');
            if (empty($ids)) {
                return response()->json(['status' => false, 'message' => '请选择要操作的数据']);
            }

            Store::whereIn('id', $ids)->update([
                'audit_status' => 'approved',
                'audit_time' => now()
            ]);

            return response()->json(['status' => true, 'message' => '批量审核通过成功！']);
        } catch (Exception $e) {
            return response()->json(['status' => false, 'message' => '操作失败：' . $e->getMessage()]);
        }
    }

    /**
     * 处理批量禁用
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchDisableStores(Request $request)
    {
        try {
            $ids = $request->get('selected');
            if (empty($ids)) {
                return response()->json(['status' => false, 'message' => '请选择要操作的数据']);
            }

            Store::whereIn('id', $ids)->update([
                'store_status' => 'inactive'
            ]);

            return response()->json(['status' => true, 'message' => '批量禁用成功！']);
        } catch (Exception $e) {
            return response()->json(['status' => false, 'message' => '操作失败：' . $e->getMessage()]);
        }
    }
}
