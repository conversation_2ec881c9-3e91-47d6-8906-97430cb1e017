<?php

namespace App\Admin\Actions;

use Encore\Admin\Actions\RowAction;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;

/**
 * 解绑代理商操作
 * 
 * <AUTHOR>
 */
class UnbindAgent extends RowAction
{
    public $name = '解绑代理商';

    /**
     * 处理解绑操作
     *
     * @param Model $model
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function handle(Model $model, Request $request)
    {
        try {
            // 解绑代理商
            $model->agent_id = null;
            $model->save();

            return $this->response()->success('解绑成功')->refresh();
        } catch (\Exception $e) {
            return $this->response()->error('解绑失败: ' . $e->getMessage());
        }
    }

    /**
     * 确认对话框
     *
     * @return string
     */
    public function confirm()
    {
        return '确定要解绑该商铺的代理商吗？';
    }

    /**
     * 显示条件
     *
     * @param Model $model
     * @return bool
     */
    public function display($model)
    {
        // 只有绑定了代理商的商铺才显示解绑按钮
        return !empty($model->agent_id);
    }
}