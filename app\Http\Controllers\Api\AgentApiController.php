<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Agent;
use App\Models\AgentRegion;
use App\Models\Area;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Exception;

/**
 * 代理商API控制器（基于新架构）
 * 
 * 基于代理商-区域分离架构的API接口
 * 支持一个代理商管理多个地区的业务场景
 * 
 * <AUTHOR> Admin
 * @version 1.0.0
 */
class AgentApiController extends Controller
{
    /**
     * 获取代理商列表（带区域配置）
     * 
     * @api {get} /api/agents 获取代理商列表
     * @apiName GetAgentsList
     * @apiGroup 代理商管理API
     * @apiVersion 1.0.0
     * @apiDescription 获取代理商基础信息及其区域配置信息
     * 
     * @apiParam {String} [status] 代理商状态筛选（active|inactive|suspended）
     * @apiParam {Integer} [with_regions] 是否包含区域配置（1-是，0-否），默认1
     * @apiParam {Integer} [area_id] 按区域ID筛选
     * @apiParam {String} [keyword] 搜索关键词（姓名、手机号）
     * @apiParam {Integer} [page] 页码，默认1
     * @apiParam {Integer} [limit] 每页数量，默认20，最大100
     * 
     * @apiSuccess {Integer} code 状态码（200表示成功）
     * @apiSuccess {String} message 响应消息
     * @apiSuccess {Object} data 代理商数据
     * @apiSuccess {Array} data.agents 代理商列表
     * @apiSuccess {Object} data.pagination 分页信息
     * @apiSuccess {Object} data.summary 汇总统计
     * @apiSuccess {String} timestamp 响应时间戳
     * 
     * @apiSuccessExample {json} 成功响应:
     * {
     *   "code": 200,
     *   "message": "获取成功",
     *   "data": {
     *     "agents": [
     *       {
     *         "id": 1,
     *         "name": "张三代理",
     *         "contact_person": "张三",
     *         "phone": "13800138001",
     *         "email": "<EMAIL>",
     *         "status": "active",
     *         "status_label": "正常",
     *         "regions": [
     *           {
     *             "region_id": 1,
     *             "area_id": 440100,
     *             "full_region": "广东省,广州市,天河区",
     *             "is_exclusive": true,
     *             "commission_rate": 15.50,
     *             "settlement_cycle": "monthly",
     *             "contract_status": "signed",
     *             "region_status": "active"
     *           }
     *         ],
     *         "summary": {
     *           "regions_count": 1,
     *           "total_stores": 25,
     *           "active_stores": 20,
     *           "total_revenue": 125600.50,
     *           "pending_amount": 1500.00
     *         }
     *       }
     *     ],
     *     "pagination": {
     *       "current_page": 1,
     *       "per_page": 20,
     *       "total": 50,
     *       "last_page": 3
     *     },
     *     "summary": {
     *       "total_agents": 50,
     *       "active_agents": 45,
     *       "total_regions": 78,
     *       "total_stores": 1250
     *     }
     *   },
     *   "timestamp": "2025-06-24 14:30:00"
     * }
     * 
     * @apiError {Integer} code 错误码
     * @apiError {String} message 错误信息
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $query = Agent::query();
            
            // 状态筛选
            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }
            
            // 区域筛选
            if ($request->filled('area_id')) {
                $query->whereHas('agentRegions', function ($q) use ($request) {
                    $q->where('area_id', $request->area_id);
                });
            }
            
            // 关键词搜索
            if ($request->filled('keyword')) {
                $keyword = $request->keyword;
                $query->where(function ($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%")
                      ->orWhere('phone', 'like', "%{$keyword}%")
                      ->orWhere('contact_person', 'like', "%{$keyword}%");
                });
            }
            
            // 分页参数
            $limit = min(100, max(1, (int)($request->limit ?? 20)));
            $page = max(1, (int)($request->page ?? 1));
            
            $total = $query->count();
            
            // 构建查询结果
            $agentsQuery = $query->skip(($page - 1) * $limit)->take($limit);
            
            // 是否包含区域配置
            $withRegions = $request->filled('with_regions') ? (bool)$request->with_regions : true;
            if ($withRegions) {
                $agentsQuery->with(['agentRegions.area']);
            }
            
            $agents = $agentsQuery->get()->map(function ($agent) use ($withRegions) {
                return $this->formatAgentData($agent, $withRegions);
            });
            
            // 分页信息
            $pagination = [
                'current_page' => $page,
                'per_page' => $limit,
                'total' => $total,
                'last_page' => ceil($total / $limit),
                'has_more' => $page * $limit < $total,
            ];
            
            // 汇总统计
            $summary = $this->getAgentsSummary();
            
            return response()->json([
                'code' => 200,
                'message' => '获取成功',
                'data' => [
                    'agents' => $agents,
                    'pagination' => $pagination,
                    'summary' => $summary,
                ],
                'timestamp' => now()->toDateTimeString()
            ]);
            
        } catch (Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '服务器错误: ' . $e->getMessage(),
                'data' => null,
                'timestamp' => now()->toDateTimeString()
            ], 500);
        }
    }

    /**
     * 获取单个代理商详情
     * 
     * @api {get} /api/agents/{id} 获取代理商详情
     * @apiName GetAgentDetail
     * @apiGroup 代理商管理API
     * @apiVersion 1.0.0
     * @apiDescription 获取指定代理商的详细信息，包括所有区域配置
     * 
     * @apiParam {Integer} id 代理商ID
     * 
     * @apiSuccess {Integer} code 状态码
     * @apiSuccess {String} message 响应消息
     * @apiSuccess {Object} data 代理商详细信息
     * @apiSuccess {String} timestamp 响应时间戳
     * 
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $agent = Agent::with(['agentRegions.area'])->find($id);
            
            if (!$agent) {
                return response()->json([
                    'code' => 404,
                    'message' => '代理商不存在',
                    'data' => null,
                    'timestamp' => now()->toDateTimeString()
                ], 404);
            }
            
            $data = $this->formatAgentData($agent, true, true); // 详细模式
            
            return response()->json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $data,
                'timestamp' => now()->toDateTimeString()
            ]);
            
        } catch (Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '服务器错误: ' . $e->getMessage(),
                'data' => null,
                'timestamp' => now()->toDateTimeString()
            ], 500);
        }
    }

    /**
     * 获取代理商的区域配置
     * 
     * @api {get} /api/agents/{id}/regions 获取代理商区域配置
     * @apiName GetAgentRegions
     * @apiGroup 代理商管理API
     * @apiVersion 1.0.0
     * @apiDescription 获取指定代理商的所有区域配置
     * 
     * @apiParam {Integer} id 代理商ID
     * @apiParam {String} [status] 区域状态筛选（active|inactive|suspended）
     * 
     * @param int $id
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function regions($id, Request $request)
    {
        try {
            $agent = Agent::find($id);
            if (!$agent) {
                return response()->json([
                    'code' => 404,
                    'message' => '代理商不存在',
                    'data' => null,
                    'timestamp' => now()->toDateTimeString()
                ], 404);
            }
            
            $query = $agent->agentRegions()->with('area');
            
            // 状态筛选
            if ($request->filled('status')) {
                $query->where('region_status', $request->status);
            }
            
            $regions = $query->get()->map(function ($region) {
                return $this->formatRegionData($region);
            });
            
            return response()->json([
                'code' => 200,
                'message' => '获取成功',
                'data' => [
                    'agent_id' => $id,
                    'agent_name' => $agent->name,
                    'regions' => $regions,
                    'regions_count' => $regions->count(),
                ],
                'timestamp' => now()->toDateTimeString()
            ]);
            
        } catch (Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '服务器错误: ' . $e->getMessage(),
                'data' => null,
                'timestamp' => now()->toDateTimeString()
            ], 500);
        }
    }

    /**
     * 获取代理商统计数据
     * 
     * @api {get} /api/agents/statistics 获取代理商统计数据
     * @apiName GetAgentsStatistics
     * @apiGroup 代理商管理API
     * @apiVersion 1.0.0
     * @apiDescription 获取代理商相关的统计数据
     * 
     * @return \Illuminate\Http\JsonResponse
     */
    public function statistics()
    {
        try {
            $stats = [
                // 基础统计
                'agents' => [
                    'total' => Agent::count(),
                    'active' => Agent::where('status', 'active')->count(),
                    'inactive' => Agent::where('status', 'inactive')->count(),
                    'suspended' => Agent::where('status', 'suspended')->count(),
                ],
                
                // 区域配置统计
                'regions' => [
                    'total' => AgentRegion::count(),
                    'active' => AgentRegion::where('region_status', 'active')->count(),
                    'exclusive' => AgentRegion::where('is_exclusive', true)->count(),
                    'non_exclusive' => AgentRegion::where('is_exclusive', false)->count(),
                ],
                
                // 业务统计
                'business' => [
                    'total_stores' => AgentRegion::sum('total_stores') ?: 0,
                    'active_stores' => AgentRegion::sum('active_stores') ?: 0,
                    'total_revenue' => AgentRegion::sum('total_revenue') ?: 0,
                    'pending_settlement' => AgentRegion::sum('pending_settlement_amount') ?: 0,
                ],
                
                // 合约统计
                'contracts' => [
                    'signed' => AgentRegion::where('contract_status', 'signed')->count(),
                    'draft' => AgentRegion::where('contract_status', 'draft')->count(),
                    'expired' => AgentRegion::where('contract_status', 'expired')->count(),
                    'terminated' => AgentRegion::where('contract_status', 'terminated')->count(),
                ],
            ];
            
            return response()->json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $stats,
                'timestamp' => now()->toDateTimeString()
            ]);
            
        } catch (Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '服务器错误: ' . $e->getMessage(),
                'data' => null,
                'timestamp' => now()->toDateTimeString()
            ], 500);
        }
    }

    /**
     * 格式化代理商数据
     * 
     * @param Agent $agent
     * @param bool $withRegions
     * @param bool $detailed
     * @return array
     */
    private function formatAgentData(Agent $agent, bool $withRegions = true, bool $detailed = false): array
    {
        $data = [
            'id' => $agent->id,
            'name' => $agent->name,
            'contact_person' => $agent->contact_person,
            'phone' => $agent->phone,
            'email' => $agent->email,
            'status' => $agent->status,
            'status_label' => $agent->status_label,
            'created_at' => $agent->created_at->toDateTimeString(),
        ];
        
        if ($detailed) {
            $data = array_merge($data, [
                'id_card' => $agent->id_card,
                'address' => $agent->address,
                'promotion_code' => $agent->promotion_code,
                'remark' => $agent->remark,
                'updated_at' => $agent->updated_at->toDateTimeString(),
            ]);
        }
        
        if ($withRegions) {
            $data['regions'] = $agent->agentRegions->map(function ($region) {
                return $this->formatRegionData($region);
            })->toArray();
            
            $data['summary'] = $agent->summary;
        }
        
        return $data;
    }

    /**
     * 格式化区域配置数据
     * 
     * @param AgentRegion $region
     * @return array
     */
    private function formatRegionData(AgentRegion $region): array
    {
        return [
            'region_id' => $region->id,
            'area_id' => $region->area_id,
            'full_region' => $region->full_region,
            'is_exclusive' => $region->is_exclusive,
            'is_exclusive_text' => $region->is_exclusive ? '独占' : '共享',
            
            // 业务配置
            'commission_rate' => $region->commission_rate,
            'commission_rate_display' => $region->commission_rate_display,
            'settlement_cycle' => $region->settlement_cycle,
            'settlement_cycle_text' => $region->settlement_cycle_text,
            'settlement_day' => $region->settlement_day,
            
            // 合约信息
            'contract_status' => $region->contract_status,
            'contract_status_text' => $region->contract_status_text,
            'signed_at' => $region->signed_at,
            'contract_start_date' => $region->contract_start_date,
            'contract_end_date' => $region->contract_end_date,
            
            // 状态
            'region_status' => $region->region_status,
            'region_status_text' => $region->region_status_text,
            
            // 统计数据
            'total_stores' => $region->total_stores ?? 0,
            'active_stores' => $region->active_stores ?? 0,
            'total_revenue' => $region->total_revenue ?? 0,
            'pending_settlement_amount' => $region->pending_settlement_amount ?? 0,
            
            'created_at' => $region->created_at->toDateTimeString(),
            'updated_at' => $region->updated_at->toDateTimeString(),
        ];
    }

    /**
     * 获取代理商汇总统计
     * 
     * @return array
     */
    private function getAgentsSummary(): array
    {
        return [
            'total_agents' => Agent::count(),
            'active_agents' => Agent::where('status', 'active')->count(),
            'total_regions' => AgentRegion::count(),
            'total_stores' => AgentRegion::sum('total_stores') ?: 0,
            'total_revenue' => AgentRegion::sum('total_revenue') ?: 0,
            'pending_amount' => AgentRegion::sum('pending_settlement_amount') ?: 0,
        ];
    }
} 