<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Hash;

/**
 * 销售人员模型
 * 
 * @property int $id
 * @property string $name 姓名
 * @property string $phone 手机号
 * @property string|null $email 邮箱
 * @property string|null $id_card 身份证号
 * @property string|null $address 地址
 * @property int|null $agent_id 所属代理商ID
 * @property int|null $team_id 所属团队ID
 * @property string $role 角色
 * @property float $commission_rate 佣金比例
 * @property string $settlement_cycle 结算周期
 * @property string $status 状态
 * @property string|null $password 登录密码
 * @property array|null $permissions 权限配置
 * @property \Illuminate\Support\Carbon|null $last_login_at 最后登录时间
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Agent|null $agent
 * @property-read \App\Models\Team|null $team
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Recruitment[] $recruitments
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Team[] $managedTeams
 */
class Salesperson extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'phone',
        'email',
        'id_card',
        'address',
        'agent_id',
        'team_id',
        'role',
        'commission_rate',
        'settlement_cycle',
        'status',
        'password',
        'permissions',
        'last_login_at',
    ];

    protected $hidden = [
        'password',
    ];

    protected $casts = [
        'commission_rate' => 'decimal:4',
        'permissions' => 'array',
        'last_login_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 角色常量
     */
    const ROLE_SALESPERSON = 'salesperson';
    const ROLE_TEAM_LEADER = 'team_leader';
    const ROLE_SUPERVISOR = 'supervisor';
    const ROLE_MANAGER = 'manager';

    /**
     * 状态常量
     */
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';
    const STATUS_SUSPENDED = 'suspended';
    const STATUS_RESIGNED = 'resigned';

    /**
     * 结算周期常量
     */
    const SETTLEMENT_WEEKLY = 'weekly';
    const SETTLEMENT_MONTHLY = 'monthly';
    const SETTLEMENT_QUARTERLY = 'quarterly';

    /**
     * 获取角色选项
     */
    public static function getRoleOptions()
    {
        return [
            self::ROLE_SALESPERSON => '销售员',
            self::ROLE_TEAM_LEADER => '团队长',
            self::ROLE_SUPERVISOR => '主管',
            self::ROLE_MANAGER => '经理',
        ];
    }

    /**
     * 获取状态选项
     */
    public static function getStatusOptions()
    {
        return [
            self::STATUS_ACTIVE => '在职',
            self::STATUS_INACTIVE => '待激活',
            self::STATUS_SUSPENDED => '停职',
            self::STATUS_RESIGNED => '离职',
        ];
    }

    /**
     * 获取结算周期选项
     */
    public static function getSettlementCycleOptions()
    {
        return [
            self::SETTLEMENT_WEEKLY => '周结',
            self::SETTLEMENT_MONTHLY => '月结',
            self::SETTLEMENT_QUARTERLY => '季结',
        ];
    }

    /**
     * 获取默认权限配置
     */
    public static function getDefaultPermissions($role)
    {
        $permissions = [
            self::ROLE_SALESPERSON => [
                'view_own_data' => true,
                'manage_merchants' => false,
                'view_team_data' => false,
                'manage_team' => false,
                'view_reports' => false,
                'export_data' => false,
            ],
            self::ROLE_TEAM_LEADER => [
                'view_own_data' => true,
                'manage_merchants' => true,
                'view_team_data' => true,
                'manage_team' => true,
                'view_reports' => true,
                'export_data' => false,
            ],
            self::ROLE_SUPERVISOR => [
                'view_own_data' => true,
                'manage_merchants' => true,
                'view_team_data' => true,
                'manage_team' => true,
                'view_reports' => true,
                'export_data' => true,
                'manage_salesperson' => true,
            ],
            self::ROLE_MANAGER => [
                'view_own_data' => true,
                'manage_merchants' => true,
                'view_team_data' => true,
                'manage_team' => true,
                'view_reports' => true,
                'export_data' => true,
                'manage_salesperson' => true,
                'manage_agents' => true,
                'system_settings' => false,
            ],
        ];

        return $permissions[$role] ?? $permissions[self::ROLE_SALESPERSON];
    }

    /**
     * 关联代理商
     */
    public function agent()
    {
        return $this->belongsTo(Agent::class);
    }

    /**
     * 关联团队
     */
    public function team()
    {
        return $this->belongsTo(Team::class);
    }

    /**
     * 关联招聘记录
     */
    public function recruitments()
    {
        return $this->hasMany(Recruitment::class, 'intended_agent_id');
    }

    /**
     * 管理的团队
     */
    public function managedTeams()
    {
        return $this->hasMany(Team::class, 'leader_id');
    }

    /**
     * 获取角色标签
     */
    public function getRoleLabelAttribute()
    {
        return self::getRoleOptions()[$this->role] ?? $this->role;
    }

    /**
     * 获取状态标签
     */
    public function getStatusLabelAttribute()
    {
        return self::getStatusOptions()[$this->status] ?? $this->status;
    }

    /**
     * 获取状态颜色
     */
    public function getStatusColorAttribute()
    {
        $colors = [
            self::STATUS_ACTIVE => 'success',
            self::STATUS_INACTIVE => 'warning',
            self::STATUS_SUSPENDED => 'danger',
            self::STATUS_RESIGNED => 'secondary',
        ];
        return $colors[$this->status] ?? 'default';
    }

    /**
     * 获取结算周期标签
     */
    public function getSettlementCycleLabelAttribute()
    {
        return self::getSettlementCycleOptions()[$this->settlement_cycle] ?? $this->settlement_cycle;
    }

    /**
     * 获取代理商名称
     */
    public function getAgentNameAttribute()
    {
        return $this->agent ? $this->agent->name : '无';
    }

    /**
     * 获取团队名称
     */
    public function getTeamNameAttribute()
    {
        return $this->team ? $this->team->name : '无';
    }

    /**
     * 获取格式化佣金比例
     */
    public function getFormattedCommissionRateAttribute()
    {
        return ($this->commission_rate * 100) . '%';
    }

    /**
     * 获取在职天数
     */
    public function getWorkingDaysAttribute()
    {
        if ($this->status === self::STATUS_RESIGNED) {
            return 0;
        }

        return $this->created_at->diffInDays(now());
    }

    /**
     * 获取最后登录时间格式化
     */
    public function getLastLoginFormatAttribute()
    {
        if (!$this->last_login_at) {
            return '从未登录';
        }

        return $this->last_login_at->diffForHumans();
    }

    /**
     * 检查权限
     */
    public function hasPermission($permission)
    {
        if (!$this->permissions) {
            return false;
        }

        return $this->permissions[$permission] ?? false;
    }

    /**
     * 设置权限
     */
    public function setPermission($permission, $value = true)
    {
        $permissions = $this->permissions ?? [];
        $permissions[$permission] = $value;
        $this->permissions = $permissions;
        return $this;
    }

    /**
     * 批量设置权限
     */
    public function setPermissions(array $permissions)
    {
        $this->permissions = array_merge($this->permissions ?? [], $permissions);
        return $this;
    }

    /**
     * 重置为默认权限
     */
    public function resetPermissions()
    {
        $this->permissions = self::getDefaultPermissions($this->role);
        return $this;
    }

    /**
     * 作用域：按代理商筛选
     */
    public function scopeByAgent($query, $agentId)
    {
        return $query->where('agent_id', $agentId);
    }

    /**
     * 作用域：按团队筛选
     */
    public function scopeByTeam($query, $teamId)
    {
        return $query->where('team_id', $teamId);
    }

    /**
     * 作用域：按角色筛选
     */
    public function scopeByRole($query, $role)
    {
        return $query->where('role', $role);
    }

    /**
     * 作用域：按状态筛选
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 作用域：在职人员
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    /**
     * 作用域：团队长
     */
    public function scopeTeamLeaders($query)
    {
        return $query->where('role', self::ROLE_TEAM_LEADER);
    }

    /**
     * 作用域：搜索
     */
    public function scopeSearch($query, $keyword)
    {
        return $query->where(function ($q) use ($keyword) {
            $q->where('name', 'like', "%{$keyword}%")
                ->orWhere('phone', 'like', "%{$keyword}%")
                ->orWhere('email', 'like', "%{$keyword}%");
        });
    }

    /**
     * 设置密码
     */
    public function setPasswordAttribute($value)
    {
        if ($value) {
            $this->attributes['password'] = Hash::make($value);
        }
    }

    /**
     * 验证密码
     */
    public function checkPassword($password)
    {
        return Hash::check($password, $this->password);
    }

    /**
     * 更新最后登录时间
     */
    public function updateLastLogin()
    {
        $this->update(['last_login_at' => now()]);
    }

    /**
     * 激活账号
     */
    public function activate()
    {
        $this->update(['status' => self::STATUS_ACTIVE]);
        return $this;
    }

    /**
     * 停职
     */
    public function suspend()
    {
        $this->update(['status' => self::STATUS_SUSPENDED]);
        return $this;
    }

    /**
     * 离职
     */
    public function resign()
    {
        $this->update(['status' => self::STATUS_RESIGNED]);
        return $this;
    }

    /**
     * 检查是否可以删除
     */
    public function canDelete()
    {
        // 如果是团队长且有团队成员，不能删除
        if ($this->role === self::ROLE_TEAM_LEADER && $this->managedTeams()->exists()) {
            return false;
        }

        // 如果有关联的招聘记录，不能删除
        if ($this->recruitments()->exists()) {
            return false;
        }

        return true;
    }

    /**
     * 获取下级销售人员
     */
    public function getSubordinates()
    {
        if ($this->role === self::ROLE_TEAM_LEADER && $this->team) {
            return $this->team->members()->where('id', '!=', $this->id)->get();
        }

        return collect();
    }

    /**
     * 获取业绩统计
     */
    public function getPerformanceStats($startDate = null, $endDate = null)
    {
        // 这里应该根据实际业务逻辑计算业绩
        // 暂时返回模拟数据
        return [
            'merchant_count' => 0,
            'total_commission' => 0,
            'this_month_commission' => 0,
            'ranking' => 0,
        ];
    }

    /**
     * 生成登录令牌
     */
    public function generateLoginToken()
    {
        return hash('sha256', $this->id . $this->phone . time());
    }
}
