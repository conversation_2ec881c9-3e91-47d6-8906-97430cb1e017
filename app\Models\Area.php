<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Area extends Model
{
    use HasFactory;
    
    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'areas';

    /**
     * 指示模型是否主动维护时间戳
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * 主键
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 获取该地区的父级地区
     */
    public function parent()
    {
        return $this->belongsTo(Area::class, 'pid');
    }

    /**
     * 获取该地区的子地区
     */
    public function children()
    {
        return $this->hasMany(Area::class, 'pid');
    }

    /**
     * 获取所有子孙地区
     */
    public function descendants()
    {
        return $this->children()->with('descendants');
    }
}
