# JSON 格式 Access Token API 请求示例

## Postman JSON 请求配置

### 获取 Access Token (JSON 格式)

**请求配置:**
- **方法**: `POST`
- **URL**: `http://localhost/pyp-Laravel-new/public/api/auth/access-token`

**Headers:**
```json
{
  "Content-Type": "application/json",
  "Accept": "application/json"
}
```

**Body (raw JSON):**
```json
{
  "appKey": "red.qzgiaXL14ncBwlBz",
  "appSecret": "cf2ef172726d1272c4ed661e1c059a45",
  "grant_type": "client_credentials"
}
```

## 完整的 HTTP 请求报文

```http
POST /pyp-Laravel-new/public/api/auth/access-token HTTP/1.1
Host: localhost
Content-Type: application/json
Accept: application/json
Content-Length: 120

{
  "appKey": "red.qzgiaXL14ncBwlBz",
  "appSecret": "cf2ef172726d1272c4ed661e1c059a45",
  "grant_type": "client_credentials"
}
```

## 验证 Token (JSON 格式)

**请求配置:**
- **方法**: `POST`
- **URL**: `http://localhost/pyp-Laravel-new/public/api/auth/verify-token`

**Headers:**
```json
{
  "Content-Type": "application/json",
  "Accept": "application/json"
}
```

**Body (raw JSON):**
```json
{
  "access_token": "eyJhcHBfa2V5IjoicmVkLnF6Z2lhWEwxNG5jQndsQnoiLCJpYXQiOjE3NTIxMzA0OTUsImV4cCI6MTc1MjEzNzY5NSwic2NvcGUiOiJyZWFkIHdyaXRlIn0..."
}
```

## cURL 命令示例

### 获取 Access Token
```bash
curl -X POST 'http://localhost/pyp-Laravel-new/public/api/auth/access-token' \
  -H 'Content-Type: application/json' \
  -H 'Accept: application/json' \
  -d '{
    "appKey": "red.qzgiaXL14ncBwlBz",
    "appSecret": "cf2ef172726d1272c4ed661e1c059a45",
    "grant_type": "client_credentials"
  }'
```

### 验证 Token
```bash
curl -X POST 'http://localhost/pyp-Laravel-new/public/api/auth/verify-token' \
  -H 'Content-Type: application/json' \
  -H 'Accept: application/json' \
  -d '{
    "access_token": "YOUR_ACCESS_TOKEN_HERE"
  }'
```

## JavaScript fetch 示例

```javascript
// 获取 Access Token
fetch('http://localhost/pyp-Laravel-new/public/api/auth/access-token', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  body: JSON.stringify({
    appKey: 'red.qzgiaXL14ncBwlBz',
    appSecret: 'cf2ef172726d1272c4ed661e1c059a45',
    grant_type: 'client_credentials'
  })
})
.then(response => response.json())
.then(data => {
  console.log('Success:', data);
  if (data.code === 200) {
    console.log('Access Token:', data.data.access_token);
  }
})
.catch(error => {
  console.error('Error:', error);
});
```

## PHP 示例

```php
<?php
$url = 'http://localhost/pyp-Laravel-new/public/api/auth/access-token';
$data = [
    'appKey' => 'red.qzgiaXL14ncBwlBz',
    'appSecret' => 'cf2ef172726d1272c4ed661e1c059a45',
    'grant_type' => 'client_credentials'
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

$result = json_decode($response, true);
echo "响应状态码: $httpCode\n";
echo "响应内容: " . json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
?>
```

## Python requests 示例

```python
import requests
import json

url = 'http://localhost/pyp-Laravel-new/public/api/auth/access-token'
headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
}
data = {
    'appKey': 'red.qzgiaXL14ncBwlBz',
    'appSecret': 'cf2ef172726d1272c4ed661e1c059a45',
    'grant_type': 'client_credentials'
}

response = requests.post(url, headers=headers, data=json.dumps(data))
print(f"状态码: {response.status_code}")
print(f"响应内容: {response.json()}")
```

## 成功响应示例

```json
{
  "code": 200,
  "message": "获取access_token成功",
  "data": {
    "access_token": "eyJhcHBfa2V5IjoicmVkLnF6Z2lhWEwxNG5jQndsQnoiLCJpYXQiOjE3NTIxMzA0OTUsImV4cCI6MTc1MjEzNzY5NSwic2NvcGUiOiJyZWFkIHdyaXRlIn0.NTE5Yjk0OTU3NzIxNGE3NzM0M2Q4MTRkZTFjOWZlODNiOGZjZWE5NTM3NjI1NGU1NTVhMDZmNjZkMmRjYWE3NQ==",
    "token_type": "Bearer",
    "expires_in": 7200,
    "scope": "read write"
  },
  "timestamp": 1752130495
}
```

## 错误响应示例

```json
{
  "code": 400,
  "message": "无效的appKey或appSecret",
  "timestamp": 1752130495
}
```

## Postman 操作步骤

1. **新建请求**
   - 方法选择: `POST`
   - URL输入: `http://localhost/pyp-Laravel-new/public/api/auth/access-token`

2. **设置Headers**
   - 点击 "Headers" 标签
   - 添加: `Content-Type: application/json`
   - 添加: `Accept: application/json`

3. **设置Body**
   - 点击 "Body" 标签
   - 选择 "raw"
   - 在右侧下拉菜单选择 "JSON"
   - 输入JSON数据:
     ```json
     {
       "appKey": "red.qzgiaXL14ncBwlBz",
       "appSecret": "cf2ef172726d1272c4ed661e1c059a45",
       "grant_type": "client_credentials"
     }
     ```

4. **发送请求**
   - 点击 "Send" 按钮

## 注意事项

1. **Content-Type**: 必须设置为 `application/json`
2. **JSON格式**: 请求体必须是有效的JSON格式
3. **字段名称**: 参数名称必须精确匹配 (`appKey`, `appSecret`, `grant_type`)
4. **Accept头**: 建议添加 `Accept: application/json` 确保返回JSON格式 