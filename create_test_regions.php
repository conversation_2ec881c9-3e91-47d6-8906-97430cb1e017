<?php

require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\\Contracts\\Console\\Kernel')->bootstrap();

use App\Models\AgentRegion;

try {
    echo "=== 为二级代理商创建区域配置 ===" . PHP_EOL;

    // 为二级代理商-北京 (ID: 4) 创建区域配置
    $region1 = AgentRegion::create([
        'agent_id' => 4,
        'area_id' => 110105, // 北京市朝阳区的区域代码
        'province_name' => '北京市',
        'city_name' => '北京市',
        'district_name' => '朝阳区',
        'region_status' => 'active',
        'is_exclusive' => true,
        'commission_rate' => 15.00,
        'commission_type' => 'percentage',
        'settlement_cycle' => 'monthly',
        'contract_status' => 'signed',
        'contract_no' => 'BJ-2025-001',
        'signed_at' => now()->format('Y-m-d'),
        'next_settlement_at' => now()->addMonth()->format('Y-m-d'),
        'total_stores' => 5,
        'active_stores' => 4,
        'total_revenue' => 25000.00,
        'pending_settlement_amount' => 3750.00,
    ]);
    echo "✓ 创建北京区域配置 (ID: {$region1->id})" . PHP_EOL;

    // 为二级代理商-上海 (ID: 5) 创建区域配置
    $region2 = AgentRegion::create([
        'agent_id' => 5,
        'area_id' => 310115, // 上海市浦东新区的区域代码
        'province_name' => '上海市',
        'city_name' => '上海市',
        'district_name' => '浦东新区',
        'region_status' => 'active',
        'is_exclusive' => true,
        'commission_rate' => 18.00,
        'commission_type' => 'percentage',
        'settlement_cycle' => 'monthly',
        'contract_status' => 'signed',
        'contract_no' => 'SH-2025-001',
        'signed_at' => now()->format('Y-m-d'),
        'next_settlement_at' => now()->addMonth()->format('Y-m-d'),
        'total_stores' => 8,
        'active_stores' => 7,
        'total_revenue' => 45000.00,
        'pending_settlement_amount' => 8100.00,
    ]);
    echo "✓ 创建上海区域配置 (ID: {$region2->id})" . PHP_EOL;

    // 为二级代理商-深圳 (ID: 6) 创建区域配置
    $region3 = AgentRegion::create([
        'agent_id' => 6,
        'area_id' => 440305, // 深圳市南山区的区域代码
        'province_name' => '广东省',
        'city_name' => '深圳市',
        'district_name' => '南山区',
        'region_status' => 'active',
        'is_exclusive' => false,
        'commission_rate' => 20.00,
        'commission_type' => 'percentage',
        'settlement_cycle' => 'monthly',
        'contract_status' => 'signed',
        'contract_no' => 'SZ-2025-001',
        'signed_at' => now()->format('Y-m-d'),
        'next_settlement_at' => now()->addMonth()->format('Y-m-d'),
        'total_stores' => 12,
        'active_stores' => 10,
        'total_revenue' => 60000.00,
        'pending_settlement_amount' => 12000.00,
    ]);
    echo "✓ 创建深圳区域配置 (ID: {$region3->id})" . PHP_EOL;

    echo PHP_EOL . "=== 创建完成 ===" . PHP_EOL;
    echo "现在agent_001应该可以看到3条区域配置记录了！" . PHP_EOL;
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . PHP_EOL;
}
