<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    // 用户状态常量
    const STATUS_ACTIVE = 1;      // 正常
    const STATUS_INACTIVE = 0;    // 未激活
    const STATUS_SUSPENDED = 2;   // 暂停
    const STATUS_BANNED = 3;      // 封禁

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'phone',
        'avatar',
        'password',
        'status',
        'preferences',
        'last_login_at',
        'last_login_ip',
        'province_id',
        'city_id',
        'district_id',
        'address',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'preferences' => 'array',
        'last_login_at' => 'datetime',
        'status' => 'integer',
    ];

    /**
     * 默认用户偏好设置
     * <AUTHOR>
     */
    public static function getDefaultPreferences()
    {
        return [
            'language' => 'zh-CN',
            'timezone' => 'Asia/Shanghai',
            'theme' => 'light',
            'notifications' => [
                'email' => true,
                'sms' => true,
                'system' => true,
            ],
            'dashboard' => [
                'layout' => 'default',
                'widgets' => ['stats', 'charts', 'recent_activities'],
            ],
        ];
    }

    /**
     * 获取状态选项
     * <AUTHOR>
     */
    public static function getStatusOptions()
    {
        return [
            self::STATUS_ACTIVE => '正常',
            self::STATUS_INACTIVE => '未激活',
            self::STATUS_SUSPENDED => '暂停',
            self::STATUS_BANNED => '封禁',
        ];
    }

    /**
     * 获取状态标签
     * <AUTHOR>
     */
    public function getStatusLabelAttribute()
    {
        return self::getStatusOptions()[$this->status] ?? '未知';
    }

    /**
     * 获取状态颜色
     * <AUTHOR>
     */
    public function getStatusColorAttribute()
    {
        $colors = [
            self::STATUS_ACTIVE => 'success',
            self::STATUS_INACTIVE => 'warning',
            self::STATUS_SUSPENDED => 'info',
            self::STATUS_BANNED => 'danger',
        ];
        return $colors[$this->status] ?? 'secondary';
    }

    /**
     * 获取头像URL
     * <AUTHOR>
     */
    public function getAvatarUrlAttribute()
    {
        if ($this->avatar && Storage::exists($this->avatar)) {
            return Storage::url($this->avatar);
        }
        // 返回默认头像
        return asset('images/default-avatar.png');
    }

    /**
     * 获取显示名称
     * <AUTHOR>
     */
    public function getDisplayNameAttribute()
    {
        return $this->name ?: $this->email;
    }

    /**
     * 获取格式化的最后登录时间
     * <AUTHOR>
     */
    public function getLastLoginFormatAttribute()
    {
        return $this->last_login_at ? $this->last_login_at->format('Y-m-d H:i:s') : '从未登录';
    }

    /**
     * 获取注册天数
     * <AUTHOR>
     */
    public function getRegisteredDaysAttribute()
    {
        return $this->created_at->diffInDays(now());
    }

    /**
     * 获取偏好设置值
     * <AUTHOR>
     */
    public function getPreference($key, $default = null)
    {
        $preferences = $this->preferences ?: [];
        return data_get($preferences, $key, $default);
    }

    /**
     * 设置偏好设置值
     * <AUTHOR>
     */
    public function setPreference($key, $value)
    {
        $preferences = $this->preferences ?: [];
        data_set($preferences, $key, $value);
        $this->preferences = $preferences;
        return $this;
    }

    /**
     * 重置偏好设置为默认值
     * <AUTHOR>
     */
    public function resetPreferences()
    {
        $this->preferences = self::getDefaultPreferences();
        return $this;
    }
    
    /**
     * 获取用户所在省份
     * <AUTHOR>
     */
    public function province()
    {
        return $this->belongsTo(Area::class, 'province_id', 'id');
    }
    
    /**
     * 获取用户所在城市
     * <AUTHOR>
     */
    public function city()
    {
        return $this->belongsTo(Area::class, 'city_id', 'id');
    }
    
    /**
     * 获取用户所在区县
     * <AUTHOR>
     */
    public function district()
    {
        return $this->belongsTo(Area::class, 'district_id', 'id');
    }
    
    /**
     * 获取用户完整地址信息
     * <AUTHOR>
     */
    public function getFullAddressAttribute()
    {
        $address = '';
        if ($this->province) {
            $address .= $this->province->name;
        }
        if ($this->city) {
            $address .= $this->city->name;
        }
        if ($this->district) {
            $address .= $this->district->name;
        }
        if ($this->address) {
            $address .= $this->address;
        }
        return $address;
    }

    // ==================== 查询作用域 ====================

    /**
     * 按状态筛选
     * <AUTHOR>
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 筛选已激活用户
     * <AUTHOR>
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    /**
     * 筛选已验证邮箱的用户
     * <AUTHOR>
     */
    public function scopeVerified($query)
    {
        return $query->whereNotNull('email_verified_at');
    }

    /**
     * 关键词搜索
     * <AUTHOR>
     */
    public function scopeSearch($query, $keyword)
    {
        if (empty($keyword)) {
            return $query;
        }
        
        return $query->where(function ($q) use ($keyword) {
            $q->where('name', 'like', "%{$keyword}%")
              ->orWhere('email', 'like', "%{$keyword}%")
              ->orWhere('phone', 'like', "%{$keyword}%");
        });
    }

    /**
     * 按角色筛选
     * <AUTHOR>
     */
    public function scopeByRole($query, $role)
    {
        // 这里需要根据实际的角色系统实现
        // 暂时预留接口
        return $query;
    }

    /**
     * 最近登录的用户
     * <AUTHOR>
     */
    public function scopeRecentLogin($query, $days = 30)
    {
        return $query->where('last_login_at', '>=', now()->subDays($days));
    }

    // ==================== 状态管理方法 ====================

    /**
     * 激活用户
     * <AUTHOR>
     */
    public function activate()
    {
        $this->status = self::STATUS_ACTIVE;
        return $this->save();
    }

    /**
     * 停用用户
     * <AUTHOR>
     */
    public function deactivate()
    {
        $this->status = self::STATUS_INACTIVE;
        return $this->save();
    }

    /**
     * 暂停用户
     * <AUTHOR>
     */
    public function suspend()
    {
        $this->status = self::STATUS_SUSPENDED;
        return $this->save();
    }

    /**
     * 封禁用户
     * <AUTHOR>
     */
    public function ban()
    {
        $this->status = self::STATUS_BANNED;
        return $this->save();
    }

    /**
     * 更新最后登录信息
     * <AUTHOR>
     */
    public function updateLastLogin($ip = null)
    {
        $this->last_login_at = now();
        $this->last_login_ip = $ip ?: request()->ip();
        return $this->save();
    }

    /**
     * 检查用户是否可以登录
     * <AUTHOR>
     */
    public function canLogin()
    {
        return in_array($this->status, [self::STATUS_ACTIVE]);
    }

    /**
     * 检查用户是否为管理员
     * <AUTHOR>
     */
    public function isAdmin()
    {
        // 这里需要根据实际的角色系统实现
        // 暂时预留接口
        return false;
    }

    /**
     * 获取用户权限
     * <AUTHOR>
     */
    public function getPermissions()
    {
        // 这里需要根据实际的权限系统实现
        // 暂时预留接口
        return [];
    }

    /**
     * 获取用户角色
     * <AUTHOR>
     */
    public function getRoles()
    {
        // 这里需要根据实际的角色系统实现
        // 暂时预留接口
        return [];
    }

    /**
     * 生成API令牌
     * <AUTHOR>
     */
    public function generateApiToken($name = 'default')
    {
        return $this->createToken($name)->plainTextToken;
    }

    /**
     * 撤销所有API令牌
     * <AUTHOR>
     */
    public function revokeAllTokens()
    {
        return $this->tokens()->delete();
    }

    /**
     * 发送通知
     * <AUTHOR>
     */
    public function sendNotification($notification)
    {
        // 这里需要根据实际的通知系统实现
        // 暂时预留接口
        return true;
    }

    /**
     * 获取用户统计信息
     * <AUTHOR>
     */
    public static function getStatistics()
    {
        return [
            'total' => self::count(),
            'active' => self::where('status', self::STATUS_ACTIVE)->count(),
            'inactive' => self::where('status', self::STATUS_INACTIVE)->count(),
            'suspended' => self::where('status', self::STATUS_SUSPENDED)->count(),
            'banned' => self::where('status', self::STATUS_BANNED)->count(),
            'verified' => self::whereNotNull('email_verified_at')->count(),
            'recent_login' => self::where('last_login_at', '>=', now()->subDays(30))->count(),
        ];
    }

    /**
     * 获取用户活动日志
     * <AUTHOR>
     */
    public function getActivityLogs($limit = 10)
    {
        // 这里需要根据实际的日志系统实现
        // 暂时预留接口
        return [];
    }

    /**
     * 检查密码强度
     * <AUTHOR>
     */
    public static function checkPasswordStrength($password)
    {
        $score = 0;
        $feedback = [];
        
        // 长度检查
        if (strlen($password) >= 8) {
            $score += 1;
        } else {
            $feedback[] = '密码长度至少8位';
        }
        
        // 包含数字
        if (preg_match('/\d/', $password)) {
            $score += 1;
        } else {
            $feedback[] = '密码应包含数字';
        }
        
        // 包含小写字母
        if (preg_match('/[a-z]/', $password)) {
            $score += 1;
        } else {
            $feedback[] = '密码应包含小写字母';
        }
        
        // 包含大写字母
        if (preg_match('/[A-Z]/', $password)) {
            $score += 1;
        } else {
            $feedback[] = '密码应包含大写字母';
        }
        
        // 包含特殊字符
        if (preg_match('/[^\w\s]/', $password)) {
            $score += 1;
        } else {
            $feedback[] = '密码应包含特殊字符';
        }
        
        $strength = 'weak';
        if ($score >= 4) {
            $strength = 'strong';
        } elseif ($score >= 3) {
            $strength = 'medium';
        }
        
        return [
            'score' => $score,
            'strength' => $strength,
            'feedback' => $feedback,
        ];
    }

    /**
     * 获取系统统计信息
     * <AUTHOR>
     */
    public static function getSystemStatistics()
    {
        return [
            'users' => self::getStatistics(),
            'today_registrations' => self::whereDate('created_at', today())->count(),
            'this_week_registrations' => self::whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count(),
            'this_month_registrations' => self::whereMonth('created_at', now()->month)->count(),
        ];
    }

    /**
     * 模型启动方法
     * <AUTHOR>
     */
    protected static function boot()
    {
        parent::boot();
        
        // 创建用户时设置默认值
        static::creating(function ($user) {
            if (empty($user->preferences)) {
                $user->preferences = self::getDefaultPreferences();
            }
            if (empty($user->status)) {
                $user->status = self::STATUS_INACTIVE;
            }
        });
    }
}
