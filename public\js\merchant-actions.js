/**
 * 商铺管理操作脚本
 * 解决JavaScript嵌入PHP字符串导致的语法错误问题
 */

// 删除商铺功能
function deleteStore(id) {
    swal({
        title: "确认删除",
        text: "您确定要删除这个商铺吗？此操作不可撤销！",
        icon: "warning",
        buttons: {
            cancel: {
                text: "取消",
                value: null,
                visible: true,
                className: "",
                closeModal: true,
            },
            confirm: {
                text: "确认删除",
                value: true,
                visible: true,
                className: "btn-danger",
                closeModal: false
            }
        }
    }).then((willDelete) => {
        if (willDelete) {
            // 发送删除请求
            $.ajax({
                url: "/admin/merchants/" + id,
                type: "DELETE",
                data: {
                    "_token": $('meta[name="csrf-token"]').attr('content')
                },
                success: function (response) {
                    if (response.status) {
                        swal("删除成功!", response.message || "商铺已成功删除", "success").then(() => {
                            // 使用PJAX重新加载页面
                            $.pjax.reload("#pjax-container");
                        });
                    } else {
                        swal("删除失败!", response.message || "删除商铺时发生错误", "error");
                    }
                },
                error: function (xhr, status, error) {
                    console.error("删除请求错误:", xhr.responseText);
                    swal("删除失败!", "服务器错误，请稍后重试", "error");
                }
            });
        }
    });
}

// PJAX环境兼容性 - 多重事件监听机制
function initMerchantActions() {
    console.log('商铺管理操作脚本已初始化');

    // 确保删除功能在全局可用
    window.deleteStore = deleteStore;
}

// 按照技术指南的多重事件监听机制
$(document).ready(function () {
    initMerchantActions();
});

$(document).on('pjax:complete', function () {
    initMerchantActions();
});

$(document).on('pjax:end', function () {
    initMerchantActions();
});

window.addEventListener('load', function () {
    initMerchantActions();
}); 