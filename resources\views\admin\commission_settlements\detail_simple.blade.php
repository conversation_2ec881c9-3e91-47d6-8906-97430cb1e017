<div class="row">
    <div class="col-md-12">
        <div class="box">
            <div class="box-header with-border">
                <h3 class="box-title">结算详情</h3>
                <div class="box-tools">
                    <a href="{{ admin_url('commission-settlements') }}" class="btn btn-sm btn-default">
                        <i class="fa fa-list"></i> 返回列表
                    </a>
                </div>
            </div>
            <div class="box-body">
                <!-- 结算信息 -->
                <div class="settlement-info" style="margin-bottom: 30px;">
                    <h4>结算信息</h4>
                    @if($settlement)
                    <table class="table table-bordered">
                        <tbody>
                            <tr>
                                <th width="15%">结算单号</th>
                                <td width="35%">{{ $settlement->settlement_no ?? '-' }}</td>
                                <th width="15%">结算对象</th>
                                <td width="35%">{{ $settlement->target_name ?? '-' }}</td>
                            </tr>
                            <tr>
                                <th>结算周期</th>
                                <td>{{ $settlement->settlement_period_start ? $settlement->settlement_period_start->format('Y-m-d') : '-' }} 至 {{ $settlement->settlement_period_end ? $settlement->settlement_period_end->format('Y-m-d') : '-' }}</td>
                                <th>结算状态</th>
                                <td>
                                    @switch($settlement->status)
                                    @case('pending')
                                    <span class="label label-warning">待审核</span>
                                    @break
                                    @case('approved')
                                    <span class="label label-success">已审核</span>
                                    @break
                                    @case('paid')
                                    <span class="label label-info">已支付</span>
                                    @break
                                    @case('rejected')
                                    <span class="label label-danger">已拒绝</span>
                                    @break
                                    @default
                                    {{ $settlement->status }}
                                    @endswitch
                                </td>
                            </tr>
                            <tr>
                                <th>总金额</th>
                                <td>￥{{ number_format($settlement->total_amount ?? 0, 2) }}</td>
                                <th>佣金金额</th>
                                <td>￥{{ number_format($settlement->commission_amount ?? 0, 2) }}</td>
                            </tr>
                            <tr>
                                <th>扣除金额</th>
                                <td>￥{{ number_format($settlement->deduction_amount ?? 0, 2) }}</td>
                                <th>实际结算金额</th>
                                <td>￥{{ number_format($settlement->actual_amount ?? 0, 2) }}</td>
                            </tr>
                            @if($agentRegion)
                            <tr>
                                <th>计算公式</th>
                                <td colspan="3">
                                    <div class="calculation-formula">
                                        @if($agentRegion->commission_type === 'percentage')
                                        <span class="text-info">按比例计算：总金额 × {{ $agentRegion->commission_rate }}% = ￥{{ number_format($settlement->total_amount * ($agentRegion->commission_rate / 100), 2) }}</span>
                                        @elseif($agentRegion->commission_type === 'fixed')
                                        <span class="text-info">固定金额：{{ $settlement->store_count }}家商铺 × ￥{{ number_format($agentRegion->commission_amount ?? 0, 2) }} = ￥{{ number_format($settlement->store_count * ($agentRegion->commission_amount ?? 0), 2) }}</span>
                                        @elseif($agentRegion->commission_type === 'tiered')
                                        <span class="text-info">阶梯计算：{{ $agentRegion->commission_rules ?? '规则未配置' }}</span>
                                        @else
                                        <span class="text-muted">计算方式：{{ $agentRegion->commission_type ?? '未知' }}</span>
                                        @endif
                                        <br>
                                        <small class="text-muted">实际结算金额 = 佣金金额 - 扣除金额 = ￥{{ number_format($settlement->commission_amount ?? 0, 2) }} - ￥{{ number_format($settlement->deduction_amount ?? 0, 2) }} = ￥{{ number_format($settlement->actual_amount ?? 0, 2) }}</small>
                                    </div>
                                </td>
                            </tr>
                            @endif
                            <tr>
                                <th>商铺数量</th>
                                <td>{{ $settlement->store_count ?? 0 }}家</td>
                                <th>活跃商铺</th>
                                <td>{{ $settlement->active_store_count ?? 0 }}家</td>
                            </tr>
                            <tr>
                                <th>创建时间</th>
                                <td>{{ $settlement->created_at ? $settlement->created_at->format('Y-m-d H:i:s') : '-' }}</td>
                                <th>支付时间</th>
                                <td>{{ $settlement->paid_at ? $settlement->paid_at->format('Y-m-d H:i:s') : '-' }}</td>
                            </tr>
                            <tr>
                                <th>备注</th>
                                <td colspan="3">{{ $settlement->remark ?? '-' }}</td>
                            </tr>
                        </tbody>
                    </table>
                    @else
                    <div class="alert alert-warning">未找到结算信息</div>
                    @endif
                </div>

                <!-- 商铺信息 -->
                <div class="store-info" style="margin-bottom: 30px;">
                    <h4>关联商铺信息 ({{ $stores->count() }}家)</h4>
                    @if($stores->isNotEmpty())
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>商铺ID</th>
                                <th>商铺名称</th>
                                <th>联系人</th>
                                <th>联系电话</th>
                                <th>地址</th>
                                <th>商品金额</th>
                                <th>结算状态</th>
                                <th>审核状态</th>
                                <th>商铺状态</th>
                                <th>审核备注</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($stores as $store)
                            <tr>
                                <td>{{ $store['id'] ?? '-' }}</td>
                                <td>{{ $store['name'] ?? '-' }}</td>
                                <td>{{ $store['contact_person'] ?? '-' }}</td>
                                <td>{{ $store['phone'] ?? '-' }}</td>
                                <td>{{ ($store['province_name'] ?? '') . ' ' . ($store['city_name'] ?? '') . ' ' . ($store['district_name'] ?? '') . ' ' . ($store['address'] ?? '') }}</td>
                                <td>￥{{ number_format($store['product_amount'] ?? 0, 2) }}</td>
                                <td>
                                    @if(($store['settlement_status'] ?? 0) == 0)
                                    <span class="label label-default">未结算</span>
                                    @elseif(($store['settlement_status'] ?? 0) == 1)
                                    <span class="label label-warning">待结算</span>
                                    @elseif(($store['settlement_status'] ?? 0) == 2)
                                    <span class="label label-success">已结算</span>
                                    @else
                                    <span class="label label-default">未知</span>
                                    @endif
                                </td>
                                <td>{{ $store['audit_status_name'] ?? '-' }}</td>
                                <td>{{ $store['store_status_name'] ?? '-' }}</td>
                                <td>{{ $store['audit_remark'] ?? '-' }}</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                    @else
                    <div class="alert alert-warning">未找到相关商铺信息</div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .table th {
        background-color: #f9f9f9;
    }

    .label {
        font-size: 12px;
        padding: 4px 8px;
    }

    .box-tools {
        position: absolute;
        right: 10px;
        top: 5px;
    }

    .table-striped tbody tr:nth-of-type(odd) {
        background-color: #f9f9f9;
    }

    .table-striped tbody tr:hover {
        background-color: #f5f5f5;
    }

    .table td {
        vertical-align: middle;
        font-size: 13px;
    }

    .table thead th {
        background-color: #3c8dbc;
        color: white;
        font-weight: bold;
        text-align: center;
        border-color: #367fa9;
    }

    .settlement-info h4,
    .store-info h4 {
        color: #3c8dbc;
        border-bottom: 2px solid #3c8dbc;
        padding-bottom: 5px;
        margin-bottom: 15px;
    }

    .text-info {
        color: #31708f !important;
        font-weight: bold;
    }

    .text-muted {
        color: #777 !important;
    }

    .calculation-formula {
        background-color: #f8f9fa;
        padding: 10px;
        border-left: 4px solid #3c8dbc;
        margin: 5px 0;
    }
</style>