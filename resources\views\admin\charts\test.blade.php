<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>图表测试页面</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .chart-container { margin: 20px 0; }
        .log-container { 
            background: #f5f5f5; 
            padding: 10px; 
            border-radius: 3px; 
            font-family: monospace; 
            font-size: 12px;
            height: 200px;
            overflow-y: auto;
        }
        .btn { 
            padding: 8px 16px; 
            margin: 5px; 
            border: none; 
            border-radius: 3px; 
            cursor: pointer; 
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-success { background: #28a745; color: white; }
    </style>
</head>
<body>
    <h1>🧪 ECharts PJAX环境测试页面</h1>
    
    <div class="test-section">
        <h3>📊 收入趋势图表测试</h3>
        <div class="chart-container">
            <div id="revenue-chart-test" style="width: 100%; height: 300px; border: 1px solid #ddd;"></div>
        </div>
        <div>
            <button class="btn btn-primary" onclick="initRevenueChart()">初始化图表</button>
            <button class="btn btn-danger" onclick="destroyRevenueChart()">销毁图表</button>
            <button class="btn btn-success" onclick="simulatePjax()">模拟PJAX重载</button>
        </div>
    </div>

    <div class="test-section">
        <h3>🥧 用户分布饼图测试</h3>
        <div class="chart-container">
            <div id="user-type-chart-test" style="width: 100%; height: 300px; border: 1px solid #ddd;"></div>
        </div>
        <div>
            <button class="btn btn-primary" onclick="initUserTypeChart()">初始化饼图</button>
            <button class="btn btn-danger" onclick="destroyUserTypeChart()">销毁饼图</button>
        </div>
    </div>

    <div class="test-section">
        <h3>📝 实时日志</h3>
        <div id="log-container" class="log-container"></div>
        <button class="btn btn-primary" onclick="clearLog()">清空日志</button>
    </div>

    <script>
        // 日志系统
        var originalConsoleLog = console.log;
        var originalConsoleError = console.error;
        var originalConsoleWarn = console.warn;
        
        function addLog(type, message) {
            var logContainer = document.getElementById('log-container');
            var timestamp = new Date().toLocaleTimeString();
            var logEntry = document.createElement('div');
            logEntry.innerHTML = '[' + timestamp + '] [' + type.toUpperCase() + '] ' + message;
            logEntry.style.color = type === 'error' ? 'red' : (type === 'warn' ? 'orange' : 'black');
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        console.log = function(message) {
            addLog('info', message);
            originalConsoleLog.apply(console, arguments);
        };
        
        console.error = function(message) {
            addLog('error', message);
            originalConsoleError.apply(console, arguments);
        };
        
        console.warn = function(message) {
            addLog('warn', message);
            originalConsoleWarn.apply(console, arguments);
        };
        
        function clearLog() {
            document.getElementById('log-container').innerHTML = '';
        }
        
        // 全局图表实例
        var testRevenueChart = null;
        var testUserTypeChart = null;
        
        // 收入图表初始化
        function initRevenueChart() {
            console.log('🔄 开始初始化收入测试图表');
            
            // 清理旧实例
            if (testRevenueChart) {
                try {
                    testRevenueChart.dispose();
                    console.log('✅ 已清理旧的收入图表实例');
                } catch (e) {
                    console.warn('⚠️ 清理图表实例时出错:', e);
                }
                testRevenueChart = null;
            }
            
            var chartDom = document.getElementById('revenue-chart-test');
            if (!chartDom) {
                console.error('❌ 图表容器未找到');
                return;
            }
            
            try {
                testRevenueChart = echarts.init(chartDom);
                
                var option = {
                    title: {
                        text: '最近7天收入趋势',
                        left: 'center',
                        textStyle: { fontSize: 14, color: '#333' }
                    },
                    tooltip: {
                        trigger: 'axis',
                        formatter: function(params) {
                            var result = params[0].name;
                            result += '\n收入: ￥' + params[0].value.toLocaleString();
                            return result;
                        }
                    },
                    grid: {
                        left: '3%', right: '4%', bottom: '3%', containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        boundaryGap: false,
                        data: ['06-20','06-21','06-22','06-23','06-24','06-25','06-26']
                    },
                    yAxis: {
                        type: 'value',
                        axisLabel: { formatter: '￥{value}' }
                    },
                    series: [{
                        name: '收入',
                        type: 'line',
                        smooth: true,
                        areaStyle: {
                            color: {
                                type: 'linear',
                                x: 0, y: 0, x2: 0, y2: 1,
                                colorStops: [
                                    { offset: 0, color: 'rgba(54, 162, 235, 0.3)' },
                                    { offset: 1, color: 'rgba(54, 162, 235, 0.1)' }
                                ]
                            }
                        },
                        lineStyle: { color: 'rgba(54, 162, 235, 1)', width: 2 },
                        data: [4414,1092,4148,4118,1248,4751,4939]
                    }]
                };
                
                testRevenueChart.setOption(option);
                console.log('✅ 收入图表初始化成功');
                
            } catch (error) {
                console.error('❌ 图表初始化失败:', error);
            }
        }
        
        // 用户分布图表初始化
        function initUserTypeChart() {
            console.log('🔄 开始初始化用户分布测试图表');
            
            // 清理旧实例
            if (testUserTypeChart) {
                try {
                    testUserTypeChart.dispose();
                    console.log('✅ 已清理旧的用户分布图表实例');
                } catch (e) {
                    console.warn('⚠️ 清理图表实例时出错:', e);
                }
                testUserTypeChart = null;
            }
            
            var chartDom = document.getElementById('user-type-chart-test');
            if (!chartDom) {
                console.error('❌ 图表容器未找到');
                return;
            }
            
            try {
                testUserTypeChart = echarts.init(chartDom);
                
                var pieData = [
                    { name: '代理商', value: 45 },
                    { name: '商铺', value: 30 },
                    { name: '业务员', value: 20 },
                    { name: '其他', value: 5 }
                ];
                
                var option = {
                    title: {
                        text: '用户类型分布',
                        left: 'center',
                        textStyle: { fontSize: 14, color: '#333' }
                    },
                    tooltip: {
                        trigger: 'item',
                        formatter: function(params) {
                            return params.seriesName + '\n' + 
                                   params.name + ': ' + params.value + ' (' + params.percent + '%)';
                        }
                    },
                    legend: {
                        orient: 'vertical',
                        left: 'left'
                    },
                    series: [{
                        name: '用户分布',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        center: ['50%', '60%'],
                        avoidLabelOverlap: false,
                        emphasis: {
                            label: { show: true, fontSize: '18', fontWeight: 'bold' }
                        },
                        data: pieData,
                        itemStyle: {
                            color: function(params) {
                                var colors = ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0'];
                                return colors[params.dataIndex % colors.length];
                            }
                        }
                    }]
                };
                
                testUserTypeChart.setOption(option);
                console.log('✅ 用户分布图表初始化成功');
                
            } catch (error) {
                console.error('❌ 用户分布图表初始化失败:', error);
            }
        }
        
        // 销毁图表
        function destroyRevenueChart() {
            if (testRevenueChart) {
                testRevenueChart.dispose();
                testRevenueChart = null;
                console.log('🗑️ 收入图表已销毁');
            }
        }
        
        function destroyUserTypeChart() {
            if (testUserTypeChart) {
                testUserTypeChart.dispose();
                testUserTypeChart = null;
                console.log('🗑️ 用户分布图表已销毁');
            }
        }
        
        // 模拟PJAX重载
        function simulatePjax() {
            console.log('🔄 模拟PJAX页面重载...');
            
            // 销毁现有图表
            destroyRevenueChart();
            destroyUserTypeChart();
            
            // 模拟重新加载
            setTimeout(function() {
                console.log('🔄 重新初始化图表...');
                initRevenueChart();
                initUserTypeChart();
            }, 1000);
        }
        
        // 页面加载完成后自动初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📄 页面加载完成，开始自动初始化图表');
            setTimeout(function() {
                initRevenueChart();
                initUserTypeChart();
            }, 500);
        });
        
        // 添加窗口大小调整处理
        window.addEventListener('resize', function() {
            if (testRevenueChart) {
                testRevenueChart.resize();
            }
            if (testUserTypeChart) {
                testUserTypeChart.resize();
            }
            console.log('📐 窗口大小调整，图表已重新调整尺寸');
        });
    </script>
</body>
</html> 