<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('admin_users', function (Blueprint $table) {
            // 代理商关联字段
            $table->unsignedBigInteger('agent_id')->nullable()->after('name')->comment('关联代理商ID');
            $table->tinyInteger('user_type')->default(1)->after('agent_id')->comment('用户类型：1=平台管理员，2=一级代理商，3=二级代理商');
            $table->json('data_permissions')->nullable()->after('user_type')->comment('数据权限配置');
            $table->timestamp('last_login_at')->nullable()->after('remember_token')->comment('最后登录时间');
            $table->string('last_login_ip', 45)->nullable()->after('last_login_at')->comment('最后登录IP');

            // 添加索引
            $table->index('agent_id');
            $table->index('user_type');
            $table->index('last_login_at');

            // 外键约束（如果需要）
            $table->foreign('agent_id')->references('id')->on('agents')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('admin_users', function (Blueprint $table) {
            $table->dropForeign(['agent_id']);
            $table->dropIndex(['agent_id']);
            $table->dropIndex(['user_type']);
            $table->dropIndex(['last_login_at']);

            $table->dropColumn([
                'agent_id',
                'user_type',
                'data_permissions',
                'last_login_at',
                'last_login_ip'
            ]);
        });
    }
};
