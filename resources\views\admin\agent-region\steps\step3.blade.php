<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            <label for="commission_type" class="required">返佣类型 *</label>
            <select class="form-control" name="commission_type" id="commission_type" required>
                <option value="">请选择返佣类型</option>
                <option value="percentage" {{ (isset($agent_region) && $agent_region->commission_type == 'percentage') ? 'selected' : '' }}>按比例</option>
                <option value="fixed" {{ (isset($agent_region) && $agent_region->commission_type == 'fixed') ? 'selected' : '' }}>固定金额</option>
                <option value="tiered" {{ (isset($agent_region) && $agent_region->commission_type == 'tiered') ? 'selected' : '' }}>阶梯式</option>
            </select>
            <small class="text-muted">选择返佣计算方式</small>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="form-group">
            <label for="commission_rate">返佣比例(%)</label>
            <input type="number" class="form-control" name="commission_rate" id="commission_rate" 
                   min="0" max="100" step="0.01" placeholder="0.00" 
                   value="{{ isset($agent_region) ? $agent_region->commission_rate : '' }}">
            <small class="text-muted">适用于按比例返佣，范围0-100%</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            <label for="commission_amount">固定返佣金额(元)</label>
            <input type="number" class="form-control" name="commission_amount" id="commission_amount" 
                   min="0" step="0.01" placeholder="0.00"
                   value="{{ isset($agent_region) ? $agent_region->commission_amount : '' }}">
            <small class="text-muted">适用于固定金额返佣</small>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="form-group">
            <label for="min_order_amount">最低订单金额(元)</label>
            <input type="number" class="form-control" name="min_order_amount" id="min_order_amount" 
                   min="0" step="0.01" placeholder="0.00"
                   value="{{ isset($agent_region) ? $agent_region->min_order_amount : '' }}">
            <small class="text-muted">返佣的最低订单门槛</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            <label for="max_commission_per_order">单笔最高返佣(元)</label>
            <input type="number" class="form-control" name="max_commission_per_order" id="max_commission_per_order" 
                   min="0" step="0.01" placeholder="不限制"
                   value="{{ isset($agent_region) ? $agent_region->max_commission_per_order : '' }}">
            <small class="text-muted">单笔订单返佣上限，留空表示不限制</small>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="form-group">
            <label for="max_commission_per_month">月度返佣上限(元)</label>
            <input type="number" class="form-control" name="max_commission_per_month" id="max_commission_per_month" 
                   min="0" step="0.01" placeholder="不限制"
                   value="{{ isset($agent_region) ? $agent_region->max_commission_per_month : '' }}">
            <small class="text-muted">月度返佣总额上限，留空表示不限制</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label for="commission_rules">阶梯式返佣规则</label>
            <textarea class="form-control" name="commission_rules" id="commission_rules" rows="6" 
                      placeholder="请输入阶梯式返佣规则，格式如：
1-1000元: 5%
1001-5000元: 8%
5001元以上: 10%">{{ isset($agent_region) ? $agent_region->commission_rules : '' }}</textarea>
            <small class="text-muted">仅当返佣类型为"阶梯式"时填写，每行一个规则</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            <label for="commission_effective_date">返佣生效日期</label>
            <input type="date" class="form-control" name="commission_effective_date" id="commission_effective_date"
                   value="{{ isset($agent_region) && $agent_region->commission_effective_date ? $agent_region->commission_effective_date->format('Y-m-d') : (isset($agent_region) && $agent_region->contract_start_date ? $agent_region->contract_start_date->format('Y-m-d') : '') }}">
            <small class="text-muted">返佣规则开始生效的日期</small>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="form-group">
            <label for="commission_expire_date">返佣到期日期</label>
            <input type="date" class="form-control" name="commission_expire_date" id="commission_expire_date"
                   value="{{ isset($agent_region) && $agent_region->commission_expire_date ? $agent_region->commission_expire_date->format('Y-m-d') : (isset($agent_region) && $agent_region->contract_end_date ? $agent_region->contract_end_date->format('Y-m-d') : '') }}">
            <small class="text-muted">返佣规则失效日期，留空表示长期有效</small>
        </div>
    </div>
</div>

<div class="alert alert-info">
    <i class="fa fa-lightbulb-o"></i>
    <strong>返佣配置说明：</strong>
    <ul class="mb-0 mt-2">
        <li><strong>按比例：</strong>根据订单金额按设定比例计算返佣</li>
        <li><strong>固定金额：</strong>每单返佣固定金额，不考虑订单大小</li>
        <li><strong>阶梯式：</strong>根据订单金额区间使用不同返佣比例</li>
        <li>可设置单笔和月度返佣上限，有效控制成本</li>
        <li>最低订单金额设置可避免小额订单的返佣成本</li>
    </ul>
</div> 