<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class AddDeletedAtToAgentsTable extends Command
{
    /**
     * 命令名称
     *
     * @var string
     */
    protected $signature = 'agents:add-deleted-at';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = '向agents表添加deleted_at列';

    /**
     * 执行命令
     */
    public function handle()
    {
        $this->info('开始向agents表添加deleted_at列...');

        try {
            if (!Schema::hasColumn('agents', 'deleted_at')) {
                DB::statement('ALTER TABLE `agents` ADD COLUMN `deleted_at` TIMESTAMP NULL DEFAULT NULL COMMENT "软删除时间" AFTER `updated_at`');
                $this->info('成功添加deleted_at列到agents表');
            } else {
                $this->info('agents表已经有deleted_at列，无需添加');
            }
            
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('添加deleted_at列时发生错误: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
} 