# pyp-Laravel-new 数据表关系说明文档

## 项目概述
Laravel + Laravel-Admin NFC多媒体营销平台后台项目（碰一碰智能营销系统），实现代理商、商铺、素材、业务员等全业务链管理。

## 核心数据表关系图

### 1. 代理商管理体系

#### 1.1 代理商层级关系
- **agents** (代理商表) - 核心表
  - `id` - 代理商ID (主键)
  - `parent_agent_id` - 上级代理商ID (外键，自关联)
  - `type` - 代理商类型 (0:超级管理员, 1:平台管理员, 2:一级代理商, 3:二级代理商)
  - `level` - 代理商层级 (1-4)
  - `path` - 层级路径 (如: 1-3-5 表示层级关系)
  - `status` - 状态 (active/inactive/suspended)

#### 1.2 代理商区域配置
- **agent_regions** (代理商区域配置表)
  - `agent_id` → `agents.id` (外键关联)
  - `area_id` - 区域ID (关联areas表)
  - `commission_rate` - 返佣比例
  - `max_stores` - 最大管理商铺数

#### 1.3 管理员用户关联
- **admin_users** (管理员用户表) - Laravel-Admin核心表
  - `agent_id` → `agents.id` (外键关联，建立用户与代理商的关系)
  - `username`, `password` - 登录凭证
  - `name` - 用户姓名
  - `status` - 用户状态

### 2. 权限管理体系 (Laravel-Admin RBAC)

#### 2.1 权限基础表
- **admin_permissions** (权限表)
  - 权限标识符格式: `模块.动作` (如: agent.create, store.show)
  
- **admin_roles** (角色表)
  - 角色定义: 平台管理员、一级代理商、二级代理商等

#### 2.2 权限关联表
- **admin_role_permissions** (角色权限关联)
  - `role_id` → `admin_roles.id`
  - `permission_id` → `admin_permissions.id`

- **admin_role_users** (用户角色关联)
  - `role_id` → `admin_roles.id`
  - `user_id` → `admin_users.id`

- **admin_user_permissions** (用户直接权限)
  - `user_id` → `admin_users.id`
  - `permission_id` → `admin_permissions.id`

#### 2.3 菜单权限
- **admin_menu** (菜单表)
  - `parent_id` - 上级菜单ID (自关联)
  - `title` - 菜单标题
  - `uri` - 路由地址
  - `permission` - 权限标识

- **admin_role_menu** (角色菜单关联)
  - `role_id` → `admin_roles.id`
  - `menu_id` → `admin_menu.id`

### 3. 商铺管理体系

#### 3.1 商铺基础信息
- **stores** (商铺表) - 核心商业实体
  - `id` - 商铺ID (主键)
  - `agent_id` → `agents.id` (外键，所属代理商)
  - `area_id` - 区域ID (关联areas表)
  - `nfc_chip_id` - NFC芯片ID (唯一标识)
  - `audit_status` - 审核状态 (pending/approved/rejected)
  - `store_status` - 商铺状态 (active/inactive/suspended)
  - `settlement_status` - 结算状态 (0未结算/1待结算/2已结算)

#### 3.2 商铺素材关联
- **store_materials** (商铺素材关联表)
  - `store_id` → `stores.id` (外键)
  - `material_id` → `materials.id` (外键)
  - `platform_id` → `media_platforms.id` (外键)
  - `module_type` - 模块类型 (video/review/social/ecommerce)
  - `is_active` - 是否启用
  - `sort_order` - 排序

### 4. 素材管理体系

#### 4.1 素材基础表
- **materials** (素材表)
  - `id` - 素材ID (主键)
  - `name` - 素材名称
  - `type` - 素材类型 (image/video/audio/document)
  - `file_path` - 文件路径
  - `category` - 素材分类
  - `status` - 状态 (active/inactive)

#### 4.2 媒体平台配置
- **media_platforms** (媒体平台表)
  - `id` - 平台ID (主键)
  - `name` - 平台名称 (抖音/快手/小红书等)
  - `type` - 平台类型
  - `api_config` - API配置 (JSON格式)
  - `status` - 平台状态

### 5. 业务员管理体系

#### 5.1 业务员基础信息
- **salespeople** (业务员表)
  - `id` - 业务员ID (主键)
  - `agent_id` → `agents.id` (外键，所属代理商)
  - `team_id` → `teams.id` (外键，所属团队，可选)
  - `type` - 类型 (direct:直属/team:团队)
  - `employee_type` - 员工类型 (platform:平台/agent:代理商)
  - `status` - 状态 (active/inactive/resigned)

#### 5.2 团队管理
- **teams** (团队表)
  - `id` - 团队ID (主键)
  - `agent_id` → `agents.id` (外键，所属代理商)
  - `parent_team_id` → `teams.id` (外键，上级团队，自关联)
  - `leader_id` → `salespeople.id` (外键，团队长)
  - `level` - 团队层级
  - `status` - 状态 (active/inactive/dissolved)

### 6. 招募管理体系

#### 6.1 招募申请
- **recruitment_applications** (招募申请表)
  - `id` - 申请ID (主键)
  - `assigned_agent_id` → `agents.id` (外键，分配的代理商)
  - `assigned_team_id` → `teams.id` (外键，分配的团队)
  - `auditor_id` → `admin_users.id` (外键，审核人)
  - `application_type` - 申请类型 (self/referral/platform)
  - `status` - 状态 (pending/approved/rejected/assigned)

#### 6.2 招募渠道
- **recruitment_channels** (招募渠道表)
  - `id` - 渠道ID (主键)
  - `type` - 渠道类型 (online/offline/referral/other)
  - `status` - 状态 (active/inactive)

### 7. 商家用户管理体系

#### 7.1 商家用户
- **merchant_users** (商家用户表)
  - `id` - 商家用户ID (主键)
  - `username`, `password` - 登录凭证
  - `status` - 状态 (active/inactive/suspended)

#### 7.2 商家用户商铺关联
- **merchant_user_stores** (商家用户商铺关联表)
  - `merchant_user_id` → `merchant_users.id` (外键)
  - `store_id` → `stores.id` (外键)
  - `role` - 角色 (owner:所有者/manager:管理员/operator:操作员)
  - `status` - 状态 (active/inactive)

### 8. 统计分析体系

#### 8.1 推广统计
- **promotion_statistics** (推广统计表)
  - `store_id` → `stores.id` (外键)
  - `platform_id` → `media_platforms.id` (外键)
  - `module_type` - 模块类型
  - `stat_date` - 统计日期
  - 统计指标：view_count, click_count, share_count等

#### 8.2 推广数据统计
- **promotion_data_stats** (推广数据统计表)
  - `entity_type` - 实体类型 (store/salesperson/material/platform)
  - `entity_id` - 实体ID
  - `stat_date` - 统计日期
  - 统计指标：views, clicks, conversions, revenue, cost

#### 8.3 用户行为日志
- **user_behavior_logs** (用户行为日志表)
  - `store_id` → `stores.id` (外键)
  - `platform_id` → `media_platforms.id` (外键)
  - `nfc_chip_id` - NFC芯片ID
  - `action` - 用户行为 (view/click/share/download)
  - `session_id` - 会话ID

### 9. 系统基础表

#### 9.1 地区管理
- **areas** (区域表)
  - `id` - 区域ID (主键)
  - `parent_id` - 上级区域ID (自关联)
  - `level` - 层级 (1:省份/2:城市/3:区县)
  - `name` - 区域名称
  - `code` - 区域代码

#### 9.2 系统配置
- **system_configs** (系统配置表)
  - `key` - 配置键 (唯一)
  - `name` - 配置名称
  - `value` - 配置值
  - `type` - 配置类型 (string/number/boolean/json/file)
  - `group` - 配置分组

#### 9.3 系统通知
- **system_notifications** (系统通知表)
  - `target_type` - 目标类型 (all/store/salesperson/admin)
  - `target_id` - 目标ID
  - `type` - 通知类型 (info/warning/error/success)
  - `priority` - 优先级 (low/normal/high/urgent)

#### 9.4 操作日志
- **operation_logs** (操作日志表)
  - `user_id` - 操作用户ID
  - `user_type` - 用户类型
  - `module` - 操作模块
  - `action` - 操作动作
  - `target_type` - 操作对象类型
  - `target_id` - 操作对象ID

### 10. Laravel框架表

#### 10.1 Laravel基础表
- **users** (Laravel用户表) - 前端用户
- **password_reset_tokens** (密码重置令牌表)
- **personal_access_tokens** (个人访问令牌表)
- **migrations** (迁移记录表)

## 数据权限设计

### 权限层级控制
1. **超级管理员/平台管理员** - 查看所有数据
2. **一级代理商** - 查看自己及下级二级代理商数据
3. **二级代理商** - 只查看自己的数据
4. **业务员** - 根据所属代理商和团队限制数据范围

### 关键外键关系总结
```
admin_users.agent_id → agents.id (用户归属代理商)
agents.parent_agent_id → agents.id (代理商层级)
stores.agent_id → agents.id (商铺归属代理商)
agent_regions.agent_id → agents.id (代理商区域配置)
salespeople.agent_id → agents.id (业务员归属代理商)
teams.agent_id → agents.id (团队归属代理商)
store_materials.store_id → stores.id (商铺素材关联)
merchant_user_stores.store_id → stores.id (商家用户商铺关联)
promotion_statistics.store_id → stores.id (推广统计)
user_behavior_logs.store_id → stores.id (用户行为日志)
```

### 数据流向
1. **用户登录** → admin_users → agents (确定用户身份和权限范围)
2. **权限验证** → admin_roles → admin_permissions (验证操作权限)
3. **数据查询** → 根据agent_id过滤相应数据范围
4. **业务操作** → 记录operation_logs (操作审计)
5. **统计分析** → promotion_statistics + user_behavior_logs (数据分析)

## 注意事项

1. **数据一致性**：所有关联表都应正确设置外键约束
2. **权限控制**：每个控制器方法都应检查用户权限和数据权限
3. **软删除**：部分表使用软删除机制，需要在查询时注意
4. **索引优化**：关键查询字段已建立索引，提升查询性能
5. **数据审计**：重要操作都记录在operation_logs中
6. **统计缓存**：统计数据可考虑缓存机制提升性能 