# pyp-Laravel-new Database Relationships Documentation

## Project Overview
Laravel + <PERSON><PERSON>-Admin NFC multimedia marketing platform backend project (Touch Marketing System), implementing comprehensive business chain management for agents, stores, materials, salespeople, etc.

## Core Database Relationship Diagram

### 1. Agent Management System

#### 1.1 Agent Hierarchy Relationships
- **agents** (Agents Table) - Core Table
  - `id` - Agent ID (Primary Key)
  - `parent_agent_id` - Parent Agent ID (Foreign Key, Self-Reference)
  - `type` - Agent Type (0:Super Admin, 1:Platform Admin, 2:Primary Agent, 3:Secondary Agent)
  - `level` - Agent Level (1-4)
  - `path` - Hierarchy Path (e.g., 1-3-5 represents hierarchy relationship)
  - `status` - Status (active/inactive/suspended)

#### 1.2 Agent Region Configuration
- **agent_regions** (Agent Region Configuration Table)
  - `agent_id` → `agents.id` (Foreign Key Association)
  - `area_id` - Area ID (Links to areas table)
  - `commission_rate` - Commission Rate
  - `max_stores` - Maximum Managed Stores

#### 1.3 Admin User Association
- **admin_users** (Admin Users Table) - Laravel-Admin Core Table
  - `agent_id` → `agents.id` (Foreign Key Association, establishes user-agent relationship)
  - `username`, `password` - Login Credentials
  - `name` - User Name
  - `status` - User Status

### 2. Permission Management System (Laravel-Admin RBAC)

#### 2.1 Permission Base Tables
- **admin_permissions** (Permissions Table)
  - Permission identifier format: `module.action` (e.g., agent.create, store.show)
  
- **admin_roles** (Roles Table)
  - Role definitions: Platform Admin, Primary Agent, Secondary Agent, etc.

#### 2.2 Permission Association Tables
- **admin_role_permissions** (Role-Permission Association)
  - `role_id` → `admin_roles.id`
  - `permission_id` → `admin_permissions.id`

- **admin_role_users** (User-Role Association)
  - `role_id` → `admin_roles.id`
  - `user_id` → `admin_users.id`

- **admin_user_permissions** (User Direct Permissions)
  - `user_id` → `admin_users.id`
  - `permission_id` → `admin_permissions.id`

#### 2.3 Menu Permissions
- **admin_menu** (Menu Table)
  - `parent_id` - Parent Menu ID (Self-Reference)
  - `title` - Menu Title
  - `uri` - Route Address
  - `permission` - Permission Identifier

- **admin_role_menu** (Role-Menu Association)
  - `role_id` → `admin_roles.id`
  - `menu_id` → `admin_menu.id`

### 3. Store Management System

#### 3.1 Store Basic Information
- **stores** (Stores Table) - Core Business Entity
  - `id` - Store ID (Primary Key)
  - `agent_id` → `agents.id` (Foreign Key, Belonging Agent)
  - `area_id` - Area ID (Links to areas table)
  - `nfc_chip_id` - NFC Chip ID (Unique Identifier)
  - `audit_status` - Audit Status (pending/approved/rejected)
  - `store_status` - Store Status (active/inactive/suspended)
  - `settlement_status` - Settlement Status (0:Unsettled/1:Pending/2:Settled)

#### 3.2 Store Material Association
- **store_materials** (Store Material Association Table)
  - `store_id` → `stores.id` (Foreign Key)
  - `material_id` → `materials.id` (Foreign Key)
  - `platform_id` → `media_platforms.id` (Foreign Key)
  - `module_type` - Module Type (video/review/social/ecommerce)
  - `is_active` - Is Active
  - `sort_order` - Sort Order

### 4. Material Management System

#### 4.1 Material Base Table
- **materials** (Materials Table)
  - `id` - Material ID (Primary Key)
  - `name` - Material Name
  - `type` - Material Type (image/video/audio/document)
  - `file_path` - File Path
  - `category` - Material Category
  - `status` - Status (active/inactive)

#### 4.2 Media Platform Configuration
- **media_platforms** (Media Platforms Table)
  - `id` - Platform ID (Primary Key)
  - `name` - Platform Name (TikTok/Kuaishou/Xiaohongshu, etc.)
  - `type` - Platform Type
  - `api_config` - API Configuration (JSON Format)
  - `status` - Platform Status

### 5. Salesperson Management System

#### 5.1 Salesperson Basic Information
- **salespeople** (Salespeople Table)
  - `id` - Salesperson ID (Primary Key)
  - `agent_id` → `agents.id` (Foreign Key, Belonging Agent)
  - `team_id` → `teams.id` (Foreign Key, Belonging Team, Optional)
  - `type` - Type (direct:Direct/team:Team)
  - `employee_type` - Employee Type (platform:Platform/agent:Agent)
  - `status` - Status (active/inactive/resigned)

#### 5.2 Team Management
- **teams** (Teams Table)
  - `id` - Team ID (Primary Key)
  - `agent_id` → `agents.id` (Foreign Key, Belonging Agent)
  - `parent_team_id` → `teams.id` (Foreign Key, Parent Team, Self-Reference)
  - `leader_id` → `salespeople.id` (Foreign Key, Team Leader)
  - `level` - Team Level
  - `status` - Status (active/inactive/dissolved)

### 6. Recruitment Management System

#### 6.1 Recruitment Applications
- **recruitment_applications** (Recruitment Applications Table)
  - `id` - Application ID (Primary Key)
  - `assigned_agent_id` → `agents.id` (Foreign Key, Assigned Agent)
  - `assigned_team_id` → `teams.id` (Foreign Key, Assigned Team)
  - `auditor_id` → `admin_users.id` (Foreign Key, Auditor)
  - `application_type` - Application Type (self/referral/platform)
  - `status` - Status (pending/approved/rejected/assigned)

#### 6.2 Recruitment Channels
- **recruitment_channels** (Recruitment Channels Table)
  - `id` - Channel ID (Primary Key)
  - `type` - Channel Type (online/offline/referral/other)
  - `status` - Status (active/inactive)

### 7. Merchant User Management System

#### 7.1 Merchant Users
- **merchant_users** (Merchant Users Table)
  - `id` - Merchant User ID (Primary Key)
  - `username`, `password` - Login Credentials
  - `status` - Status (active/inactive/suspended)

#### 7.2 Merchant User Store Association
- **merchant_user_stores** (Merchant User Store Association Table)
  - `merchant_user_id` → `merchant_users.id` (Foreign Key)
  - `store_id` → `stores.id` (Foreign Key)
  - `role` - Role (owner:Owner/manager:Manager/operator:Operator)
  - `status` - Status (active/inactive)

### 8. Statistics Analysis System

#### 8.1 Promotion Statistics
- **promotion_statistics** (Promotion Statistics Table)
  - `store_id` → `stores.id` (Foreign Key)
  - `platform_id` → `media_platforms.id` (Foreign Key)
  - `module_type` - Module Type
  - `stat_date` - Statistics Date
  - Statistics Metrics: view_count, click_count, share_count, etc.

#### 8.2 Promotion Data Statistics
- **promotion_data_stats** (Promotion Data Statistics Table)
  - `entity_type` - Entity Type (store/salesperson/material/platform)
  - `entity_id` - Entity ID
  - `stat_date` - Statistics Date
  - Statistics Metrics: views, clicks, conversions, revenue, cost

#### 8.3 User Behavior Logs
- **user_behavior_logs** (User Behavior Logs Table)
  - `store_id` → `stores.id` (Foreign Key)
  - `platform_id` → `media_platforms.id` (Foreign Key)
  - `nfc_chip_id` - NFC Chip ID
  - `action` - User Behavior (view/click/share/download)
  - `session_id` - Session ID

### 9. System Base Tables

#### 9.1 Area Management
- **areas** (Areas Table)
  - `id` - Area ID (Primary Key)
  - `parent_id` - Parent Area ID (Self-Reference)
  - `level` - Level (1:Province/2:City/3:District)
  - `name` - Area Name
  - `code` - Area Code

#### 9.2 System Configuration
- **system_configs** (System Configuration Table)
  - `key` - Configuration Key (Unique)
  - `name` - Configuration Name
  - `value` - Configuration Value
  - `type` - Configuration Type (string/number/boolean/json/file)
  - `group` - Configuration Group

#### 9.3 System Notifications
- **system_notifications** (System Notifications Table)
  - `target_type` - Target Type (all/store/salesperson/admin)
  - `target_id` - Target ID
  - `type` - Notification Type (info/warning/error/success)
  - `priority` - Priority (low/normal/high/urgent)

#### 9.4 Operation Logs
- **operation_logs** (Operation Logs Table)
  - `user_id` - Operating User ID
  - `user_type` - User Type
  - `module` - Operation Module
  - `action` - Operation Action
  - `target_type` - Operation Target Type
  - `target_id` - Operation Target ID

### 10. Laravel Framework Tables

#### 10.1 Laravel Base Tables
- **users** (Laravel Users Table) - Frontend Users
- **password_reset_tokens** (Password Reset Tokens Table)
- **personal_access_tokens** (Personal Access Tokens Table)
- **migrations** (Migration Records Table)

## Data Permission Design

### Permission Level Control
1. **Super Admin/Platform Admin** - View All Data
2. **Primary Agent** - View Own and Subordinate Secondary Agent Data
3. **Secondary Agent** - View Only Own Data
4. **Salesperson** - Data Range Limited by Belonging Agent and Team

### Key Foreign Key Relationships Summary
```
admin_users.agent_id → agents.id (User belongs to Agent)
agents.parent_agent_id → agents.id (Agent Hierarchy)
stores.agent_id → agents.id (Store belongs to Agent)
agent_regions.agent_id → agents.id (Agent Region Configuration)
salespeople.agent_id → agents.id (Salesperson belongs to Agent)
teams.agent_id → agents.id (Team belongs to Agent)
store_materials.store_id → stores.id (Store Material Association)
merchant_user_stores.store_id → stores.id (Merchant User Store Association)
promotion_statistics.store_id → stores.id (Promotion Statistics)
user_behavior_logs.store_id → stores.id (User Behavior Logs)
```

### Data Flow
1. **User Login** → admin_users → agents (Determine user identity and permission scope)
2. **Permission Verification** → admin_roles → admin_permissions (Verify operation permissions)
3. **Data Query** → Filter appropriate data range based on agent_id
4. **Business Operations** → Record operation_logs (Operation audit)
5. **Statistical Analysis** → promotion_statistics + user_behavior_logs (Data analysis)

## Important Notes

1. **Data Consistency**: All association tables should properly set foreign key constraints
2. **Permission Control**: Every controller method should check user permissions and data permissions
3. **Soft Deletes**: Some tables use soft delete mechanism, need attention during queries
4. **Index Optimization**: Key query fields have established indexes to improve query performance
5. **Data Audit**: Important operations are recorded in operation_logs
6. **Statistics Cache**: Statistical data can consider caching mechanisms to improve performance 