# 小红书API接口文档

## 概述

本文档说明了两种不同的API接口：

1. **本地Laravel接口** - 在您的项目中实现的认证接口
2. **小红书API代理接口** - 代理调用小红书外部API的接口

## 1. 本地Laravel接口

### 获取本地Access Token
- **接口地址**: `POST /api/auth/access-token`
- **完整URL**: `http://localhost/pyp-Laravel-new/public/api/auth/access-token`
- **说明**: 这是您项目内部的认证接口

### 请求示例
```json
{
    "appKey": "red.qzgiaXL14ncBwlBz",
    "appSecret": "cf2ef172726d1272c4ed661e1c059a45",
    "grant_type": "client_credentials"
}
```

## 2. 小红书API代理接口

### 方式一：通过代理获取小红书Token
- **接口地址**: `POST /api/xiaohongshu/access-token`
- **完整URL**: `http://localhost/pyp-Laravel-new/public/api/xiaohongshu/access-token`
- **说明**: 代理调用小红书API `https://edith.xiaohongshu.com/api/sns/v1/ext/access/token`

### 方式二：直接代理请求
- **接口地址**: `POST /api/xiaohongshu/proxy`
- **完整URL**: `http://localhost/pyp-Laravel-new/public/api/xiaohongshu/proxy`
- **说明**: 直接代理到小红书API，返回原始响应

### 测试连接
- **接口地址**: `GET /api/xiaohongshu/test`
- **完整URL**: `http://localhost/pyp-Laravel-new/public/api/xiaohongshu/test`
- **说明**: 测试与小红书API的连接状态

## Postman配置示例

### 调用小红书API代理

#### 请求配置
- **Method**: POST
- **URL**: `http://localhost/pyp-Laravel-new/public/api/xiaohongshu/access-token`
- **Headers**:
  ```
  Content-Type: application/json
  Accept: application/json
  ```

#### 请求Body (JSON)
```json
{
    "grant_type": "client_credentials"
}
```

#### 预期响应
```json
{
    "code": 200,
    "message": "获取小红书access_token成功",
    "data": {
        "access_token": "...",
        "token_type": "Bearer",
        "expires_in": 7200
    },
    "timestamp": 1640995200
}
```

## 实际的小红书API

- **外部URL**: `https://edith.xiaohongshu.com/api/sns/v1/ext/access/token`
- **说明**: 这是小红书的真实API地址，我们的代理接口会调用这个地址

## 使用场景

1. **开发测试**: 使用本地接口进行开发和测试
2. **生产环境**: 通过代理接口调用小红书API
3. **调试**: 使用测试接口检查连接状态

## 注意事项

1. 小红书API可能需要特殊的认证方式或IP白名单
2. 代理接口会自动处理缓存和错误重试
3. 所有请求都有详细的日志记录
4. 支持CORS跨域请求 