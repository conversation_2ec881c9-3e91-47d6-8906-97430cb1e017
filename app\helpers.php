<?php

if (!function_exists('can_access')) {
    /**
     * 检查当前用户是否有指定权限
     */
    function can_access(string $permission, $resource = null): bool
    {
        return auth('admin')->check() && auth('admin')->user()->can($permission, $resource);
    }
}

if (!function_exists('cannot_access')) {
    /**
     * 检查当前用户是否没有指定权限
     */
    function cannot_access(string $permission, $resource = null): bool
    {
        return !can_access($permission, $resource);
    }
}

if (!function_exists('user_type')) {
    /**
     * 获取当前用户类型
     */
    function user_type(): int
    {
        return \App\Services\PermissionService::getCurrentUserType();
    }
}

if (!function_exists('is_platform_admin')) {
    /**
     * 判断是否为平台管理员
     */
    function is_platform_admin(): bool
    {
        return \App\Services\PermissionService::isPlatformAdmin();
    }
}

if (!function_exists('is_primary_agent')) {
    /**
     * 判断是否为一级代理商
     */
    function is_primary_agent(): bool
    {
        return user_type() === \App\Services\PermissionService::USER_TYPE_PRIMARY_AGENT;
    }
}

if (!function_exists('is_secondary_agent')) {
    /**
     * 判断是否为二级代理商
     */
    function is_secondary_agent(): bool
    {
        return user_type() === \App\Services\PermissionService::USER_TYPE_SECONDARY_AGENT;
    }
}
