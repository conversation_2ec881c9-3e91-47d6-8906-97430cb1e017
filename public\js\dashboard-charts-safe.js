/**
 * 安全的仪表板图表管理器
 * 解决PJAX环境下的JavaScript转义问题
 * 通过data属性读取数据，避免字符串转义
 * 作者: lauJinyu
 * 日期: 2025-01-08
 */

(function() {
    'use strict';
    
    // 全局图表实例管理
    window.DashboardChartsSafe = {
        charts: {},
        
        // 清理所有图表实例
        cleanup: function() {
            for (var chartId in this.charts) {
                if (this.charts[chartId]) {
                    try {
                        this.charts[chartId].dispose();
                        console.log('✅ 图表实例已清理:', chartId);
                    } catch (e) {
                        console.warn('⚠️ 清理图表时出错:', chartId, e);
                    }
                    delete this.charts[chartId];
                }
            }
        },
        
        // 初始化收入趋势图表
        initRevenueChart: function(container) {
            var chartId = container.id;
            console.log('🔄 开始初始化收入图表:', chartId);
            
            // 检查ECharts是否可用
            if (typeof echarts === 'undefined') {
                container.innerHTML = '<div class="alert alert-warning text-center">ECharts库未加载</div>';
                return;
            }
            
            try {
                // 从data属性读取数据
                var labels = JSON.parse(container.getAttribute('data-labels') || '[]');
                var values = JSON.parse(container.getAttribute('data-values') || '[]');
                
                if (labels.length === 0 || values.length === 0) {
                    container.innerHTML = '<div class="alert alert-warning text-center">图表数据为空</div>';
                    return;
                }
                
                // 清理旧实例
                if (this.charts[chartId]) {
                    this.charts[chartId].dispose();
                }
                
                // 创建新实例
                var myChart = echarts.init(container);
                this.charts[chartId] = myChart;
                
                var option = {
                    title: {
                        text: '最近7天收入趋势',
                        left: 'center',
                        textStyle: { fontSize: 14, color: '#333' }
                    },
                    tooltip: {
                        trigger: 'axis',
                        formatter: function(params) {
                            return params[0].name + '<br/>收入: ￥' + params[0].value.toLocaleString();
                        }
                    },
                    grid: {
                        left: '3%', right: '4%', bottom: '3%', containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        boundaryGap: false,
                        data: labels
                    },
                    yAxis: {
                        type: 'value',
                        axisLabel: { formatter: '￥{value}' }
                    },
                    series: [{
                        name: '收入',
                        type: 'line',
                        smooth: true,
                        areaStyle: {
                            color: {
                                type: 'linear',
                                x: 0, y: 0, x2: 0, y2: 1,
                                colorStops: [
                                    { offset: 0, color: 'rgba(54, 162, 235, 0.3)' },
                                    { offset: 1, color: 'rgba(54, 162, 235, 0.1)' }
                                ]
                            }
                        },
                        lineStyle: { color: 'rgba(54, 162, 235, 1)', width: 2 },
                        data: values
                    }]
                };
                
                myChart.setOption(option);
                console.log('✅ 收入趋势图表初始化成功:', chartId);
                
            } catch (error) {
                console.error('❌ 收入图表初始化失败:', chartId, error);
                container.innerHTML = '<div class="alert alert-danger text-center">图表加载失败: ' + error.message + '</div>';
            }
        },
        
        // 初始化用户分布图表
        initUserChart: function(container) {
            var chartId = container.id;
            console.log('🔄 开始初始化用户分布图表:', chartId);
            
            // 检查ECharts是否可用
            if (typeof echarts === 'undefined') {
                container.innerHTML = '<div class="alert alert-warning text-center">ECharts库未加载</div>';
                return;
            }
            
            try {
                // 从data属性读取数据
                var pieData = JSON.parse(container.getAttribute('data-pie-data') || '[]');
                
                if (pieData.length === 0) {
                    container.innerHTML = '<div class="alert alert-warning text-center">图表数据为空</div>';
                    return;
                }
                
                // 清理旧实例
                if (this.charts[chartId]) {
                    this.charts[chartId].dispose();
                }
                
                // 创建新实例
                var myChart = echarts.init(container);
                this.charts[chartId] = myChart;
                
                var option = {
                    title: {
                        text: '用户类型分布',
                        left: 'center',
                        textStyle: { fontSize: 14, color: '#333' }
                    },
                    tooltip: {
                        trigger: 'item',
                        formatter: function(params) {
                            return params.seriesName + '<br/>' + 
                                   params.name + ': ' + params.value + ' (' + params.percent + '%)';
                        }
                    },
                    legend: {
                        orient: 'vertical',
                        left: 'left'
                    },
                    series: [{
                        name: '用户分布',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        center: ['50%', '60%'],
                        avoidLabelOverlap: false,
                        emphasis: {
                            label: { show: true, fontSize: '16', fontWeight: 'bold' }
                        },
                        data: pieData,
                        itemStyle: {
                            color: function(params) {
                                var colors = ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0'];
                                return colors[params.dataIndex % colors.length];
                            }
                        }
                    }]
                };
                
                myChart.setOption(option);
                console.log('✅ 用户分布图表初始化成功:', chartId);
                
            } catch (error) {
                console.error('❌ 用户分布图表初始化失败:', chartId, error);
                container.innerHTML = '<div class="alert alert-danger text-center">图表加载失败: ' + error.message + '</div>';
            }
        },
        
        // 初始化所有图表
        initAll: function() {
            console.log('🔄 开始初始化所有仪表板图表');
            
            // 延迟执行确保DOM准备就绪
            var self = this;
            setTimeout(function() {
                // 初始化收入图表
                var revenueCharts = document.querySelectorAll('.dashboard-revenue-chart');
                for (var i = 0; i < revenueCharts.length; i++) {
                    self.initRevenueChart(revenueCharts[i]);
                }
                
                // 初始化用户分布图表
                var userCharts = document.querySelectorAll('.dashboard-user-chart');
                for (var j = 0; j < userCharts.length; j++) {
                    self.initUserChart(userCharts[j]);
                }
            }, 100);
        },
        
        // 响应式调整
        resize: function() {
            for (var chartId in this.charts) {
                if (this.charts[chartId]) {
                    try {
                        this.charts[chartId].resize();
                    } catch (e) {
                        console.warn('⚠️ 图表resize失败:', chartId, e);
                    }
                }
            }
        }
    };
    
    // 窗口大小调整处理
    window.addEventListener('resize', function() {
        if (window.DashboardChartsSafe) {
            window.DashboardChartsSafe.resize();
        }
    });
    
    // 自动初始化机制
    function autoInit() {
        // 检查是否在仪表板页面
        var hasRevenueChart = document.querySelector('.dashboard-revenue-chart');
        var hasUserChart = document.querySelector('.dashboard-user-chart');
        
        if (hasRevenueChart || hasUserChart) {
            console.log('🔄 检测到仪表板页面，准备初始化图表');
            window.DashboardChartsSafe.initAll();
        }
    }
    
    // 多种触发方式确保初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', autoInit);
    } else {
        setTimeout(autoInit, 100);
    }
    
    // PJAX环境支持
    if (typeof $ !== 'undefined') {
        $(document).on('pjax:complete', function() {
            console.log('🔄 PJAX完成，重新初始化仪表板图表');
            // 清理旧图表
            window.DashboardChartsSafe.cleanup();
            // 重新初始化
            setTimeout(autoInit, 200);
        });
        
        $(document).on('pjax:end', function() {
            console.log('🔄 PJAX结束，确保图表初始化');
            setTimeout(autoInit, 400);
        });
    }
    
})(); 