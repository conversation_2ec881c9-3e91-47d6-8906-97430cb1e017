<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Encore\Admin\Auth\Database\Role;
use Encore\Admin\Auth\Database\Permission;

class InitializeRolePermissions extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'permission:init';

    /**
     * The console command description.
     */
    protected $description = '初始化角色权限系统，创建代理商相关的角色和权限';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('开始初始化角色权限系统...');

        // 创建角色
        $this->createRoles();

        // 创建权限
        $this->createPermissions();

        // 分配权限给角色
        $this->assignPermissionsToRoles();

        $this->info('角色权限系统初始化完成！');

        return Command::SUCCESS;
    }

    /**
     * 创建角色
     */
    private function createRoles()
    {
        $roles = [
            [
                'name' => '平台管理员',
                'slug' => 'platform_admin',
            ],
            [
                'name' => '一级代理商',
                'slug' => 'primary_agent',
            ],
            [
                'name' => '二级代理商',
                'slug' => 'secondary_agent',
            ],
        ];

        foreach ($roles as $roleData) {
            $role = Role::firstOrCreate(
                ['slug' => $roleData['slug']],
                $roleData
            );
            $this->info("角色创建/更新: {$role->name}");
        }
    }

    /**
     * 创建权限
     */
    private function createPermissions()
    {
        $permissions = [
            // 代理商管理权限
            [
                'slug' => 'agent.list',
                'name' => '代理商列表',
                'http_method' => 'GET',
                'http_path' => '/agents*',
            ],
            [
                'slug' => 'agent.create',
                'name' => '创建代理商',
                'http_method' => 'GET,POST',
                'http_path' => '/agents/create*',
            ],
            [
                'slug' => 'agent.edit',
                'name' => '编辑代理商',
                'http_method' => 'GET,PUT',
                'http_path' => '/agents/*/edit*',
            ],
            [
                'slug' => 'agent.delete',
                'name' => '删除代理商',
                'http_method' => 'DELETE',
                'http_path' => '/agents/*',
            ],
            [
                'slug' => 'agent.hierarchy',
                'name' => '代理商层级管理',
                'http_method' => 'GET,POST,PUT',
                'http_path' => '/agents/hierarchy*',
            ],

            // 商铺管理权限
            [
                'slug' => 'store.list',
                'name' => '商铺列表',
                'http_method' => 'GET',
                'http_path' => '/stores*',
            ],
            [
                'slug' => 'store.create',
                'name' => '创建商铺',
                'http_method' => 'GET,POST',
                'http_path' => '/stores/create*',
            ],
            [
                'slug' => 'store.edit',
                'name' => '编辑商铺',
                'http_method' => 'GET,PUT',
                'http_path' => '/stores/*/edit*',
            ],
            [
                'slug' => 'store.delete',
                'name' => '删除商铺',
                'http_method' => 'DELETE',
                'http_path' => '/stores/*',
            ],

            // 业务员管理权限
            [
                'slug' => 'salesperson.list',
                'name' => '业务员列表',
                'http_method' => 'GET',
                'http_path' => '/salespersons*',
            ],
            [
                'slug' => 'salesperson.create',
                'name' => '创建业务员',
                'http_method' => 'GET,POST',
                'http_path' => '/salespersons/create*',
            ],
            [
                'slug' => 'salesperson.edit',
                'name' => '编辑业务员',
                'http_method' => 'GET,PUT',
                'http_path' => '/salespersons/*/edit*',
            ],

            // 素材管理权限
            [
                'slug' => 'material.list',
                'name' => '素材列表',
                'http_method' => 'GET',
                'http_path' => '/materials*',
            ],
            [
                'slug' => 'material.create',
                'name' => '上传素材',
                'http_method' => 'GET,POST',
                'http_path' => '/materials/create*',
            ],
            [
                'slug' => 'material.edit',
                'name' => '编辑素材',
                'http_method' => 'GET,PUT',
                'http_path' => '/materials/*/edit*',
            ],

            // 统计查看权限
            [
                'slug' => 'statistics.view',
                'name' => '查看统计数据',
                'http_method' => 'GET',
                'http_path' => '/statistics*',
            ],
            [
                'slug' => 'statistics.export',
                'name' => '导出统计数据',
                'http_method' => 'GET,POST',
                'http_path' => '/statistics/export*',
            ],

            // 系统管理权限
            [
                'slug' => 'system.setting',
                'name' => '系统设置',
                'http_method' => 'GET,POST,PUT',
                'http_path' => '/system-settings*',
            ],
            [
                'slug' => 'permission.manage',
                'name' => '权限管理',
                'http_method' => 'GET,POST,PUT,DELETE',
                'http_path' => '/permissions*',
            ],
        ];

        foreach ($permissions as $permissionData) {
            $permission = Permission::firstOrCreate(
                ['slug' => $permissionData['slug']],
                $permissionData
            );
            $this->info("权限创建/更新: {$permission->name}");
        }
    }

    /**
     * 分配权限给角色
     */
    private function assignPermissionsToRoles()
    {
        // 平台管理员 - 拥有所有权限
        $platformAdmin = Role::where('slug', 'platform_admin')->first();
        if ($platformAdmin) {
            $allPermissions = Permission::all();
            $platformAdmin->permissions()->sync($allPermissions->pluck('id'));
            $this->info("为平台管理员分配了 {$allPermissions->count()} 个权限");
        }

        // 一级代理商 - 拥有代理商、商铺、业务员、素材、统计权限
        $primaryAgent = Role::where('slug', 'primary_agent')->first();
        if ($primaryAgent) {
            $permissions = Permission::whereIn('slug', [
                'agent.list',
                'agent.create',
                'agent.edit',
                'agent.hierarchy',
                'store.list',
                'store.create',
                'store.edit',
                'salesperson.list',
                'salesperson.create',
                'salesperson.edit',
                'material.list',
                'material.create',
                'statistics.view',
                'statistics.export'
            ])->get();
            $primaryAgent->permissions()->sync($permissions->pluck('id'));
            $this->info("为一级代理商分配了 {$permissions->count()} 个权限");
        }

        // 二级代理商 - 拥有商铺、业务员、素材、统计权限（受限）
        $secondaryAgent = Role::where('slug', 'secondary_agent')->first();
        if ($secondaryAgent) {
            $permissions = Permission::whereIn('slug', [
                'agent.list', // 只能看到自己
                'store.list',
                'store.create',
                'store.edit',
                'salesperson.list',
                'salesperson.create',
                'salesperson.edit',
                'material.list',
                'material.create',
                'statistics.view'
            ])->get();
            $secondaryAgent->permissions()->sync($permissions->pluck('id'));
            $this->info("为二级代理商分配了 {$permissions->count()} 个权限");
        }
    }
}
