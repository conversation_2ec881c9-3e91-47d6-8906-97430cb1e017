/**
 * 代理商层级管理JavaScript功能
 * 
 * 功能包括：
 * - 层级结构树形图显示
 * - 表格筛选和排序
 * - 统计数据实时更新
 * - 层级详情模态框
 */

var HierarchyManagement = {
    // 配置选项
    options: {
        hierarchyData: [],
        treeContainer: '#hierarchy-tree',
        tableContainer: '#hierarchy-table'
    },

    // 初始化
    init: function (options) {
        this.options = $.extend(this.options, options);
        this.initTree();
        this.initTable();
        this.bindEvents();
    },

    // 初始化层级结构树
    initTree: function () {
        if (typeof echarts === 'undefined') {
            console.warn('ECharts库未加载，跳过树形图初始化');
            return;
        }

        var container = document.querySelector(this.options.treeContainer);
        if (!container) {
            console.warn('树形图容器不存在');
            return;
        }

        var chart = echarts.init(container);
        var treeData = this.buildTreeData();

        var option = {
            title: {
                text: '代理商层级结构图',
                left: 'center',
                top: 10
            },
            tooltip: {
                trigger: 'item',
                formatter: function (params) {
                    var data = params.data;
                    return [
                        '<strong>' + data.name + '</strong>',
                        '等级: ' + data.level_label,
                        '下级代理商: ' + data.current_sub_agents + '/' + data.max_sub_agents,
                        '直推商铺: ' + data.current_direct_stores + '/' + data.max_direct_stores,
                        '状态: ' + data.status_label
                    ].join('<br>');
                }
            },
            series: [{
                type: 'tree',
                data: [treeData],
                top: '5%',
                left: '10%',
                bottom: '5%',
                right: '20%',
                symbolSize: 12,
                label: {
                    position: 'left',
                    verticalAlign: 'middle',
                    align: 'right',
                    fontSize: 12,
                    formatter: function (params) {
                        return params.data.name + '\n' + params.data.level_label;
                    }
                },
                leaves: {
                    label: {
                        position: 'right',
                        verticalAlign: 'middle',
                        align: 'left'
                    }
                },
                expandAndCollapse: true,
                animationDuration: 550,
                animationDurationUpdate: 750,
                itemStyle: {
                    color: function (params) {
                        var level = params.data.level;
                        return level === 1 ? '#5cb85c' : '#5bc0de';
                    }
                }
            }]
        };

        chart.setOption(option);

        // 点击节点事件
        chart.on('click', function (params) {
            if (params.data.id) {
                HierarchyManagement.showDetail(params.data.id);
            }
        });

        // 响应式调整
        window.addEventListener('resize', function () {
            chart.resize();
        });
    },

    // 构建树形数据
    buildTreeData: function () {
        var data = this.options.hierarchyData;
        var tree = {
            name: '代理商层级结构',
            children: []
        };

        // 找到顶级代理商
        var topLevelAgents = data.filter(function (agent) {
            return !agent.parent_agent_id;
        });

        // 递归构建树形结构
        var buildChildren = function (parentId) {
            return data.filter(function (agent) {
                return agent.parent_agent_id === parentId;
            }).map(function (agent) {
                return {
                    id: agent.id,
                    name: agent.name,
                    level: agent.level,
                    level_label: agent.level === 1 ? '一级代理商' : '二级代理商',
                    current_sub_agents: agent.current_sub_agents || 0,
                    max_sub_agents: agent.max_sub_agents || 0,
                    current_direct_stores: agent.current_direct_stores || 0,
                    max_direct_stores: agent.max_direct_stores || 0,
                    status_label: agent.status === 'active' ? '正常' : '异常',
                    children: buildChildren(agent.id)
                };
            });
        };

        tree.children = topLevelAgents.map(function (agent) {
            return {
                id: agent.id,
                name: agent.name,
                level: agent.level,
                level_label: agent.level === 1 ? '一级代理商' : '二级代理商',
                current_sub_agents: agent.current_sub_agents || 0,
                max_sub_agents: agent.max_sub_agents || 0,
                current_direct_stores: agent.current_direct_stores || 0,
                max_direct_stores: agent.max_direct_stores || 0,
                status_label: agent.status === 'active' ? '正常' : '异常',
                children: buildChildren(agent.id)
            };
        });

        return tree;
    },

    // 初始化表格
    initTable: function () {
        var $table = $(this.options.tableContainer);
        if (!$table.length) {
            return;
        }

        // 如果有DataTable，初始化
        if (typeof $table.DataTable === 'function') {
            $table.DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Chinese.json'
                },
                pageLength: 25,
                order: [[1, 'asc']], // 按等级排序
                columnDefs: [
                    { orderable: false, targets: [6] } // 操作列不可排序
                ]
            });
        }
    },

    // 绑定事件
    bindEvents: function () {
        var self = this;

        // 刷新按钮
        $(document).on('click', '[onclick="refreshHierarchy()"]', function () {
            self.refreshData();
        });

        // 筛选事件
        window.filterByLevel = function (level) {
            self.filterTable('level', level);
        };

        window.filterByStatus = function (status) {
            self.filterTable('status', status);
        };

        // 更新统计数据
        window.updateCounts = function (agentId) {
            self.updateCounts(agentId);
        };
    },

    // 表格筛选
    filterTable: function (type, value) {
        var $table = $(this.options.tableContainer);
        var $rows = $table.find('tbody tr');

        if (value === 'all') {
            $rows.show();
        } else {
            $rows.each(function () {
                var $row = $(this);
                var dataValue = $row.data(type);

                if (type === 'level') {
                    if (dataValue == value) {
                        $row.show();
                    } else {
                        $row.hide();
                    }
                } else if (type === 'status') {
                    if (dataValue === value) {
                        $row.show();
                    } else {
                        $row.hide();
                    }
                }
            });
        }

        // 更新显示的行数
        this.updateTableInfo();
    },

    // 更新表格信息
    updateTableInfo: function () {
        var $table = $(this.options.tableContainer);
        var $visibleRows = $table.find('tbody tr:visible');
        var total = $table.find('tbody tr').length;
        var visible = $visibleRows.length;

        // 如果有分页信息区域，更新它
        var $info = $table.closest('.box').find('.dataTables_info');
        if ($info.length) {
            $info.text('显示 ' + visible + ' 条，共 ' + total + ' 条记录');
        }
    },

    // 显示详情
    showDetail: function (agentId) {
        var url = '/admin/agents/' + agentId + '/hierarchy-detail';
        window.location.href = url;
    },

    // 刷新数据
    refreshData: function () {
        // 显示加载提示
        if (typeof toastr !== 'undefined') {
            toastr.info('正在刷新数据...');
        }

        // 重新加载页面
        setTimeout(function () {
            location.reload();
        }, 500);
    },

    // 更新统计数据
    updateCounts: function (agentId) {
        var self = this;

        $.ajax({
            url: '/admin/agents/' + agentId + '/update-counts',
            type: 'POST',
            data: {
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            beforeSend: function () {
                if (typeof toastr !== 'undefined') {
                    toastr.info('正在更新统计数据...');
                }
            },
            success: function (response) {
                if (response.success) {
                    if (typeof toastr !== 'undefined') {
                        toastr.success('统计数据更新成功');
                    }

                    // 更新页面显示
                    self.updateDisplayCounts(agentId, response.data);
                } else {
                    if (typeof toastr !== 'undefined') {
                        toastr.error(response.message || '更新失败');
                    }
                }
            },
            error: function (xhr) {
                var message = '更新失败，请重试';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }

                if (typeof toastr !== 'undefined') {
                    toastr.error(message);
                }
            }
        });
    },

    // 更新页面显示的统计数据
    updateDisplayCounts: function (agentId, data) {
        // 更新统计卡片
        if (data.total_agents !== undefined) {
            $('#total-agents').text(data.total_agents);
        }
        if (data.primary_agents !== undefined) {
            $('#primary-agents').text(data.primary_agents);
        }
        if (data.secondary_agents !== undefined) {
            $('#secondary-agents').text(data.secondary_agents);
        }
        if (data.total_stores !== undefined) {
            $('#total-stores').text(data.total_stores);
        }

        // 更新表格中对应行的数据
        var $row = $(this.options.tableContainer).find('tr[data-agent-id="' + agentId + '"]');
        if ($row.length && data.agent) {
            var agent = data.agent;

            // 更新下级代理商进度条
            var subAgentsPercentage = agent.max_sub_agents > 0 ?
                (agent.current_sub_agents / agent.max_sub_agents) * 100 : 0;
            $row.find('.sub-agents-progress .progress-bar').css('width', subAgentsPercentage + '%');
            $row.find('.sub-agents-text').text(agent.current_sub_agents + '/' + agent.max_sub_agents);

            // 更新直推商铺进度条
            var storesPercentage = agent.max_direct_stores > 0 ?
                (agent.current_direct_stores / agent.max_direct_stores) * 100 : 0;
            $row.find('.direct-stores-progress .progress-bar').css('width', storesPercentage + '%');
            $row.find('.direct-stores-text').text(agent.current_direct_stores + '/' + agent.max_direct_stores);
        }
    }
};

// 全局函数
function refreshHierarchy() {
    HierarchyManagement.refreshData();
}

function filterByLevel(level) {
    HierarchyManagement.filterTable('level', level);
}

function filterByStatus(status) {
    HierarchyManagement.filterTable('status', status);
}

function updateCounts(agentId) {
    HierarchyManagement.updateCounts(agentId);
}

// 页面加载完成后自动初始化
$(document).ready(function () {
    // 如果页面有层级管理容器，则自动初始化
    if ($('#hierarchy-tree').length || $('#hierarchy-table').length) {
        // 等待其他脚本加载完成
        setTimeout(function () {
            if (typeof HierarchyManagement !== 'undefined') {
                console.log('层级管理功能已准备就绪');
            }
        }, 100);
    }
}); 