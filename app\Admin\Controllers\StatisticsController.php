<?php

namespace App\Admin\Controllers;

use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Layout\Content;
use Encore\Admin\Widgets\Box;
use Encore\Admin\Widgets\InfoBox;
use App\Models\Store;
use App\Models\Agent;
use App\Models\Salesperson;
use App\Models\PromotionData;
use App\Services\PermissionService;
use Illuminate\Http\Request;

class StatisticsController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = '数据统计中心';

    /**
     * 平台运营数据统计
     */
    public function platform(Content $content)
    {
        // 检查权限
        if (!PermissionService::hasPermission('statistics.view')) {
            abort(403, '您没有权限访问平台统计数据');
        }

        return $content
            ->title('平台运营数据统计')
            ->description('查看平台整体运营数据')
            ->row(function ($row) {
                // 基础数据统计 - 根据权限过滤数据
                $storeQuery = Store::query();
                $agentQuery = Agent::query();
                $salespersonQuery = Salesperson::query();

                // 根据用户角色过滤数据
                if (!PermissionService::isPlatformAdmin()) {
                    $accessibleAgentIds = PermissionService::getAccessibleAgentIds();
                    $storeQuery->whereIn('agent_id', $accessibleAgentIds);
                    $agentQuery->whereIn('id', $accessibleAgentIds);
                    $salespersonQuery->whereIn('agent_id', $accessibleAgentIds);
                }

                $row->column(3, new InfoBox('总商家数', 'users', 'aqua', '/admin/stores', $storeQuery->count()));
                $row->column(3, new InfoBox('活跃商家', 'shopping-cart', 'green', '/admin/stores', $storeQuery->where('store_status', 'active')->count()));
                $row->column(3, new InfoBox('代理商数', 'user-plus', 'yellow', '/admin/agents', $agentQuery->count()));
                $row->column(3, new InfoBox('业务员数', 'users', 'red', '/admin/salespersons', $salespersonQuery->count()));
            })
            ->row(function ($row) {
                // 推广数据统计图表
                $row->column(6, $this->promotionChart());
                $row->column(6, $this->revenueChart());
            })
            ->row(function ($row) {
                // 平台数据表格
                $row->column(12, $this->platformDataTable());
            });
    }

    /**
     * 代理商运营数据统计
     */
    public function agent(Content $content)
    {
        // 检查权限
        if (!PermissionService::hasPermission('statistics.view')) {
            abort(403, '您没有权限访问代理商统计数据');
        }

        return $content
            ->title('代理商运营数据统计')
            ->description('查看各代理商运营数据')
            ->row(function ($row) {
                $row->column(12, $this->agentDataTable());
            });
    }

    /**
     * 报表管理
     */
    public function reports(Content $content)
    {
        // 检查权限
        if (!PermissionService::hasPermission('statistics.view')) {
            abort(403, '您没有权限访问报表管理');
        }

        return $content
            ->title('报表管理')
            ->description('生成和管理各类运营报表')
            ->row(function ($row) {
                $row->column(12, $this->reportsTable());
            });
    }

    /**
     * 推广数据图表
     */
    protected function promotionChart()
    {
        return new Box('推广数据统计', view('admin.charts.promotion', [
            'data' => $this->getPromotionData()
        ]));
    }

    /**
     * 收入数据图表
     */
    protected function revenueChart()
    {
        return new Box('收入数据统计', view('admin.charts.revenue', [
            'data' => $this->getRevenueData()
        ]));
    }

    /**
     * 平台数据表格
     */
    protected function platformDataTable()
    {
        // 根据权限过滤数据
        $storeQuery = Store::with('agent')->latest();
        $agentQuery = Agent::withCount('stores')->latest();

        if (!PermissionService::isPlatformAdmin()) {
            $accessibleAgentIds = PermissionService::getAccessibleAgentIds();
            $storeQuery->whereIn('agent_id', $accessibleAgentIds);
            $agentQuery->whereIn('id', $accessibleAgentIds);
        }

        return new Box('平台详细数据', view('admin.tables.platform_data', [
            'stores' => $storeQuery->take(10)->get(),
            'agents' => $agentQuery->take(10)->get()
        ]));
    }

    /**
     * 代理商数据表格
     */
    protected function agentDataTable()
    {
        // 根据权限过滤数据
        $agentQuery = Agent::withCount(['stores', 'salespersons']);

        if (!PermissionService::isPlatformAdmin()) {
            $agentQuery->whereIn('id', PermissionService::getAccessibleAgentIds());
        }

        return new Box('代理商数据统计', view('admin.tables.agent_data', [
            'agents' => $agentQuery->get()
        ]));
    }

    /**
     * 报表表格
     */
    protected function reportsTable()
    {
        return new Box('报表列表', view('admin.tables.reports', [
            'reports' => $this->getReportsList()
        ]));
    }

    /**
     * 获取推广数据
     */
    protected function getPromotionData()
    {
        // 模拟数据，实际应从数据库获取
        return [
            'douyin' => [
                'videos' => 1250,
                'views' => 125000,
                'likes' => 8500
            ],
            'xiaohongshu' => [
                'posts' => 850,
                'views' => 95000,
                'likes' => 6200
            ],
            'dianping' => [
                'reviews' => 650,
                'checkins' => 1200
            ],
            'wechat' => [
                'friends_added' => 2300,
                'moments_shared' => 1800
            ]
        ];
    }

    /**
     * 获取收入数据
     */
    protected function getRevenueData()
    {
        // 模拟数据，实际应从数据库获取
        return [
            'total_revenue' => 125000.00,
            'commission_paid' => 25000.00,
            'platform_profit' => 100000.00,
            'monthly_growth' => 15.5
        ];
    }

    /**
     * 获取报表列表
     */
    protected function getReportsList()
    {
        // 模拟数据，实际应从数据库获取
        return [
            [
                'name' => '月度运营报表',
                'type' => 'monthly',
                'generated_at' => now()->subDays(1),
                'file_path' => '/reports/monthly_2024_01.xlsx'
            ],
            [
                'name' => '代理商业绩报表',
                'type' => 'agent_performance',
                'generated_at' => now()->subDays(3),
                'file_path' => '/reports/agent_performance_2024_01.xlsx'
            ],
            [
                'name' => '推广效果分析报表',
                'type' => 'promotion_analysis',
                'generated_at' => now()->subWeek(),
                'file_path' => '/reports/promotion_analysis_2024_w4.xlsx'
            ]
        ];
    }

    /**
     * 导出数据
     */
    public function export(Request $request)
    {
        // 检查权限
        if (!PermissionService::hasPermission('statistics.export')) {
            abort(403, '您没有权限导出统计数据');
        }

        $type = $request->input('type', 'platform');
        $start_date = $request->input('start_date', now()->subMonth()->format('Y-m-d'));
        $end_date = $request->input('end_date', now()->format('Y-m-d'));

        // 根据类型和时间范围导出数据
        switch ($type) {
            case 'platform':
                return $this->exportPlatformData($start_date, $end_date);
            case 'agent':
                return $this->exportAgentData($start_date, $end_date);
            case 'promotion':
                return $this->exportPromotionData($start_date, $end_date);
            default:
                return response()->json(['error' => '不支持的导出类型'], 400);
        }
    }

    /**
     * 导出平台数据
     */
    protected function exportPlatformData($start_date, $end_date)
    {
        // 实现平台数据导出逻辑
        return response()->download(storage_path('app/exports/platform_data.xlsx'));
    }

    /**
     * 导出代理商数据
     */
    protected function exportAgentData($start_date, $end_date)
    {
        // 实现代理商数据导出逻辑
        return response()->download(storage_path('app/exports/agent_data.xlsx'));
    }

    /**
     * 导出推广数据
     */
    protected function exportPromotionData($start_date, $end_date)
    {
        // 实现推广数据导出逻辑
        return response()->download(storage_path('app/exports/promotion_data.xlsx'));
    }
}
