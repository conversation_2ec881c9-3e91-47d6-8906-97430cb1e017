# 管理平台-商家管理需求文档

## 1. 业务背景与关联关系

### 1.1 核心关联关系
- **商铺 ↔ 代理商**：每个商铺必须归属于一个代理商（`stores.agent_id`）
- **商铺 ↔ 业务员**：商铺可以由业务员管理，业务员归属于代理商或平台
- **商铺 ↔ 商家用户**：商铺可以绑定多个商家用户，支持不同角色（owner/manager/operator）
- **商铺 ↔ 区域**：商铺必须关联具体区域（省市区三级）
- **商铺 ↔ NFC芯片**：每个商铺绑定唯一的NFC芯片ID

### 1.2 商家端功能对应关系
根据商家端文档，管理端需要支持以下配置：

**商家端功能模块** → **管理端配置需求**
- 基本信息配置 → 商铺基础信息管理
- 发视频配置 → 推广权限配置、素材关联
- 点评打卡配置 → 平台权限配置
- 微信营销配置 → 营销权限管理
- 团购配置 → 团购平台权限
- 抽奖配置 → 活动权限管理
- WiFi配置 → WiFi信息管理
- 素材库管理 → 素材权限分配
- 报表统计 → 数据统计权限

## 2. 数据表结构分析

### 2.1 核心表：`stores`
```sql
-- 基础信息字段
name, logo_url, banner_url, description, contact_person, phone, email, address

-- 地理位置字段  
latitude, longitude, business_hours, area_id, province_id, city_id, district_id

-- 关联关系字段
agent_id, salesperson_id, nfc_chip_id

-- 业务配置字段
business_type, category, status, is_active, verified_at

-- 权限配置字段
can_publish_video, can_use_review, can_use_wechat_marketing, 
can_use_group_buying, can_use_lottery, can_access_materials

-- 时间字段
created_at, updated_at, deleted_at
```

### 2.2 关联表结构
- **`merchant_users`**：商家用户表，关联商铺和用户
- **`agents`**：代理商表
- **`salespersons`**：业务员表
- **`areas`**：区域表（省市区）
- **`materials`**：素材表
- **`commission_settlements`**：佣金结算表

## 3. 功能模块设计

### 3.1 商铺列表页面（/admin/merchant/list）

#### 3.1.1 列表展示字段
- 商铺ID
- 商铺名称
- 商铺类型
- 联系人
- 联系电话
- 归属代理商
- 归属业务员
- 所在区域
- 状态（启用/禁用）
- 认证状态
- 创建时间
- 操作按钮

#### 3.1.2 筛选条件
- 商铺名称搜索
- 代理商筛选
- 业务员筛选
- 区域筛选（省市区三级联动）
- 商铺类型筛选
- 状态筛选
- 认证状态筛选
- 创建时间范围

#### 3.1.3 批量操作
- 批量启用/禁用
- 批量分配代理商
- 批量分配业务员
- 批量删除
- 批量导出

### 3.2 商铺详情页面（/admin/merchant/detail）

#### 3.2.1 基本信息
- 商铺基础信息展示
- 商铺图片展示（logo、banner）
- 地理位置信息
- 营业时间
- 联系方式

#### 3.2.2 关联信息
- 归属代理商信息
- 归属业务员信息
- 绑定的商家用户列表
- NFC芯片信息

#### 3.2.3 业务数据
- 素材使用统计
- 推广数据统计
- 佣金结算记录
- 操作日志

### 3.3 商铺配置页面（/admin/merchant/config）

#### 3.3.1 基础配置
- 商铺基本信息编辑
- 营业时间设置
- 联系方式管理
- 地理位置设置

#### 3.3.2 权限配置
- 发视频权限
- 点评打卡权限
- 微信营销权限
- 团购权限
- 抽奖权限
- 素材库访问权限

#### 3.3.3 关联配置
- 代理商分配
- 业务员分配
- 商家用户绑定
- NFC芯片绑定

### 3.4 数据统计页面（/admin/merchant/statistics）

#### 3.4.1 总体统计
- 商铺总数
- 活跃商铺数
- 认证商铺数
- 新增商铺趋势

#### 3.4.2 分类统计
- 按代理商统计
- 按业务员统计
- 按区域统计
- 按商铺类型统计

#### 3.4.3 业务统计
- 素材使用统计
- 推广数据统计
- 佣金产生统计
- 用户活跃度统计

### 3.5 批量操作页面（/admin/merchant/batch）

#### 3.5.1 批量导入
- Excel模板下载
- 批量导入商铺信息
- 导入结果反馈
- 错误数据处理

#### 3.5.2 批量编辑
- 批量修改商铺信息
- 批量权限设置
- 批量关联设置

#### 3.5.3 批量导出
- 按条件导出商铺数据
- 导出格式选择
- 导出进度跟踪

## 4. 技术实现规范

### 4.1 控制器设计
```php
class MerchantController extends AdminController
{
    public function index(Content $content)      // 商铺列表
    public function show($id, Content $content)  // 商铺详情
    public function create(Content $content)     // 创建商铺
    public function store(Request $request)      // 保存商铺
    public function edit($id, Content $content)  // 编辑商铺
    public function update($id, Request $request) // 更新商铺
    public function destroy($id)                 // 删除商铺
    
    // 自定义方法
    public function config($id, Content $content)    // 商铺配置
    public function statistics(Content $content)     // 数据统计
    public function batch(Content $content)          // 批量操作
    public function batchUpdate(Request $request)    // 批量更新
    public function export(Request $request)         // 导出数据
    public function import(Request $request)         // 导入数据
}
```

### 4.2 API接口设计
```php
class MerchantApiController extends Controller
{
    public function list(Request $request)           // 商铺列表API
    public function detail($id)                      // 商铺详情API
    public function statistics(Request $request)     // 统计数据API
    public function agents()                         // 代理商列表API
    public function salespersons($agentId = null)    // 业务员列表API
    public function areas()                          // 区域列表API
    public function materials($storeId)              // 素材列表API
}
```

### 4.3 数据验证规则
```php
// 商铺创建验证
$rules = [
    'name' => 'required|string|max:255',
    'contact_person' => 'required|string|max:100',
    'phone' => 'required|string|max:20',
    'email' => 'nullable|email|max:255',
    'address' => 'required|string|max:500',
    'agent_id' => 'required|exists:agents,id',
    'area_id' => 'required|exists:areas,id',
    'business_type' => 'required|string|max:100',
    'category' => 'required|string|max:100',
];
```

### 4.4 权限控制
```php
// 权限定义
'merchant.list'       => '商铺列表查看',
'merchant.create'     => '商铺创建',
'merchant.edit'       => '商铺编辑',
'merchant.delete'     => '商铺删除',
'merchant.config'     => '商铺配置',
'merchant.statistics' => '商铺统计',
'merchant.batch'      => '批量操作',
'merchant.export'     => '数据导出',
'merchant.import'     => '数据导入',
```

## 5. 前端界面设计

### 5.1 列表页面布局
- 页面标题：商铺管理
- 筛选区域：折叠式筛选条件
- 操作按钮：新增、批量操作、导出
- 数据表格：支持排序、分页
- 操作列：查看、编辑、配置、删除

### 5.2 详情页面布局
- 面包屑导航
- 商铺基本信息卡片
- 关联信息卡片
- 业务数据卡片
- 操作按钮：编辑、配置、返回

### 5.3 配置页面布局
- 分步骤表单设计
- 基础信息配置
- 权限配置
- 关联配置
- 保存和预览功能

### 5.4 统计页面布局
- 数据概览卡片
- 图表展示区域
- 筛选条件面板
- 数据导出功能

## 6. 数据库优化建议

### 6.1 索引优化
```sql
-- 商铺表索引
CREATE INDEX idx_stores_agent_id ON stores(agent_id);
CREATE INDEX idx_stores_salesperson_id ON stores(salesperson_id);
CREATE INDEX idx_stores_area_id ON stores(area_id);
CREATE INDEX idx_stores_status ON stores(status);
CREATE INDEX idx_stores_created_at ON stores(created_at);
```

### 6.2 查询优化
- 使用Eloquent关联查询减少N+1问题
- 列表查询使用分页和索引
- 统计查询使用缓存机制
- 大数据量导出使用队列处理

## 7. 安全考虑

### 7.1 数据安全
- 商铺信息访问权限控制
- 敏感信息脱敏处理
- 操作日志记录
- 数据备份策略

### 7.2 接口安全
- API接口鉴权
- 请求频率限制
- 输入数据验证
- SQL注入防护

## 8. 测试方案

### 8.1 功能测试
- 商铺CRUD操作测试
- 权限配置测试
- 批量操作测试
- 数据统计测试

### 8.2 性能测试
- 大数据量列表加载测试
- 批量操作性能测试
- 导出功能性能测试
- 并发访问测试

## 9. 部署和维护

### 9.1 部署要求
- PHP 8.0+
- MySQL 5.7+
- Redis缓存
- 队列服务

### 9.2 维护建议
- 定期数据备份
- 性能监控
- 日志分析
- 用户反馈收集

## 10. 开发计划

### 10.1 第一阶段（基础功能）
- 商铺列表和详情页面
- 基础CRUD操作
- 权限控制

### 10.2 第二阶段（高级功能）
- 商铺配置页面
- 数据统计功能
- 批量操作

### 10.3 第三阶段（优化完善）
- 性能优化
- 用户体验优化
- 功能完善

## 11. 风险评估

### 11.1 技术风险
- 大数据量处理性能问题
- 复杂关联查询优化
- 前端交互复杂度

### 11.2 业务风险
- 权限设计复杂度
- 数据一致性保证
- 用户操作习惯适应

### 11.3 风险缓解
- 分步骤开发和测试
- 充分的用户培训
- 完善的错误处理机制 