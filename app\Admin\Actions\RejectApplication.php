<?php

namespace App\Admin\Actions;

use Encore\Admin\Actions\RowAction;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use App\Models\Recruitment;

/**
 * 拒绝招募申请操作
 * 
 * <AUTHOR>
 */
class RejectApplication extends RowAction
{
    public $name = '拒绝';

    /**
     * 处理拒绝操作
     *
     * @param Model $model
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function handle(Model $model, Request $request)
    {
        try {
            /** @var Recruitment $model */
            if ($model->status !== 'pending') {
                return $this->response()->error('该申请已处理，无法重复操作');
            }

            $remark = $request->get('remark');
            if (empty($remark)) {
                return $this->response()->error('请填写拒绝原因');
            }

            // 拒绝申请
            $model->status = 'rejected';
            $model->audit_time = now();
            $model->audit_remark = $remark;
            $model->save();

            return $this->response()->success('申请已拒绝')->refresh();
        } catch (\Exception $e) {
            return $this->response()->error('拒绝失败: ' . $e->getMessage());
        }
    }

    /**
     * 表单字段
     *
     * @return array
     */
    public function form()
    {
        $this->textarea('remark', '拒绝原因')->required();
    }

    /**
     * 确认对话框
     *
     * @return string
     */
    public function confirm()
    {
        return '确定要拒绝该申请吗？';
    }

    /**
     * 显示条件
     *
     * @param Model $model
     * @return bool
     */
    public function display($model)
    {
        return $model->status === 'pending';
    }

    /**
     * 按钮样式
     *
     * @return string
     */
    public function style()
    {
        return 'btn btn-sm btn-danger';
    }
}