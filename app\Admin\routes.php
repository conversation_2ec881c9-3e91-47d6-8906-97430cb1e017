<?php

use Illuminate\Routing\Router;
use Encore\Admin\Facades\Admin;
use Illuminate\Support\Facades\Route;
use App\Admin\Controllers\AgentController;
use App\Admin\Controllers\AgentRegionController;
use App\Admin\Controllers\AgentCommissionController;
use App\Admin\Controllers\AgentStatisticsController;
use App\Admin\Controllers\MerchantController;
use App\Admin\Controllers\MediaConfigController;
use App\Admin\Controllers\MaterialController;
use App\Admin\Controllers\SalespersonController;
use App\Admin\Controllers\RecruitmentController;
use App\Admin\Controllers\TeamController;
use App\Admin\Controllers\AreaController;
use App\Admin\Controllers\CommissionSettlementController;
use App\Admin\Controllers\StoreController;

Admin::routes();

Route::group([
    'prefix'        => config('admin.route.prefix'),
    'namespace'     => config('admin.route.namespace'),
    'middleware'    => config('admin.route.middleware'),
    'as'            => config('admin.route.prefix') . '.',
], function (Router $router) {

    // 仪表板
    $router->get('/', 'DashboardController@index')->name('dashboard');
    $router->get('/dashboard', 'DashboardController@index');
    $router->get('/dashboard/real-time-data', 'DashboardController@getRealTimeData');
    $router->get('/dashboard/export', 'DashboardController@exportData');

    // 💡 仪表板API接口（支持外部调用）
    $router->group(['prefix' => 'dashboard/api'], function (Router $router) {
        $router->get('charts', 'DashboardController@apiGetCharts')->name('dashboard.api.charts');
        $router->get('statistics', 'DashboardController@apiGetStatistics')->name('dashboard.api.statistics');
    });

    // 代理商管理
    $router->get('agents', 'AgentController@index');
    $router->get('agents/create', 'AgentController@create');
    $router->post('agents', 'AgentController@store');
    $router->get('agents/{agent}/edit', 'AgentController@edit');
    $router->put('agents/{agent}', 'AgentController@update');
    $router->delete('agents/{agent}', 'AgentController@destroy');
    $router->get('agents/{agent}', 'AgentController@show');

    // 🆕 代理商层级管理
    $router->get('agents/hierarchy', 'AgentController@hierarchy')->name('agents.hierarchy');
    $router->get('agents/{id}/hierarchy-detail', 'AgentController@hierarchyDetail')->name('agents.hierarchy-detail');
    $router->get('agents/{id}/create-sub-agent', 'AgentController@createSubAgent')->name('agents.create-sub-agent');
    $router->post('agents/{id}/create-sub-agent', 'AgentController@storeSubAgent')->name('agents.store-sub-agent');
    $router->get('agents/{id}/edit-limits', 'AgentController@editLimits')->name('agents.edit-limits');
    $router->put('agents/{id}/edit-limits', 'AgentController@updateLimits')->name('agents.update-limits');
    $router->post('agents/{id}/update-counts', 'AgentController@updateCounts')->name('agents.update-counts');

    // 代理商区域配置
    $router->get('agent-regions', 'AgentRegionController@index');
    $router->get('agent-regions/create', 'AgentRegionController@create');
    $router->post('agent-regions', 'AgentRegionController@store');

    // 分步表单专用路由（必须在{id}路由之前）
    $router->post('agent-regions/save-step', 'AgentRegionController@saveStepData');
    $router->get('agent-regions/get-cities', 'AgentRegionController@getCities');
    $router->get('agent-regions/get-districts', 'AgentRegionController@getDistricts');

    // 步骤内容API端点（必须在{id}路由之前）
    $router->get('agent-regions/step-content/{step}', 'AgentRegionController@getStepContent');
    $router->get('agent-regions/step-content/{step}/{id}', 'AgentRegionController@getStepContent');

    // 参数化路由（必须放在最后）
    $router->get('agent-regions/{id}', 'AgentRegionController@show');
    $router->get('agent-regions/{id}/edit', 'AgentRegionController@edit');
    $router->put('agent-regions/{id}', 'AgentRegionController@update');
    $router->delete('agent-regions/{id}', 'AgentRegionController@destroy');

    // 💡 代理商区域配置API接口（支持外部调用）
    $router->group(['prefix' => 'agent-regions/api'], function (Router $router) {
        $router->get('list', 'AgentRegionController@apiList')->name('agent-regions.api.list');
        $router->get('areas', 'AgentRegionController@apiAreas')->name('agent-regions.api.areas');
    });

    // 代理商数据统计
    $router->get('agent-statistics', 'AgentStatisticsController@index');
    $router->get('agent-statistics/data', 'AgentStatisticsController@getData');
    $router->get('agent-statistics/export', 'AgentStatisticsController@export');

    // 商铺管理自定义路由（必须在resource路由之前）
    $router->get('merchant/list', 'MerchantController@index')->name('merchant.list');
    $router->get('merchant/detail/{id}', 'MerchantController@show')->name('merchant.detail');
    $router->get('merchant/config/{id}', 'MerchantController@config')->name('merchant.config');
    $router->get('merchant/statistics', 'MerchantController@statistics')->name('merchant.statistics');
    $router->get('merchant/batch', 'MerchantController@batch')->name('merchant.batch');

    // 商铺批量操作路由
    $router->post('merchant/batch-approve', 'MerchantController@batchApproveStores')->name('merchant.batch-approve');
    $router->post('merchant/batch-disable', 'MerchantController@batchDisableStores')->name('merchant.batch-disable');

    // 商铺管理API接口
    $router->group(['prefix' => 'merchant/api'], function (Router $router) {
        $router->get('list', 'MerchantController@apiList')->name('merchant.api.list');
        $router->get('detail/{id}', 'MerchantController@apiDetail')->name('merchant.api.detail');
        $router->get('statistics', 'MerchantController@apiStatistics')->name('merchant.api.statistics');
    });

    // 商铺管理资源路由
    $router->resource('merchants', MerchantController::class);

    // 媒体配置管理
    $router->resource('media-configs', MediaConfigController::class);

    // 素材库管理
    $router->resource('materials', MaterialController::class);

    // 业务员管理
    $router->resource('salespersons', SalespersonController::class);

    // 招募管理
    $router->resource('recruitments', RecruitmentController::class);
    $router->post('recruitments/{id}/approve', 'RecruitmentController@approve');
    $router->post('recruitments/{id}/reject', 'RecruitmentController@reject');

    // 团队管理
    $router->resource('teams', TeamController::class);

    // 数据统计中心
    $router->get('statistics', 'StatisticsController@index');
    $router->get('statistics/platform', 'StatisticsController@platformData');
    $router->get('statistics/agent', 'StatisticsController@agentData');
    $router->get('statistics/reports', 'StatisticsController@reports');
    $router->get('statistics/export', 'StatisticsController@export');

    // 权限管理
    $router->group(['prefix' => 'permissions'], function (Router $router) {
        // 角色管理
        $router->get('roles', 'PermissionController@roles');
        $router->get('roles/create', 'PermissionController@roleForm');
        $router->post('roles', 'PermissionController@roleForm');
        $router->get('roles/{id}/edit', 'PermissionController@roleForm');
        $router->put('roles/{id}', 'PermissionController@roleForm');
        $router->delete('roles/{id}', 'PermissionController@roleForm');

        // 权限管理
        $router->get('permissions', 'PermissionController@permissions');
        $router->get('permissions/create', 'PermissionController@permissionForm');
        $router->post('permissions', 'PermissionController@permissionForm');
        $router->get('permissions/{id}/edit', 'PermissionController@permissionForm');
        $router->put('permissions/{id}', 'PermissionController@permissionForm');
        $router->delete('permissions/{id}', 'PermissionController@permissionForm');

        // 用户管理
        $router->get('users', 'PermissionController@users');
        $router->get('users/create', 'PermissionController@userForm');
        $router->post('users', 'PermissionController@userForm');
        $router->get('users/{id}/edit', 'PermissionController@userForm');
        $router->put('users/{id}', 'PermissionController@userForm');
        $router->delete('users/{id}', 'PermissionController@userForm');

        // 操作日志
        $router->get('operation-logs', 'PermissionController@operationLogs');

        // 初始化权限
        $router->post('init-permissions', 'PermissionController@initPermissions');
    });

    // 系统设置
    $router->group(['prefix' => 'system-settings'], function (Router $router) {
        $router->get('/', 'SystemSettingController@index');
        $router->post('basic', 'SystemSettingController@saveBasicSettings');
        $router->post('payment', 'SystemSettingController@savePaymentSettings');
        $router->post('sms', 'SystemSettingController@saveSmsSettings');
        $router->post('email', 'SystemSettingController@saveEmailSettings');
        $router->post('storage', 'SystemSettingController@saveStorageSettings');
        $router->post('security', 'SystemSettingController@saveSecuritySettings');
        $router->post('api', 'SystemSettingController@saveApiSettings');
        $router->post('clear-cache', 'SystemSettingController@clearCache');
        $router->post('optimize', 'SystemSettingController@optimize');
    });

    // 地区API路由
    $router->get('api/areas', 'AreaController@areas');
    $router->get('api/area-by-code', 'AreaController@getAreaByCode');

    // 新增佣金结算路由
    $router->resource('agent-regions', AgentRegionController::class);

    // 结算单详情相关路由（必须放在resource路由之前）
    $router->get('commission-settlements/{id}/detail', 'CommissionSettlementController@detail')->name('commission-settlements.detail');
    $router->get('commission-settlements/{id}/api-detail', 'CommissionSettlementController@apiDetail')->name('commission-settlements.api-detail');
    $router->get('commission-settlements/{id}/stores', 'CommissionSettlementController@getStores')->name('commission-settlements.stores');
    $router->get('agent-commissions/{id}/detail', 'CommissionSettlementController@detail')->name('agent-commissions.detail');
    $router->get('agent-commissions/{id}/api-detail', 'CommissionSettlementController@apiDetail')->name('agent-commissions.api-detail');
    $router->get('agent-commissions/{id}/stores', 'CommissionSettlementController@getStores')->name('agent-commissions.stores');

    // 佣金结算（兼容旧菜单路由）
    $router->resource('commission-settlements', CommissionSettlementController::class);
    $router->resource('agent-commissions', CommissionSettlementController::class);

    // 手动生成结算单
    $router->post('agent-commissions/generate', 'CommissionSettlementController@generateManual')->name('agent-commissions.generate');

    // 商铺管理
    $router->resource('stores', StoreController::class);
});
