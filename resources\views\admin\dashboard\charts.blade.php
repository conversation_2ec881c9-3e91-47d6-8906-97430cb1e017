{{-- 收入趋势图表 --}}
<div class="box box-primary">
    <div class="box-header with-border">
        <h3 class="box-title">收入趋势</h3>
    </div>
    <div class="box-body">
        <div id="revenue_chart_safe" style="height: 300px; width: 100%;"></div>
    </div>
</div>

{{-- 用户分布图表 --}}
<div class="box box-info">
    <div class="box-header with-border">
        <h3 class="box-title">用户分布</h3>
    </div>
    <div class="box-body">
        <div id="user_chart_safe" style="height: 300px; width: 100%;"></div>
    </div>
</div>

<script type="text/javascript">
document.addEventListener('DOMContentLoaded', function() {
    // 延迟加载确保ECharts库已准备好
    setTimeout(function() {
        
        // 初始化收入趋势图表
        var revenueChartDom = document.getElementById('revenue_chart_safe');
        if (revenueChartDom && typeof echarts !== 'undefined') {
            var revenueChart = echarts.init(revenueChartDom);
            var revenueOption = {
                title: {
                    text: '最近7天收入趋势',
                    left: 'center',
                    textStyle: { fontSize: 14, color: '#333' }
                },
                tooltip: {
                    trigger: 'axis',
                    formatter: function(params) {
                        return params[0].name + '<br/>收入: ￥' + params[0].value.toLocaleString();
                    }
                },
                grid: {
                    left: '3%', right: '4%', bottom: '3%', containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: ['06-27', '06-28', '06-29', '06-30', '07-01', '07-02', '07-03']
                },
                yAxis: {
                    type: 'value',
                    axisLabel: { formatter: '￥{value}' }
                },
                series: [{
                    name: '收入',
                    type: 'line',
                    smooth: true,
                    areaStyle: {
                        color: {
                            type: 'linear',
                            x: 0, y: 0, x2: 0, y2: 1,
                            colorStops: [
                                { offset: 0, color: 'rgba(54, 162, 235, 0.3)' },
                                { offset: 1, color: 'rgba(54, 162, 235, 0.1)' }
                            ]
                        }
                    },
                    lineStyle: { color: 'rgba(54, 162, 235, 1)', width: 2 },
                    data: [3500, 3800, 4200, 3900, 4100, 3700, 4000]
                }]
            };
            revenueChart.setOption(revenueOption);
            console.log('✅ 收入趋势图表初始化成功');
        } else {
            if (revenueChartDom) {
                revenueChartDom.innerHTML = '<div style="text-align:center;padding:50px;color:#999;">ECharts库未加载</div>';
            }
        }
        
        // 初始化用户分布图表
        var userChartDom = document.getElementById('user_chart_safe');
        if (userChartDom && typeof echarts !== 'undefined') {
            var userChart = echarts.init(userChartDom);
            var userOption = {
                title: {
                    text: '用户类型分布',
                    left: 'center',
                    textStyle: { fontSize: 14, color: '#333' }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: function(params) {
                        return params.seriesName + '<br/>' + 
                               params.name + ': ' + params.value + ' (' + params.percent + '%)';
                    }
                },
                legend: {
                    orient: 'vertical',
                    left: 'left'
                },
                series: [{
                    name: '用户分布',
                    type: 'pie',
                    radius: ['40%', '70%'],
                    center: ['50%', '60%'],
                    avoidLabelOverlap: false,
                    emphasis: {
                        label: { show: true, fontSize: '16', fontWeight: 'bold' }
                    },
                    data: [
                        { value: 156, name: '代理商' },
                        { value: 1235, name: '商铺' },
                        { value: 89, name: '业务员' },
                        { value: 23, name: '团队长' }
                    ],
                    itemStyle: {
                        color: function(params) {
                            var colors = ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0'];
                            return colors[params.dataIndex % colors.length];
                        }
                    }
                }]
            };
            userChart.setOption(userOption);
            console.log('✅ 用户分布图表初始化成功');
        } else {
            if (userChartDom) {
                userChartDom.innerHTML = '<div style="text-align:center;padding:50px;color:#999;">ECharts库未加载</div>';
            }
        }
        
        // 响应式调整
        window.addEventListener('resize', function() {
            if (typeof revenueChart !== 'undefined') {
                revenueChart.resize();
            }
            if (typeof userChart !== 'undefined') {
                userChart.resize();
            }
        });
        
    }, 1500); // 增加延迟确保完全加载
});
</script> 