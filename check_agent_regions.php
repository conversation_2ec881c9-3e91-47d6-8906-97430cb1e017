<?php

require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\\Contracts\\Console\\Kernel')->bootstrap();

try {
    echo "=== agent_regions表数据检查 ===" . PHP_EOL;

    // 检查agent_regions表的所有数据
    $regions = DB::table('agent_regions')->get();
    echo "agent_regions表总记录数: " . $regions->count() . PHP_EOL;

    if ($regions->count() > 0) {
        echo PHP_EOL . "详细数据:" . PHP_EOL;
        foreach ($regions as $region) {
            echo "  ID: {$region->id}" . PHP_EOL;
            echo "  代理商ID: {$region->agent_id}" . PHP_EOL;

            // 查找对应的代理商
            $agent = DB::table('agents')->where('id', $region->agent_id)->first();
            if ($agent) {
                echo "  代理商名称: {$agent->name}" . PHP_EOL;
                echo "  代理商等级: {$agent->level}" . PHP_EOL;
                echo "  上级代理商ID: {$agent->parent_agent_id}" . PHP_EOL;
            } else {
                echo "  代理商名称: 未找到" . PHP_EOL;
            }

            echo "  省份: " . ($region->province_name ?? '未设置') . PHP_EOL;
            echo "  城市: " . ($region->city_name ?? '未设置') . PHP_EOL;
            echo "  区域状态: " . ($region->region_status ?? '未设置') . PHP_EOL;
            echo "  创建时间: " . ($region->created_at ?? '未设置') . PHP_EOL;
            echo "  ---" . PHP_EOL;
        }
    }

    // 检查agents表中parent_agent_id=3的代理商
    echo PHP_EOL . "parent_agent_id=3的代理商:" . PHP_EOL;
    $subAgents = DB::table('agents')->where('parent_agent_id', 3)->get();
    foreach ($subAgents as $agent) {
        echo "  ID: {$agent->id}, 名称: {$agent->name}, 等级: {$agent->level}" . PHP_EOL;

        // 检查这个代理商是否有区域配置
        $regionCount = DB::table('agent_regions')->where('agent_id', $agent->id)->count();
        echo "    区域配置数量: {$regionCount}" . PHP_EOL;
    }
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . PHP_EOL;
}
