# Access Token API 接口文档

## 概述

本文档描述了获取和验证 access_token 的 API 接口。该接口基于 OAuth 2.0 client_credentials 流程，用于第三方应用程序获取访问令牌。

## 基础信息

- **基础URL**: `http://your-domain.com/api/auth`
- **认证方式**: appKey + appSecret
- **数据格式**: JSON
- **字符编码**: UTF-8

## 接口列表

### 1. 获取 Access Token

**接口描述**: 通过 appKey 和 appSecret 获取访问令牌

**请求信息**:
- **请求方法**: `POST`
- **请求路径**: `/access-token`
- **Content-Type**: `application/x-www-form-urlencoded`

**请求参数**:

| 参数名     | 类型   | 必填 | 描述                                | 示例值                           |
| ---------- | ------ | ---- | ----------------------------------- | -------------------------------- |
| appKey     | string | 是   | 应用密钥                            | red.qzgiaXL14ncBwlBz             |
| appSecret  | string | 是   | 应用密码                            | cf2ef172726d1272c4ed661e1c059a45 |
| grant_type | string | 否   | 授权类型，默认为 client_credentials | client_credentials               |

**成功响应**:

```json
{
  "code": 200,
  "message": "获取access_token成功",
  "data": {
    "access_token": "eyJhcHBfa2V5IjoicmVkLnF6Z2lhWEwxNG5jQndsQnoiLCJpYXQiOjE3NTIxMzA0OTUsImV4cCI6MTc1MjEzNzY5NSwic2NvcGUiOiJyZWFkIHdyaXRlIn0.NTE5Yjk0OTU3NzIxNGE3NzM0M2Q4MTRkZTFjOWZlODNiOGZjZWE5NTM3NjI1NGU1NTVhMDZmNjZkMmRjYWE3NQ==",
    "token_type": "Bearer",
    "expires_in": 7200,
    "scope": "read write"
  },
  "timestamp": 1752130495
}
```

**错误响应**:

```json
{
  "code": 400,
  "message": "无效的appKey或appSecret",
  "timestamp": 1752130495
}
```

**响应字段说明**:

| 字段名            | 类型   | 描述                                  |
| ----------------- | ------ | ------------------------------------- |
| code              | int    | 响应状态码，200表示成功               |
| message           | string | 响应消息                              |
| data.access_token | string | 访问令牌                              |
| data.token_type   | string | 令牌类型，固定为"Bearer"              |
| data.expires_in   | int    | 令牌有效期（秒），默认7200秒（2小时） |
| data.scope        | string | 授权范围                              |
| timestamp         | int    | 响应时间戳                            |

### 2. 验证 Access Token

**接口描述**: 验证访问令牌的有效性

**请求信息**:
- **请求方法**: `POST`
- **请求路径**: `/verify-token`
- **Content-Type**: `application/x-www-form-urlencoded`

**请求参数**:

| 参数名       | 类型   | 必填 | 描述               | 示例值                                                                                                                     |
| ------------ | ------ | ---- | ------------------ | -------------------------------------------------------------------------------------------------------------------------- |
| access_token | string | 是   | 需要验证的访问令牌 | eyJhcHBfa2V5IjoicmVkLnF6Z2lhWEwxNG5jQndsQnoiLCJpYXQiOjE3NTIxMzA0OTUsImV4cCI6MTc1MjEzNzY5NSwic2NvcGUiOiJyZWFkIHdyaXRlIn0... |

**成功响应**:

```json
{
  "code": 200,
  "message": "令牌验证成功",
  "data": {
    "valid": true,
    "payload": {
      "app_key": "red.qzgiaXL14ncBwlBz",
      "iat": 1752130495,
      "exp": 1752137695,
      "scope": "read write"
    },
    "message": "令牌有效"
  },
  "timestamp": 1752130495
}
```

**错误响应**:

```json
{
  "code": 401,
  "message": "令牌已过期",
  "timestamp": 1752130495
}
```

## 错误码说明

| 错误码 | 描述           | 解决方案                          |
| ------ | -------------- | --------------------------------- |
| 200    | 请求成功       | -                                 |
| 400    | 请求参数错误   | 检查 appKey 和 appSecret 是否正确 |
| 401    | 令牌无效或过期 | 重新获取 access_token             |
| 422    | 参数验证失败   | 检查必填参数是否提供              |
| 500    | 服务器内部错误 | 联系技术支持                      |

## 使用示例

### cURL 示例

**获取 access_token:**

```bash
curl -X POST 'http://localhost/pyp-Laravel-new/public/api/auth/access-token' \
  -H 'Content-Type: application/x-www-form-urlencoded' \
  -H 'Accept: application/json' \
  -d 'appKey=red.qzgiaXL14ncBwlBz&appSecret=cf2ef172726d1272c4ed661e1c059a45&grant_type=client_credentials'
```

**验证 token:**

```bash
curl -X POST 'http://localhost/pyp-Laravel-new/public/api/auth/verify-token' \
  -H 'Content-Type: application/x-www-form-urlencoded' \
  -H 'Accept: application/json' \
  -d 'access_token=[YOUR_ACCESS_TOKEN]'
```

### JavaScript 示例

```javascript
// 获取 access_token
fetch('/api/auth/access-token', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded',
    'Accept': 'application/json'
  },
  body: new URLSearchParams({
    'appKey': 'red.qzgiaXL14ncBwlBz',
    'appSecret': 'cf2ef172726d1272c4ed661e1c059a45',
    'grant_type': 'client_credentials'
  })
})
.then(response => response.json())
.then(data => {
  if (data.code === 200) {
    console.log('Access Token:', data.data.access_token);
  } else {
    console.error('Error:', data.message);
  }
});
```

### PHP 示例

```php
<?php
// 获取 access_token
$url = 'http://localhost/pyp-Laravel-new/public/api/auth/access-token';
$data = [
    'appKey' => 'red.qzgiaXL14ncBwlBz',
    'appSecret' => 'cf2ef172726d1272c4ed661e1c059a45',
    'grant_type' => 'client_credentials'
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/x-www-form-urlencoded',
    'Accept: application/json'
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

$result = json_decode($response, true);
if ($result['code'] === 200) {
    echo "Access Token: " . $result['data']['access_token'];
} else {
    echo "Error: " . $result['message'];
}
?>
```

## 注意事项

1. **令牌缓存**: 系统会自动缓存有效的 access_token，重复请求会返回缓存的令牌
2. **令牌有效期**: 默认有效期为 2 小时（7200秒）
3. **安全性**: appSecret 应该妥善保管，不要在前端代码中暴露
4. **频率限制**: 建议不要频繁请求新令牌，使用缓存的令牌直到过期
5. **HTTPS**: 生产环境建议使用 HTTPS 协议保证数据传输安全

## 联系支持

如有问题，请联系技术支持团队。 