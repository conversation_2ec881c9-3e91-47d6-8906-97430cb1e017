<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class CreateAgentMenuCommand extends Command
{
    /**
     * 命令名称
     *
     * @var string
     */
    protected $signature = 'admin:create-agent-menu';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = '创建代理商管理模块菜单';

    /**
     * 执行命令
     */
    public function handle()
    {
        $this->info('开始创建代理商管理模块菜单...');

        try {
            // 获取当前最大的ID和order值
            $maxId = DB::table('admin_menu')->max('id');
            $maxOrder = DB::table('admin_menu')->where('parent_id', 0)->max('order');

            // 创建代理商管理主菜单
            $agentMenuId = $maxId + 1;
            DB::table('admin_menu')->insert([
                'id' => $agentMenuId,
                'parent_id' => 0,
                'order' => $maxOrder + 1,
                'title' => '代理商管理',
                'icon' => 'fa-handshake-o',
                'uri' => '/agents',
                'permission' => null,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);

            $this->info("✓ 创建代理商管理主菜单成功 (ID: {$agentMenuId})");

            // 创建代理商列表子菜单
            DB::table('admin_menu')->insert([
                'id' => $maxId + 2,
                'parent_id' => $agentMenuId,
                'order' => 1,
                'title' => '代理商列表',
                'icon' => 'fa-list',
                'uri' => '/agents',
                'permission' => null,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);
            $this->info('✓ 创建代理商列表子菜单成功');

            // 创建新增代理商子菜单
            DB::table('admin_menu')->insert([
                'id' => $maxId + 3,
                'parent_id' => $agentMenuId,
                'order' => 2,
                'title' => '新增代理商',
                'icon' => 'fa-plus-circle',
                'uri' => '/agents/create',
                'permission' => null,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);
            $this->info('✓ 创建新增代理商子菜单成功');

            // 创建区域配置子菜单
            DB::table('admin_menu')->insert([
                'id' => $maxId + 4,
                'parent_id' => $agentMenuId,
                'order' => 3,
                'title' => '区域配置',
                'icon' => 'fa-map-marker',
                'uri' => '/agent-regions',
                'permission' => null,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);
            $this->info('✓ 创建区域配置子菜单成功');

            // 创建结算与分佣管理子菜单
            DB::table('admin_menu')->insert([
                'id' => $maxId + 5,
                'parent_id' => $agentMenuId,
                'order' => 4,
                'title' => '结算与分佣管理',
                'icon' => 'fa-money',
                'uri' => '/agent-commissions',
                'permission' => null,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);
            $this->info('✓ 创建结算与分佣管理子菜单成功');

            // 创建代理商数据统计子菜单
            DB::table('admin_menu')->insert([
                'id' => $maxId + 6,
                'parent_id' => $agentMenuId,
                'order' => 5,
                'title' => '代理商数据统计',
                'icon' => 'fa-bar-chart',
                'uri' => '/agent-statistics',
                'permission' => null,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);
            $this->info('✓ 创建代理商数据统计子菜单成功');

            $this->info('代理商管理模块菜单创建成功！');
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('创建菜单时发生错误: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}
