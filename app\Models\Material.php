<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

/**
 * 素材模型
 * 
 * @property int $id
 * @property string $title 素材标题
 * @property string $type 素材类型
 * @property string|null $category 分类
 * @property array|null $tags 标签
 * @property array|null $applicable_modules 适用模块
 * @property string|null $file_path 文件路径
 * @property string|null $content 文本内容
 * @property string $audit_status 审核状态
 * @property string|null $audit_remark 审核备注
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 */
class Material extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'type',
        'category',
        'tags',
        'applicable_modules',
        'file_path',
        'content',
        'audit_status',
        'audit_remark',
    ];

    protected $casts = [
        'tags' => 'array',
        'applicable_modules' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 素材类型常量
     */
    const TYPE_VIDEO = 'video';
    const TYPE_IMAGE = 'image';
    const TYPE_TEXT = 'text';
    const TYPE_AI_PROMPT = 'ai_prompt';

    /**
     * 审核状态常量
     */
    const AUDIT_PENDING = 'pending';
    const AUDIT_APPROVED = 'approved';
    const AUDIT_REJECTED = 'rejected';

    /**
     * 获取素材类型选项
     */
    public static function getTypeOptions()
    {
        return [
            self::TYPE_VIDEO => '视频',
            self::TYPE_IMAGE => '图片',
            self::TYPE_TEXT => '文本',
            self::TYPE_AI_PROMPT => 'AI提示词',
        ];
    }

    /**
     * 获取审核状态选项
     */
    public static function getAuditStatusOptions()
    {
        return [
            self::AUDIT_PENDING => '待审核',
            self::AUDIT_APPROVED => '已通过',
            self::AUDIT_REJECTED => '已驳回',
        ];
    }

    /**
     * 获取分类选项
     */
    public static function getCategoryOptions()
    {
        return [
            'promotion' => '推广素材',
            'brand' => '品牌素材',
            'product' => '产品素材',
            'activity' => '活动素材',
            'template' => '模板素材',
        ];
    }

    /**
     * 获取适用模块选项
     */
    public static function getApplicableModuleOptions()
    {
        return [
            'video_publish' => '发视频',
            'review_checkin' => '点评打卡',
            'wechat_marketing' => '微信营销',
            'group_buying' => '团购配置',
            'follow_account' => '关注账号',
            'wifi_config' => 'WiFi配置',
        ];
    }

    /**
     * 获取类型标签
     */
    public function getTypeLabelAttribute()
    {
        return self::getTypeOptions()[$this->type] ?? $this->type;
    }

    /**
     * 获取审核状态标签
     */
    public function getAuditStatusLabelAttribute()
    {
        return self::getAuditStatusOptions()[$this->audit_status] ?? $this->audit_status;
    }

    /**
     * 获取审核状态颜色
     */
    public function getAuditStatusColorAttribute()
    {
        $colors = [
            self::AUDIT_PENDING => 'warning',
            self::AUDIT_APPROVED => 'success',
            self::AUDIT_REJECTED => 'danger',
        ];
        return $colors[$this->audit_status] ?? 'default';
    }

    /**
     * 获取分类标签
     */
    public function getCategoryLabelAttribute()
    {
        $categories = self::getCategoryOptions();
        return $categories[$this->category] ?? $this->category;
    }

    /**
     * 获取文件URL
     */
    public function getFileUrlAttribute()
    {
        if (!$this->file_path) {
            return null;
        }

        if (filter_var($this->file_path, FILTER_VALIDATE_URL)) {
            return $this->file_path;
        }

        return Storage::url($this->file_path);
    }

    /**
     * 获取文件大小
     */
    public function getFileSizeAttribute()
    {
        if (!$this->file_path || !Storage::exists($this->file_path)) {
            return null;
        }

        $bytes = Storage::size($this->file_path);
        return $this->formatBytes($bytes);
    }

    /**
     * 格式化字节数
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * 获取标签字符串
     */
    public function getTagsStringAttribute()
    {
        return $this->tags ? implode(', ', $this->tags) : '';
    }

    /**
     * 获取适用模块字符串
     */
    public function getApplicableModulesStringAttribute()
    {
        if (!$this->applicable_modules) {
            return '';
        }

        $moduleOptions = self::getApplicableModuleOptions();
        $labels = [];

        foreach ($this->applicable_modules as $module) {
            $labels[] = $moduleOptions[$module] ?? $module;
        }

        return implode(', ', $labels);
    }

    /**
     * 作用域：按类型筛选
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * 作用域：按审核状态筛选
     */
    public function scopeByAuditStatus($query, $status)
    {
        return $query->where('audit_status', $status);
    }

    /**
     * 作用域：已审核通过
     */
    public function scopeApproved($query)
    {
        return $query->where('audit_status', self::AUDIT_APPROVED);
    }

    /**
     * 作用域：按分类筛选
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * 作用域：按适用模块筛选
     */
    public function scopeByApplicableModule($query, $module)
    {
        return $query->whereJsonContains('applicable_modules', $module);
    }

    /**
     * 作用域：搜索
     */
    public function scopeSearch($query, $keyword)
    {
        return $query->where(function ($q) use ($keyword) {
            $q->where('title', 'like', "%{$keyword}%")
                ->orWhere('content', 'like', "%{$keyword}%")
                ->orWhere('category', 'like', "%{$keyword}%");
        });
    }

    /**
     * 检查是否为图片类型
     */
    public function isImage()
    {
        return $this->type === self::TYPE_IMAGE;
    }

    /**
     * 检查是否为视频类型
     */
    public function isVideo()
    {
        return $this->type === self::TYPE_VIDEO;
    }

    /**
     * 检查是否为文本类型
     */
    public function isText()
    {
        return $this->type === self::TYPE_TEXT;
    }

    /**
     * 检查是否为AI提示词类型
     */
    public function isAiPrompt()
    {
        return $this->type === self::TYPE_AI_PROMPT;
    }

    /**
     * 审核通过
     */
    public function approve($remark = null)
    {
        $this->update([
            'audit_status' => self::AUDIT_APPROVED,
            'audit_remark' => $remark,
        ]);
    }

    /**
     * 审核驳回
     */
    public function reject($remark)
    {
        $this->update([
            'audit_status' => self::AUDIT_REJECTED,
            'audit_remark' => $remark,
        ]);
    }

    /**
     * 删除文件
     */
    public function deleteFile()
    {
        if ($this->file_path && Storage::exists($this->file_path)) {
            Storage::delete($this->file_path);
        }
    }

    /**
     * 模型删除时删除关联文件
     */
    protected static function boot()
    {
        parent::boot();

        static::deleting(function ($material) {
            $material->deleteFile();
        });
    }
}
