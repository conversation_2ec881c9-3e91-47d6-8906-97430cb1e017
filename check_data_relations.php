<?php

require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\\Contracts\\Console\\Kernel')->bootstrap();

use App\Models\Agent;
use App\Models\AgentRegion;
use Encore\Admin\Auth\Database\Administrator;

try {
    echo "=== 数据关系检查 ===" . PHP_EOL;

    // 检查agent_001用户
    $user = Administrator::where('username', 'agent_001')->first();
    if ($user) {
        echo "用户 agent_001:" . PHP_EOL;
        echo "  ID: {$user->id}" . PHP_EOL;
        echo "  关联代理商ID: {$user->agent_id}" . PHP_EOL;

        // 检查关联的代理商
        $currentAgent = Agent::find($user->agent_id);
        if ($currentAgent) {
            echo "  关联代理商: {$currentAgent->name} (ID: {$currentAgent->id})" . PHP_EOL;

            // 检查下级代理商
            $subAgents = Agent::where('parent_agent_id', $currentAgent->id)->get();
            echo "  下级代理商数量: " . $subAgents->count() . PHP_EOL;

            foreach ($subAgents as $subAgent) {
                echo "    - {$subAgent->name} (ID: {$subAgent->id})" . PHP_EOL;

                // 检查该代理商的区域配置
                $regionCount = AgentRegion::where('agent_id', $subAgent->id)->count();
                echo "      区域配置数量: {$regionCount}" . PHP_EOL;
            }

            // 检查所有区域配置
            $allRegions = AgentRegion::all();
            echo PHP_EOL . "所有区域配置:" . PHP_EOL;
            foreach ($allRegions as $region) {
                $agent = Agent::find($region->agent_id);
                $agentName = $agent ? $agent->name : '未知';
                echo "  区域ID: {$region->id}, 代理商ID: {$region->agent_id} ({$agentName})" . PHP_EOL;
            }

            // 检查应该显示的区域配置
            $subAgentIds = $subAgents->pluck('id');
            $visibleRegions = AgentRegion::whereIn('agent_id', $subAgentIds)->get();
            echo PHP_EOL . "agent_001应该看到的区域配置:" . PHP_EOL;
            echo "  数量: " . $visibleRegions->count() . PHP_EOL;
            foreach ($visibleRegions as $region) {
                $agent = Agent::find($region->agent_id);
                $agentName = $agent ? $agent->name : '未知';
                echo "    - 区域ID: {$region->id}, 代理商: {$agentName}" . PHP_EOL;
            }
        }
    }
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . PHP_EOL;
}
