<?php

namespace App\Admin\Actions;

use Encore\Admin\Grid\Tools\BatchAction;

class BatchApproveStores extends BatchAction
{
    public $name = '批量审核通过';

    public function script()
    {
        return <<<EOT

$('{$this->getElementClass()}').on('click', function() {

    var selected = selectedRows();
    
    if (selected.length === 0) {
        toastr.error('请选择要操作的数据');
        return;
    }
    
    var data = {
        _method:'POST',
        _token:'{$this->getToken()}',
        selected:selected
    };

    $.ajax({
        method: 'POST',
        url: '/admin/merchant/batch-approve',
        data: data,
        success: function (data) {
            if (data.status) {
                $.pjax.reload('#pjax-container');
                toastr.success(data.message);
            } else {
                toastr.error(data.message);
            }
        },
        error: function() {
            toastr.error('操作失败');
        }
    });
});

EOT;
    }
}
