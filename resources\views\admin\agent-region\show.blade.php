<?php
/**
 * 代理商区域配置详情页
 */
?>

<div class="box box-info">
    <div class="box-header with-border">
        <h3 class="box-title">代理商区域配置详情</h3>
        <div class="box-tools">
            <a href="{{ admin_url('agent-regions') }}" class="btn btn-sm btn-default">
                <i class="fa fa-list"></i> 返回列表
            </a>
        </div>
    </div>

    <div class="box-body">
        <!-- 第一部分：基础配置 -->
        <div class="panel panel-primary">
            <div class="panel-heading">
                <h4 class="panel-title"><i class="fa fa-cog"></i> 基础配置</h4>
            </div>
            <div class="panel-body">
                <table class="table table-bordered table-striped">
                    <tr>
                        <th style="width: 200px;">代理商</th>
                        <td>{{ $agent_region->agent->name ?? '未知' }}</td>
                    </tr>
                    <tr>
                        <th>区域状态</th>
                        <td>
                            @php
                                $statusMap = [
                                    'pending' => '<span class="label label-warning">待分配</span>',
                                    'active' => '<span class="label label-success">活跃</span>',
                                    'suspended' => '<span class="label label-danger">暂停</span>',
                                    'inactive' => '<span class="label label-default">停用</span>'
                                ];
                            @endphp
                            {!! $statusMap[$agent_region->region_status] ?? '未知状态' !!}
                        </td>
                    </tr>
                    <tr>
                        <th>管理区域</th>
                        <td>{{ $agent_region->province->name ?? '' }} {{ $agent_region->city->name ?? '' }} {{ $agent_region->district->name ?? '' }}</td>
                    </tr>
                    <tr>
                        <th>区域独占</th>
                        <td>{!! $agent_region->is_exclusive ? '<span class="label label-success">是</span>' : '<span class="label label-default">否</span>' !!}</td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- 第二部分：合同管理 -->
        <div class="panel panel-info">
            <div class="panel-heading">
                <h4 class="panel-title"><i class="fa fa-file-text"></i> 合同管理</h4>
            </div>
            <div class="panel-body">
                <table class="table table-bordered table-striped">
                    <tr>
                        <th style="width: 200px;">合约状态</th>
                        <td>
                            @php
                                $contractStatusMap = [
                                    'draft' => '<span class="label label-default">草稿</span>',
                                    'reviewing' => '<span class="label label-warning">审核中</span>',
                                    'signed' => '<span class="label label-success">已签署</span>',
                                    'expired' => '<span class="label label-danger">已过期</span>',
                                    'terminated' => '<span class="label label-danger">已终止</span>'
                                ];
                            @endphp
                            {!! $contractStatusMap[$agent_region->contract_status] ?? '未知状态' !!}
                        </td>
                    </tr>
                    <tr>
                        <th>合约编号</th>
                        <td>{{ $agent_region->contract_no ?? '未设置' }}</td>
                    </tr>
                    <tr>
                        <th>合约标题</th>
                        <td>{{ $agent_region->contract_title ?? '未设置' }}</td>
                    </tr>
                    <tr>
                        <th>签署日期</th>
                        <td>{{ $agent_region->signed_at ? $agent_region->signed_at->format('Y-m-d') : '未设置' }}</td>
                    </tr>
                    <tr>
                        <th>合约期限</th>
                        <td>
                            {{ $agent_region->contract_start_date ? $agent_region->contract_start_date->format('Y-m-d') : '未设置' }}
                            至
                            {{ $agent_region->contract_end_date ? $agent_region->contract_end_date->format('Y-m-d') : '未设置' }}
                        </td>
                    </tr>
                    <tr>
                        <th>合约文件</th>
                        <td>
                            @if($agent_region->contract_file_url)
                                <a href="{{ $agent_region->contract_file_url }}" target="_blank" class="btn btn-xs btn-info">
                                    <i class="fa fa-download"></i> 查看文件
                                </a>
                            @else
                                未上传
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <th>合约备注</th>
                        <td>{{ $agent_region->contract_notes ?? '无' }}</td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- 第三部分：返佣配置 -->
        <div class="panel panel-success">
            <div class="panel-heading">
                <h4 class="panel-title"><i class="fa fa-money"></i> 返佣配置</h4>
            </div>
            <div class="panel-body">
                <table class="table table-bordered table-striped">
                    <tr>
                        <th style="width: 200px;">返佣类型</th>
                        <td>
                            @php
                                $commissionTypeMap = [
                                    'percentage' => '按比例',
                                    'fixed' => '固定金额',
                                    'tiered' => '阶梯式'
                                ];
                            @endphp
                            {{ $commissionTypeMap[$agent_region->commission_type] ?? '未知' }}
                        </td>
                    </tr>
                    <tr>
                        <th>返佣比例</th>
                        <td>{{ $agent_region->commission_rate ? $agent_region->commission_rate . '%' : '未设置' }}</td>
                    </tr>
                    <tr>
                        <th>固定返佣金额</th>
                        <td>{{ $agent_region->commission_amount ? '¥' . number_format($agent_region->commission_amount, 2) : '未设置' }}</td>
                    </tr>
                    <tr>
                        <th>最低订单金额</th>
                        <td>{{ $agent_region->min_order_amount ? '¥' . number_format($agent_region->min_order_amount, 2) : '不限制' }}</td>
                    </tr>
                    <tr>
                        <th>单笔最高返佣</th>
                        <td>{{ $agent_region->max_commission_per_order ? '¥' . number_format($agent_region->max_commission_per_order, 2) : '不限制' }}</td>
                    </tr>
                    <tr>
                        <th>月度返佣上限</th>
                        <td>{{ $agent_region->max_commission_per_month ? '¥' . number_format($agent_region->max_commission_per_month, 2) : '不限制' }}</td>
                    </tr>
                    <tr>
                        <th>阶梯式返佣规则</th>
                        <td>
                            @if($agent_region->commission_rules)
                                <pre>{{ $agent_region->commission_rules }}</pre>
                            @else
                                未设置
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <th>返佣有效期</th>
                        <td>
                            {{ $agent_region->commission_effective_date ? $agent_region->commission_effective_date->format('Y-m-d') : '未设置' }}
                            至
                            {{ $agent_region->commission_expire_date ? $agent_region->commission_expire_date->format('Y-m-d') : '长期有效' }}
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- 第四部分：结算配置 -->
        <div class="panel panel-warning">
            <div class="panel-heading">
                <h4 class="panel-title"><i class="fa fa-bank"></i> 结算配置</h4>
            </div>
            <div class="panel-body">
                <table class="table table-bordered table-striped">
                    <tr>
                        <th style="width: 200px;">结算周期</th>
                        <td>
                            @php
                                $settlementCycleMap = [
                                    'weekly' => '每周结算',
                                    'monthly' => '每月结算',
                                    'quarterly' => '每季度结算'
                                ];
                            @endphp
                            {{ $settlementCycleMap[$agent_region->settlement_cycle] ?? '未知' }}
                        </td>
                    </tr>
                    <tr>
                        <th>结算日</th>
                        <td>{{ $agent_region->settlement_day ?? '未设置' }}</td>
                    </tr>
                    <tr>
                        <th>银行账户信息</th>
                        <td>
                            <p><strong>账户名：</strong>{{ $agent_region->settlement_account_name ?? '未设置' }}</p>
                            <p><strong>账号：</strong>{{ $agent_region->settlement_bank_account ?? '未设置' }}</p>
                            <p><strong>开户行：</strong>{{ $agent_region->settlement_bank_name ?? '未设置' }}</p>
                            <p><strong>支行：</strong>{{ $agent_region->bank_branch ?? '未设置' }}</p>
                        </td>
                    </tr>
                    <tr>
                        <th>最低结算金额</th>
                        <td>{{ $agent_region->min_settlement_amount ? '¥' . number_format($agent_region->min_settlement_amount, 2) : '不限制' }}</td>
                    </tr>
                    <tr>
                        <th>结算手续费率</th>
                        <td>{{ $agent_region->settlement_fee_rate ? $agent_region->settlement_fee_rate . '%' : '0%' }}</td>
                    </tr>
                    <tr>
                        <th>结算时间</th>
                        <td>
                            <p><strong>上次结算：</strong>{{ $agent_region->last_settlement_at ? $agent_region->last_settlement_at->format('Y-m-d') : '未结算' }}</p>
                            <p><strong>下次结算：</strong>{{ $agent_region->next_settlement_at ? $agent_region->next_settlement_at->format('Y-m-d') : '未确定' }}</p>
                        </td>
                    </tr>
                    <tr>
                        <th>结算备注</th>
                        <td>{{ $agent_region->settlement_notes ?? '无' }}</td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- 第五部分：其他信息 -->
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title"><i class="fa fa-info-circle"></i> 其他信息</h4>
            </div>
            <div class="panel-body">
                <table class="table table-bordered table-striped">
                    <tr>
                        <th style="width: 200px;">创建人</th>
                        <td>{{ $agent_region->created_by ?? '系统' }}</td>
                    </tr>
                    <tr>
                        <th>联系方式</th>
                        <td>{{ $agent_region->contact_info ?? '未设置' }}</td>
                    </tr>
                    <tr>
                        <th>创建时间</th>
                        <td>{{ $agent_region->created_at ? $agent_region->created_at->format('Y-m-d H:i:s') : '未知' }}</td>
                    </tr>
                    <tr>
                        <th>最后更新</th>
                        <td>{{ $agent_region->updated_at ? $agent_region->updated_at->format('Y-m-d H:i:s') : '未知' }}</td>
                    </tr>
                    <tr>
                        <th>备注说明</th>
                        <td>{{ $agent_region->notes ?? '无' }}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

<style>
.panel {
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,.1);
}
.panel-heading {
    border-bottom: 1px solid #eee;
}
.panel-title {
    margin: 0;
    font-size: 16px;
}
.panel-title i {
    margin-right: 5px;
}
.table th {
    background-color: #f9f9f9;
}
pre {
    background-color: #f5f5f5;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    white-space: pre-wrap;
}
</style> 