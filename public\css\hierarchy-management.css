/**
 * 代理商层级管理专用样式
 */

/* 层级管理主页面样式 */
.hierarchy-overview {
    margin-bottom: 20px;
}

.hierarchy-overview .small-box {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
}

.hierarchy-overview .small-box:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 层级路径样式 */
.hierarchy-path {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    padding: 20px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: 8px;
    margin-bottom: 20px;
}

.path-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    min-width: 120px;
    margin: 0 10px;
    padding: 15px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.path-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.path-item.current {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: 2px solid #007bff;
    transform: scale(1.05);
}

.path-item.current .path-icon {
    color: white;
}

.path-icon {
    font-size: 28px;
    color: #007bff;
    margin-bottom: 8px;
}

.path-info h4 {
    margin: 0 0 5px 0;
    font-size: 14px;
    font-weight: bold;
}

.path-info p {
    margin: 0;
    font-size: 12px;
    opacity: 0.8;
}

.path-arrow {
    font-size: 20px;
    color: #007bff;
    margin: 0 15px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }

    100% {
        opacity: 1;
    }
}

/* 层级表格样式 */
.hierarchy-table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hierarchy-table .table {
    margin-bottom: 0;
}

.hierarchy-table .table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    font-weight: 600;
    text-align: center;
}

.hierarchy-table .table tbody tr {
    transition: background-color 0.2s ease;
}

.hierarchy-table .table tbody tr:hover {
    background-color: #f8f9fa;
}

.hierarchy-table .agent-info {
    padding: 5px 0;
}

.hierarchy-table .agent-info strong {
    color: #333;
    font-size: 14px;
}

.hierarchy-table .agent-info small {
    display: block;
    margin-top: 2px;
    color: #666;
}

/* 进度条样式 */
.progress-group {
    margin-bottom: 10px;
}

.progress-text {
    font-size: 12px;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
    display: block;
}

.progress {
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.progress-bar {
    transition: width 0.6s ease;
}

.progress-bar-green {
    background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
}

.progress-bar-blue {
    background: linear-gradient(90deg, #007bff 0%, #17a2b8 100%);
}

.progress-bar-yellow {
    background: linear-gradient(90deg, #ffc107 0%, #fd7e14 100%);
}

.progress-bar-red {
    background: linear-gradient(90deg, #dc3545 0%, #e83e8c 100%);
}

/* 层级详情页面样式 */
.hierarchy-detail {
    padding: 20px;
}

.info-box {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
}

.info-box:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.info-box-icon {
    border-radius: 8px 0 0 8px;
}

.info-box-content {
    padding: 15px;
}

.info-box-text {
    font-size: 13px;
    font-weight: 600;
    color: #666;
    text-transform: uppercase;
}

.info-box-number {
    font-size: 18px;
    font-weight: bold;
    color: #333;
}

/* 统计卡片样式 */
.stats-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    transition: transform 0.2s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.stats-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.stats-card-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.stats-card-icon {
    font-size: 20px;
    color: #007bff;
}

.stats-card-body {
    padding: 10px 0;
}

.stats-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f8f9fa;
}

.stats-item:last-child {
    border-bottom: none;
}

.stats-label {
    font-size: 14px;
    color: #666;
}

.stats-value {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

/* 操作按钮样式 */
.btn-group .btn {
    border-radius: 4px;
    margin-right: 5px;
    transition: all 0.2s ease;
}

.btn-group .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-xs {
    padding: 3px 8px;
    font-size: 11px;
}

/* 标签样式 */
.label {
    font-size: 11px;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.label-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.label-info {
    background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%);
}

.label-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.label-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
}

.label-danger {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-control {
    border-radius: 4px;
    border: 1px solid #ddd;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.text-danger {
    color: #dc3545;
    font-size: 12px;
    margin-top: 5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .hierarchy-path {
        flex-direction: column;
        align-items: center;
    }

    .path-item {
        margin: 10px 0;
        min-width: 200px;
    }

    .path-arrow {
        transform: rotate(90deg);
        margin: 10px 0;
    }

    .hierarchy-table {
        font-size: 12px;
    }

    .btn-group .btn {
        margin-bottom: 5px;
    }
}

@media (max-width: 576px) {
    .hierarchy-overview .col-lg-3 {
        margin-bottom: 15px;
    }

    .stats-card {
        padding: 15px;
    }

    .info-box-content {
        padding: 10px;
    }

    .hierarchy-table .table {
        font-size: 11px;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }

    to {
        transform: translateX(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

.slide-in {
    animation: slideIn 0.5s ease-out;
}

/* 加载状态 */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 1001;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* 工具提示 */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 120px;
    background-color: #333;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px 0;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -60px;
    opacity: 0;
    transition: opacity 0.3s;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* 打印样式 */
@media print {

    .box-tools,
    .btn-group,
    .hierarchy-tree,
    .modal {
        display: none !important;
    }

    .hierarchy-table {
        box-shadow: none;
    }

    .hierarchy-path {
        background: white;
        box-shadow: none;
    }
}