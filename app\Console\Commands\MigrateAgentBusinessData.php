<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Agent;
use App\Models\AgentRegion;
use App\Models\AgentCommission;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

class MigrateAgentBusinessData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'agent:migrate-business-data {--dry-run : 只运行测试，不实际修改数据} {--force : 强制执行，跳过确认}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '将agents表中的业务配置数据迁移到agent_regions表';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');
        $force = $this->option('force');

        $this->info('开始数据迁移：agents -> agent_regions');
        $this->info('迁移模式：' . ($dryRun ? '测试模式（不修改数据）' : '实际执行'));

        // 安全确认
        if (!$dryRun && !$force) {
            if (!$this->confirm('此操作将修改数据库数据，建议先使用 --dry-run 测试。确认继续吗？')) {
                $this->info('操作已取消');
                return 0;
            }
        }

        // 步骤1：数据一致性检查
        $this->info('步骤1：执行数据一致性检查...');
        $inconsistencies = $this->checkDataConsistency();
        
        if (!empty($inconsistencies)) {
            $this->error('发现数据一致性问题：');
            foreach ($inconsistencies as $issue) {
                $this->error('  - ' . $issue);
            }
            
            if (!$force && !$this->confirm('发现数据问题，是否继续执行？')) {
                return 1;
            }
        } else {
            $this->info('✓ 数据一致性检查通过');
        }

        // 步骤2：获取需要迁移的代理商数据
        $this->info('步骤2：分析需要迁移的数据...');
        $agentsNeedMigration = $this->getAgentsNeedMigration();
        
        $this->info("发现 {$agentsNeedMigration->count()} 个代理商需要迁移业务配置");

        if ($agentsNeedMigration->count() === 0) {
            $this->info('没有需要迁移的数据');
            return 0;
        }

        // 步骤3：执行数据迁移
        $this->info('步骤3：开始数据迁移...');
        
        if (!$dryRun) {
            DB::beginTransaction();
        }

        try {
            $migrated = 0;
            $updated = 0;
            $errors = 0;

            foreach ($agentsNeedMigration as $agent) {
                $result = $this->migrateAgentBusinessData($agent, $dryRun);
                
                if ($result['success']) {
                    if ($result['action'] === 'created') {
                        $migrated++;
                    } else {
                        $updated++;
                    }
                    
                    if ($dryRun) {
                        $this->line("  [测试] 代理商 {$agent->name}: {$result['message']}");
                    }
                } else {
                    $errors++;
                    $this->error("  代理商 {$agent->name}: {$result['message']}");
                }
            }

            if (!$dryRun) {
                DB::commit();
            }

            // 步骤4：显示迁移结果
            $this->info('步骤4：迁移完成统计');
            $this->table(
                ['项目', '数量'],
                [
                    ['新创建区域配置', $migrated],
                    ['更新现有区域配置', $updated],
                    ['错误数量', $errors],
                    ['总处理数量', $agentsNeedMigration->count()]
                ]
            );

            if (!$dryRun) {
                // 步骤5：验证迁移结果
                $this->info('步骤5：验证迁移结果...');
                $this->validateMigrationResult();
            }

            $this->info('✓ 数据迁移完成');
            return 0;

        } catch (Exception $e) {
            if (!$dryRun) {
                DB::rollBack();
            }
            
            $this->error('迁移过程中发生错误：' . $e->getMessage());
            Log::error('Agent business data migration failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return 1;
        }
    }

    /**
     * 检查数据一致性
     */
    private function checkDataConsistency()
    {
        $inconsistencies = [];

        // 检查agents表中有业务配置但无对应agent_regions记录的情况
        $agentsWithoutRegions = Agent::where(function ($query) {
                $query->whereNotNull('commission_rate')
                      ->orWhereNotNull('settlement_cycle')
                      ->orWhereNotNull('settlement_days');
            })
            ->whereDoesntHave('agentRegions')
            ->count();

        if ($agentsWithoutRegions > 0) {
            $inconsistencies[] = "发现 {$agentsWithoutRegions} 个代理商有业务配置但无区域记录";
        }

        // 检查agent_commissions表与agents表的配置冲突（如果表存在）
        try {
            $conflictingCommissions = AgentCommission::join('agents', 'agent_commissions.agent_id', '=', 'agents.id')
                ->whereRaw('agent_commissions.commission_rate != agents.commission_rate')
                ->count();

            if ($conflictingCommissions > 0) {
                $inconsistencies[] = "发现 {$conflictingCommissions} 个代理商在agents表和agent_commissions表中有不同的佣金配置";
            }
        } catch (Exception $e) {
            // agent_commissions表不存在，跳过此检查
            $this->warn('agent_commissions表不存在，跳过佣金配置冲突检查');
        }

        return $inconsistencies;
    }

    /**
     * 获取需要迁移的代理商
     */
    private function getAgentsNeedMigration()
    {
        return Agent::where(function ($query) {
            $query->whereNotNull('commission_rate')
                  ->orWhereNotNull('settlement_cycle')
                  ->orWhereNotNull('settlement_days')
                  ->orWhereNotNull('signed_at');
        })->get();
    }

    /**
     * 迁移单个代理商的业务数据
     */
    private function migrateAgentBusinessData($agent, $dryRun = false)
    {
        try {
            // 检查是否已有agent_regions记录
            $existingRegion = AgentRegion::where('agent_id', $agent->id)->first();

            $businessData = [
                'commission_rate' => $agent->commission_rate ?? 0.00,
                'commission_amount' => $agent->commission_amount ?? 0.00,
                'min_order_amount' => $agent->min_order_amount ?? 0.00,
                'max_commission_per_order' => $agent->max_commission_per_order ?? 0.00,
                'max_commission_per_month' => $agent->max_commission_per_month ?? 0.00,
                'commission_rules' => $agent->commission_rules ?? null,
                'commission_effective_date' => $agent->commission_effective_date ?? null,
                'commission_expire_date' => $agent->commission_expire_date ?? null,
                'settlement_cycle' => $agent->settlement_cycle ?? 'monthly',
                'settlement_day' => $agent->settlement_day ?? 1,
                'last_settlement_at' => $agent->last_settlement_at,
                'next_settlement_at' => $agent->next_settlement_at,
                'settlement_notes' => $agent->settlement_notes ?? null,
                'notes' => $agent->notes ?? null,
                'created_by' => $agent->created_by ?? null,
                'contact_info' => $agent->contact_info ?? null,
                'contract_notes' => $agent->contract_notes ?? null,
                'region_status' => $agent->status ?? 'active',
            ];

            if ($existingRegion) {
                // 更新现有记录
                if (!$dryRun) {
                    $existingRegion->update($businessData);
                }
                
                return [
                    'success' => true,
                    'action' => 'updated',
                    'message' => "更新现有区域配置 (区域: {$existingRegion->full_region})"
                ];
            } else {
                // 创建新的区域配置记录
                $regionData = array_merge($businessData, [
                    'agent_id' => $agent->id,
                    'area_id' => $agent->region_code ?: 1, // 如果没有有效的area_id，使用1（默认省份）
                    'province_name' => $agent->region_name ?: '未指定',
                    'is_exclusive' => true, // 默认独占
                    'contract_status' => 'signed', // 如果有签约日期，设为已签约
                ]);

                if (!$dryRun) {
                    AgentRegion::create($regionData);
                }

                return [
                    'success' => true,
                    'action' => 'created',
                    'message' => "创建新区域配置 (区域: {$agent->region_name})"
                ];
            }

        } catch (Exception $e) {
            return [
                'success' => false,
                'action' => 'error',
                'message' => "迁移失败: {$e->getMessage()}"
            ];
        }
    }

    /**
     * 验证迁移结果
     */
    private function validateMigrationResult()
    {
        // 检查是否所有有业务配置的代理商都有对应的区域配置
        $agentsWithBusiness = Agent::where(function ($query) {
            $query->whereNotNull('commission_rate')
                  ->orWhereNotNull('settlement_cycle');
        })->count();

        $regionsWithBusiness = AgentRegion::whereNotNull('commission_rate')->count();

        $this->info("有业务配置的代理商: {$agentsWithBusiness}");
        $this->info("有业务配置的区域: {$regionsWithBusiness}");

        if ($agentsWithBusiness <= $regionsWithBusiness) {
            $this->info('✓ 迁移验证通过');
        } else {
            $this->warn('⚠ 可能存在未完全迁移的数据');
        }

        // 显示迁移后的统计
        $totalRegions = AgentRegion::count();
        $activeRegions = AgentRegion::where('region_status', 'active')->count();
        $exclusiveRegions = AgentRegion::where('is_exclusive', true)->count();

        $this->table(
            ['统计项目', '数量'],
            [
                ['总区域配置数', $totalRegions],
                ['活跃区域配置', $activeRegions],
                ['独占区域配置', $exclusiveRegions],
                ['平均佣金比例', round(AgentRegion::avg('commission_rate'), 2) . '%'],
            ]
        );
    }
}
