<?php

namespace App\Http\Controllers;

use App\Models\Area;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class AreaController extends Controller
{
    /**
     * 获取所有省份
     * <AUTHOR>
     */
    public function getProvinces(): JsonResponse
    {
        $provinces = Area::where('level', 1)->get(['id', 'name', 'pinyin']);
        
        return response()->json([
            'code' => 200,
            'message' => '获取省份列表成功',
            'data' => $provinces
        ]);
    }
    
    /**
     * 根据省份ID获取城市列表
     * <AUTHOR>
     */
    public function getCitiesByProvince(Request $request): JsonResponse
    {
        $provinceId = $request->input('province_id');
        
        if (!$provinceId) {
            return response()->json([
                'code' => 400,
                'message' => '省份ID不能为空'
            ]);
        }
        
        $cities = Area::where('pid', $provinceId)
                     ->where('level', 2)
                     ->get(['id', 'name', 'pinyin']);
        
        return response()->json([
            'code' => 200,
            'message' => '获取城市列表成功',
            'data' => $cities
        ]);
    }
    
    /**
     * 根据城市ID获取区县列表
     * <AUTHOR>
     */
    public function getDistrictsByCity(Request $request): JsonResponse
    {
        $cityId = $request->input('city_id');
        
        if (!$cityId) {
            return response()->json([
                'code' => 400,
                'message' => '城市ID不能为空'
            ]);
        }
        
        $districts = Area::where('pid', $cityId)
                        ->where('level', 3)
                        ->get(['id', 'name', 'pinyin']);
        
        return response()->json([
            'code' => 200,
            'message' => '获取区县列表成功',
            'data' => $districts
        ]);
    }
    
    /**
     * 根据关键词搜索地区
     * <AUTHOR>
     */
    public function searchAreas(Request $request): JsonResponse
    {
        $keyword = $request->input('keyword');
        
        if (!$keyword) {
            return response()->json([
                'code' => 400,
                'message' => '搜索关键词不能为空'
            ]);
        }
        
        $areas = Area::where('name', 'like', '%' . $keyword . '%')
                    ->orWhere('pinyin', 'like', '%' . $keyword . '%')
                    ->limit(20)
                    ->get(['id', 'name', 'level', 'pinyin']);
        
        return response()->json([
            'code' => 200,
            'message' => '搜索成功',
            'data' => $areas
        ]);
    }
    
    /**
     * 获取地区详情及其层级路径
     * <AUTHOR>
     */
    public function getAreaPath(Request $request): JsonResponse
    {
        $areaId = $request->input('area_id');
        
        if (!$areaId) {
            return response()->json([
                'code' => 400,
                'message' => '地区ID不能为空'
            ]);
        }
        
        $area = Area::find($areaId);
        
        if (!$area) {
            return response()->json([
                'code' => 404,
                'message' => '地区不存在'
            ]);
        }
        
        // 获取完整路径
        $path = [];
        $current = $area;
        
        while ($current) {
            array_unshift($path, [
                'id' => $current->id,
                'name' => $current->name,
                'level' => $current->level,
                'pinyin' => $current->pinyin
            ]);
            $current = $current->parent;
        }
        
        return response()->json([
            'code' => 200,
            'message' => '获取地区路径成功',
            'data' => [
                'area' => $area,
                'path' => $path
            ]
        ]);
    }
    
    /**
     * 获取用户的完整地址信息
     * <AUTHOR>
     */
    public function getUserAddress(Request $request): JsonResponse
    {
        $user = $request->user();
        
        if (!$user) {
            return response()->json([
                'code' => 401,
                'message' => '用户未登录'
            ]);
        }
        
        $addressInfo = [
            'province' => $user->province ? $user->province->name : null,
            'city' => $user->city ? $user->city->name : null,
            'district' => $user->district ? $user->district->name : null,
            'address' => $user->address,
            'full_address' => $user->full_address
        ];
        
        return response()->json([
            'code' => 200,
            'message' => '获取用户地址成功',
            'data' => $addressInfo
        ]);
    }
}