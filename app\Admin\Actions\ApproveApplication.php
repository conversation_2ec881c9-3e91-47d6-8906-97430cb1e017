<?php

namespace App\Admin\Actions;

use Encore\Admin\Actions\RowAction;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use App\Models\Recruitment;

/**
 * 批准招募申请操作
 * 
 * <AUTHOR>
 */
class ApproveApplication extends RowAction
{
    public $name = '批准';

    /**
     * 处理批准操作
     *
     * @param Model $model
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function handle(Model $model, Request $request)
    {
        try {
            /** @var Recruitment $model */
            if ($model->status !== 'pending') {
                return $this->response()->error('该申请已处理，无法重复操作');
            }

            // 批准申请
            $model->status = 'approved';
            $model->audit_time = now();
            $model->audit_remark = $request->get('remark', '申请已批准');
            $model->save();

            // 这里可以添加后续处理逻辑，如创建业务员账号等
            // $model->handlePostApproval();

            return $this->response()->success('申请已批准')->refresh();
        } catch (\Exception $e) {
            return $this->response()->error('批准失败: ' . $e->getMessage());
        }
    }

    /**
     * 确认对话框
     *
     * @return string
     */
    public function confirm()
    {
        return '确定要批准该申请吗？';
    }

    /**
     * 显示条件
     *
     * @param Model $model
     * @return bool
     */
    public function display($model)
    {
        return $model->status === 'pending';
    }

    /**
     * 按钮样式
     *
     * @return string
     */
    public function style()
    {
        return 'btn btn-sm btn-success';
    }
}