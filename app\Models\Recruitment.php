<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Log;

/**
 * 招聘模型
 * 
 * @property int $id
 * @property string $applicant_name 申请人姓名
 * @property string $applicant_phone 申请人手机号
 * @property string|null $applicant_email 申请人邮箱
 * @property string|null $applicant_id_card 申请人身份证号
 * @property string|null $applicant_address 申请人地址
 * @property string $application_type 申请类型
 * @property string $recruitment_channel 招聘渠道
 * @property int|null $intended_agent_id 意向代理商ID
 * @property string $status 状态
 * @property string|null $audit_remark 审核备注
 * @property int|null $auditor_id 审核人ID
 * @property \Illuminate\Support\Carbon|null $audited_at 审核时间
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Salesperson|null $intendedAgent
 * @property-read \App\Models\User|null $auditor
 */
class Recruitment extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'applicant_name',
        'applicant_phone',
        'applicant_email',
        'applicant_id_card',
        'applicant_address',
        'application_type',
        'recruitment_channel',
        'intended_agent_id',
        'status',
        'audit_remark',
        'auditor_id',
        'audited_at',
    ];

    protected $casts = [
        'audited_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 申请类型常量
     */
    const TYPE_AGENT = 'agent';
    const TYPE_SALESPERSON = 'salesperson';
    const TYPE_TEAM_LEADER = 'team_leader';

    /**
     * 招聘渠道常量
     */
    const CHANNEL_ONLINE = 'online';
    const CHANNEL_REFERRAL = 'referral';
    const CHANNEL_OFFLINE = 'offline';
    const CHANNEL_SOCIAL_MEDIA = 'social_media';
    const CHANNEL_JOB_FAIR = 'job_fair';
    const CHANNEL_HEADHUNTER = 'headhunter';

    /**
     * 状态常量
     */
    const STATUS_PENDING = 'pending';
    const STATUS_APPROVED = 'approved';
    const STATUS_REJECTED = 'rejected';
    const STATUS_WITHDRAWN = 'withdrawn';

    /**
     * 获取申请类型选项
     */
    public static function getApplicationTypeOptions()
    {
        return [
            self::TYPE_AGENT => '代理商申请',
            self::TYPE_SALESPERSON => '销售员申请',
            self::TYPE_TEAM_LEADER => '团队长申请',
        ];
    }

    /**
     * 获取招聘渠道选项
     */
    public static function getRecruitmentChannelOptions()
    {
        return [
            self::CHANNEL_ONLINE => '线上招聘',
            self::CHANNEL_REFERRAL => '内部推荐',
            self::CHANNEL_OFFLINE => '线下招聘',
            self::CHANNEL_SOCIAL_MEDIA => '社交媒体',
            self::CHANNEL_JOB_FAIR => '招聘会',
            self::CHANNEL_HEADHUNTER => '猎头推荐',
        ];
    }

    /**
     * 获取状态选项
     */
    public static function getStatusOptions()
    {
        return [
            self::STATUS_PENDING => '待审核',
            self::STATUS_APPROVED => '已通过',
            self::STATUS_REJECTED => '已拒绝',
            self::STATUS_WITHDRAWN => '已撤回',
        ];
    }

    /**
     * 关联意向代理商
     */
    public function intendedAgent()
    {
        return $this->belongsTo(Salesperson::class, 'intended_agent_id');
    }

    /**
     * 关联审核人
     */
    public function auditor()
    {
        return $this->belongsTo(User::class, 'auditor_id');
    }

    /**
     * 获取申请类型标签
     */
    public function getApplicationTypeLabelAttribute()
    {
        return self::getApplicationTypeOptions()[$this->application_type] ?? $this->application_type;
    }

    /**
     * 获取招聘渠道标签
     */
    public function getRecruitmentChannelLabelAttribute()
    {
        return self::getRecruitmentChannelOptions()[$this->recruitment_channel] ?? $this->recruitment_channel;
    }

    /**
     * 获取状态标签
     */
    public function getStatusLabelAttribute()
    {
        return self::getStatusOptions()[$this->status] ?? $this->status;
    }

    /**
     * 获取状态颜色
     */
    public function getStatusColorAttribute()
    {
        $colors = [
            self::STATUS_PENDING => 'warning',
            self::STATUS_APPROVED => 'success',
            self::STATUS_REJECTED => 'danger',
            self::STATUS_WITHDRAWN => 'secondary',
        ];
        return $colors[$this->status] ?? 'default';
    }

    /**
     * 获取意向代理商名称
     */
    public function getIntendedAgentNameAttribute()
    {
        return $this->intendedAgent ? $this->intendedAgent->name : '无';
    }

    /**
     * 获取审核人名称
     */
    public function getAuditorNameAttribute()
    {
        return $this->auditor ? $this->auditor->name : '无';
    }

    /**
     * 获取申请时长
     */
    public function getApplicationDurationAttribute()
    {
        if ($this->status === self::STATUS_PENDING) {
            return $this->created_at->diffForHumans();
        }
        
        if ($this->audited_at) {
            return $this->created_at->diffInDays($this->audited_at) . ' 天';
        }
        
        return $this->created_at->diffForHumans();
    }

    /**
     * 获取审核时间格式化
     */
    public function getAuditedAtFormatAttribute()
    {
        return $this->audited_at ? $this->audited_at->format('Y-m-d H:i:s') : '未审核';
    }

    /**
     * 获取申请人完整信息
     */
    public function getApplicantInfoAttribute()
    {
        $info = [
            '姓名' => $this->applicant_name,
            '手机' => $this->applicant_phone,
        ];
        
        if ($this->applicant_email) {
            $info['邮箱'] = $this->applicant_email;
        }
        
        if ($this->applicant_id_card) {
            $info['身份证'] = $this->maskIdCard($this->applicant_id_card);
        }
        
        if ($this->applicant_address) {
            $info['地址'] = $this->applicant_address;
        }
        
        return $info;
    }

    /**
     * 掩码身份证号
     */
    private function maskIdCard($idCard)
    {
        if (strlen($idCard) < 8) {
            return $idCard;
        }
        
        return substr($idCard, 0, 4) . '****' . substr($idCard, -4);
    }

    /**
     * 作用域：按申请类型筛选
     */
    public function scopeByApplicationType($query, $type)
    {
        return $query->where('application_type', $type);
    }

    /**
     * 作用域：按招聘渠道筛选
     */
    public function scopeByRecruitmentChannel($query, $channel)
    {
        return $query->where('recruitment_channel', $channel);
    }

    /**
     * 作用域：按状态筛选
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 作用域：待审核
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * 作用域：已审核
     */
    public function scopeAudited($query)
    {
        return $query->whereIn('status', [self::STATUS_APPROVED, self::STATUS_REJECTED]);
    }

    /**
     * 作用域：按意向代理商筛选
     */
    public function scopeByIntendedAgent($query, $agentId)
    {
        return $query->where('intended_agent_id', $agentId);
    }

    /**
     * 作用域：搜索
     */
    public function scopeSearch($query, $keyword)
    {
        return $query->where(function ($q) use ($keyword) {
            $q->where('applicant_name', 'like', "%{$keyword}%")
              ->orWhere('applicant_phone', 'like', "%{$keyword}%")
              ->orWhere('applicant_email', 'like', "%{$keyword}%");
        });
    }

    /**
     * 作用域：按时间范围筛选
     */
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * 审核通过
     */
    public function approve($auditorId, $remark = null)
    {
        $this->update([
            'status' => self::STATUS_APPROVED,
            'audit_remark' => $remark,
            'auditor_id' => $auditorId,
            'audited_at' => now(),
        ]);
        
        // 这里可以添加后续处理逻辑，如创建对应的代理商或销售员记录
        $this->handleApproval();
        
        return $this;
    }

    /**
     * 审核拒绝
     */
    public function reject($auditorId, $remark)
    {
        $this->update([
            'status' => self::STATUS_REJECTED,
            'audit_remark' => $remark,
            'auditor_id' => $auditorId,
            'audited_at' => now(),
        ]);
        
        return $this;
    }

    /**
     * 撤回申请
     */
    public function withdraw()
    {
        if ($this->status !== self::STATUS_PENDING) {
            throw new \Exception('只有待审核的申请才能撤回');
        }
        
        $this->update(['status' => self::STATUS_WITHDRAWN]);
        return $this;
    }

    /**
     * 处理审核通过后的逻辑
     */
    private function handleApproval()
    {
        switch ($this->application_type) {
            case self::TYPE_AGENT:
                $this->createAgent();
                break;
                
            case self::TYPE_SALESPERSON:
            case self::TYPE_TEAM_LEADER:
                $this->createSalesperson();
                break;
        }
    }

    /**
     * 创建代理商记录
     */
    private function createAgent()
    {
        Agent::create([
            'name' => $this->applicant_name,
            'phone' => $this->applicant_phone,
            'email' => $this->applicant_email,
            'id_card' => $this->applicant_id_card,
            'address' => $this->applicant_address,
            'commission_rate' => 0.05, // 默认佣金比例
            'settlement_cycle' => Agent::SETTLEMENT_MONTHLY,
            'status' => Agent::STATUS_ACTIVE,
        ]);
    }

    /**
     * 创建销售员记录
     */
    private function createSalesperson()
    {
        $role = $this->application_type === self::TYPE_TEAM_LEADER 
            ? Salesperson::ROLE_TEAM_LEADER 
            : Salesperson::ROLE_SALESPERSON;
            
        Salesperson::create([
            'name' => $this->applicant_name,
            'phone' => $this->applicant_phone,
            'email' => $this->applicant_email,
            'id_card' => $this->applicant_id_card,
            'address' => $this->applicant_address,
            'agent_id' => $this->intended_agent_id,
            'role' => $role,
            'commission_rate' => 0.03, // 默认佣金比例
            'settlement_cycle' => Salesperson::SETTLEMENT_MONTHLY,
            'status' => Salesperson::STATUS_INACTIVE, // 需要激活
            'permissions' => Salesperson::getDefaultPermissions($role),
        ]);
    }

    /**
     * 检查是否可以审核
     */
    public function canAudit()
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * 检查是否可以撤回
     */
    public function canWithdraw()
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * 获取统计数据
     */
    public static function getStatistics($startDate = null, $endDate = null)
    {
        $query = self::query();
        
        if ($startDate && $endDate) {
            $query->byDateRange($startDate, $endDate);
        }
        
        return [
            'total' => $query->count(),
            'pending' => $query->where('status', self::STATUS_PENDING)->count(),
            'approved' => $query->where('status', self::STATUS_APPROVED)->count(),
            'rejected' => $query->where('status', self::STATUS_REJECTED)->count(),
            'by_type' => $query->selectRaw('application_type, count(*) as count')
                              ->groupBy('application_type')
                              ->pluck('count', 'application_type')
                              ->toArray(),
            'by_channel' => $query->selectRaw('recruitment_channel, count(*) as count')
                                 ->groupBy('recruitment_channel')
                                 ->pluck('count', 'recruitment_channel')
                                 ->toArray(),
        ];
    }

    /**
     * 发送通知
     */
    public function sendNotification($type, $message = null)
    {
        // 这里可以实现发送短信、邮件等通知逻辑
        // 暂时只记录日志
        Log::info("Recruitment notification sent", [
            'recruitment_id' => $this->id,
            'type' => $type,
            'message' => $message,
            'applicant_phone' => $this->applicant_phone,
        ]);
    }
}