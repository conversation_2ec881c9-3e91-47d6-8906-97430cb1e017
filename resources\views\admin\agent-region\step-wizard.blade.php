{{-- 这是一个用于Laravel Admin Content的片段模板，不需要@section --}}

{{-- 添加CSRF令牌支持 --}}
<meta name="csrf-token" content="{{ csrf_token() }}">

<div class="step-wizard-container"
    data-steps="{{ htmlspecialchars(json_encode($steps), ENT_QUOTES, 'UTF-8') }}"
    data-agent-id="{{ $agent_id ?? '' }}"
    data-edit-id="{{ $edit_id ?? '' }}"
    data-is-editing="{{ $is_editing ? 'true' : 'false' }}"
    data-edit-data="{{ isset($edit_data) ? htmlspecialchars(json_encode($edit_data), ENT_QUOTES, 'UTF-8') : '' }}"
    data-current-step="1"
    data-init-ready="false">
    <!-- 步骤指示器 -->
    <div class="step-indicator">
        <div class="step-progress">
            <div class="progress-bar" id="progressBar"></div>
        </div>
        <div class="steps">
            @foreach($steps as $stepNum => $step)
            <div class="step" data-step="{{ $stepNum }}" id="stepIndicator{{ $stepNum }}">
                <div class="step-circle">
                    <i class="fa {{ $step['icon'] }}"></i>
                    <span class="step-number">{{ $stepNum }}</span>
                </div>
                <div class="step-label">{{ $step['title'] }}</div>
                <div class="step-desc">{{ $step['description'] }}</div>
            </div>
            @endforeach
        </div>
    </div>

    <!-- 步骤内容容器 -->
    <div class="step-content-container">
        <div class="step-header">
            <h3 id="stepTitle">第 1 步：{{ $steps[1]['title'] }}</h3>
            <p id="stepDescription">{{ $steps[1]['description'] }}</p>
        </div>
        <div class="step-content" id="stepContent">
            <!-- 步骤内容将通过Ajax动态加载 -->
            <div class="loading-placeholder">
                <i class="fa fa-spinner fa-spin"></i>
                正在加载步骤内容...
            </div>
        </div>

        <!-- 导航按钮 -->
        <div class="step-navigation">
            <button type="button" class="btn btn-default btn-prev" id="btnPrev" style="display: none;">
                <i class="fa fa-arrow-left"></i>
                <span id="prevLabel">上一步</span>
            </button>

            <button type="button" class="btn btn-primary btn-next" id="btnNext">
                <span id="nextLabel">下一步：{{ $steps[2]['title'] }}</span>
                <i class="fa fa-arrow-right"></i>
            </button>

            <button type="button" class="btn btn-success btn-submit" id="btnSubmit" style="display: none;">
                <i class="fa fa-check"></i>
                提交配置
            </button>
        </div>
    </div>
</div>

{{-- 引入代理商区域配置的JavaScript文件 --}}
<script src="{{ asset('js/agent-region-form.js') }}?v={{ time() }}&version=2.5.0"></script>

<style>
    .step-wizard-container {
        min-height: 600px;
        padding: 30px;
        border-radius: 10px;
        color: white;
    }

    .step-indicator {
        margin-bottom: 40px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 6px;
        padding-bottom: 20px;
    }

    .step-progress {
        position: relative;
        height: 4px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 2px;
        margin-bottom: 20px;
    }

    .progress-bar {
        height: 100%;
        background: #00d4aa;
        border-radius: 2px;
        transition: width 0.4s ease;
        width: 20%;
    }

    .steps {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .step {
        text-align: center;
        flex: 1;
        position: relative;
    }

    .step-circle {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 10px;
        transition: all 0.3s ease;
        border: 2px solid rgba(255, 255, 255, 0.3);
        position: relative;
    }

    .step.active .step-circle {
        background: #00d4aa;
        border-color: #00d4aa;
        transform: scale(1.1);
        box-shadow: 0 4px 15px rgba(0, 212, 170, 0.3);
    }

    .step.completed .step-circle {
        background: #28a745;
        border-color: #28a745;
    }

    .step-circle .fa {
        font-size: 20px;
        color: white;
    }

    .step-number {
        font-weight: bold;
        font-size: 16px;
        display: none;
    }

    .step.completed .step-circle .fa {
        display: none;
    }

    .step.completed .step-number {
        display: block;
    }

    .step.completed .step-number:before {
        content: '\2713';
        /* Unicode for checkmark */
        font-size: 20px;
    }

    .step-label {
        font-weight: bold;
        font-size: 14px;
        margin-bottom: 5px;
    }

    .step-desc {
        font-size: 12px;
        opacity: 0.8;
    }

    .step-content-container {
        background: white !important;
        border-radius: 10px !important;
        padding: 30px !important;
        color: #333 !important;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
    }

    .step-header {
        text-align: center;
        margin-bottom: 30px;
        border-bottom: 1px solid #eee;
        padding-bottom: 20px;
    }

    .step-header h3 {
        color: #667eea !important;
        margin-bottom: 10px;
    }

    .step-content {
        min-height: 300px;
        margin-bottom: 30px;
    }

    .loading-placeholder {
        text-align: center;
        padding: 50px;
        color: #999;
    }

    .loading-placeholder .fa {
        font-size: 24px;
        margin-bottom: 10px;
    }

    .step-navigation {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-top: 1px solid #eee;
        padding-top: 20px;
    }

    .btn-prev,
    .btn-next,
    .btn-submit {
        padding: 12px 24px !important;
        border-radius: 25px !important;
        font-weight: bold !important;
        transition: all 0.3s ease !important;
    }

    .btn-prev:hover,
    .btn-next:hover,
    .btn-submit:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    }

    .btn-next {
        background: linear-gradient(45deg, #667eea, #764ba2) !important;
        border: none !important;
        color: white !important;
    }

    .btn-submit {
        background: linear-gradient(45deg, #28a745, #20c997) !important;
        border: none !important;
        color: white !important;
    }

    /* 只针对alert-info信息框的样式优化 */
    .step-wizard-container .step-content .alert-info {
        background: linear-gradient(135deg, #e8f4fd 0%, #d1ecf1 100%) !important;
        border: 1px solid #b8daff !important;
        border-radius: 12px !important;
        padding: 20px 25px !important;
        margin: 25px 0 !important;
        color: #004085 !important;
        box-shadow: 0 4px 12px rgba(52, 144, 220, 0.15) !important;
        position: relative !important;
        overflow: hidden !important;
    }

    .step-wizard-container .step-content .alert-info:before {
        content: '' !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        width: 4px !important;
        height: 100% !important;
        background: linear-gradient(to bottom, #007bff, #0056b3) !important;
    }

    .step-wizard-container .step-content .alert-info .fa {
        color: #007bff !important;
        font-size: 18px !important;
        margin-right: 10px !important;
        vertical-align: middle !important;
    }

    .step-wizard-container .step-content .alert-info strong {
        color: #003d82 !important;
        font-weight: 600 !important;
        font-size: 16px !important;
    }

    .step-wizard-container .step-content .alert-info ul {
        margin: 15px 0 5px 20px !important;
        padding: 0 !important;
    }

    .step-wizard-container .step-content .alert-info li {
        margin-bottom: 8px !important;
        color: #004085 !important;
        font-size: 14px !important;
        line-height: 1.6 !important;
        position: relative !important;
    }

    .step-wizard-container .step-content .alert-info li:before {
        content: '\2022';
        /* Unicode for bullet */
        color: #007bff !important;
        font-weight: bold !important;
        position: absolute !important;
        left: -15px !important;
    }
</style>