# Laravel Admin 权限系统完整配置指南

## 📖 目录

1. [权限系统架构概述](#权限系统架构概述)
2. [权限配置标准流程](#权限配置标准流程)
3. [角色权限配置矩阵](#角色权限配置矩阵)
4. [控制器权限实现](#控制器权限实现)
5. [前端权限控制](#前端权限控制)
6. [权限测试与维护](#权限测试与维护)

---

## 🏗️ 权限系统架构概述

### 数据表结构

**核心权限表：**
```sql
-- 权限定义表
admin_permissions (id, name, slug, http_method, http_path, created_at, updated_at)

-- 角色定义表  
admin_roles (id, name, slug, created_at, updated_at)

-- 角色权限关联表
admin_role_permissions (role_id, permission_id, created_at, updated_at)

-- 用户角色关联表
admin_role_users (role_id, user_id, created_at, updated_at)

-- 菜单权限配置表
admin_menu (id, parent_id, order, title, icon, uri, permission, created_at, updated_at)
```

### 权限命名规范

**格式标准：** `模块.动作`

**支持的模块：**
- `agent` - 代理商管理
- `store` - 商铺管理  
- `material` - 素材管理
- `salesperson` - 业务员管理
- `team` - 团队管理
- `recruitment` - 招募管理
- `statistics` - 数据统计
- `system` - 系统管理

**支持的动作：**
- `list` - 列表查看
- `create` - 创建新增
- `edit` - 编辑修改
- `show` - 详情查看
- `delete` - 删除操作
- `manage` - 管理权限（包含多个子权限）
- `config` - 配置管理
- `export` - 数据导出
- `import` - 数据导入

**权限示例：**
```
agent.list        # 代理商列表
agent.create      # 创建代理商
agent.edit        # 编辑代理商
agent.show        # 查看代理商详情
agent.delete      # 删除代理商
agent.manage      # 代理商管理（综合权限）
store.config      # 商铺配置
statistics.export # 统计数据导出
```

---

## 📋 权限配置标准流程

### 第一步：创建权限（admin_permissions表）

```sql
-- 代理商模块权限
INSERT INTO admin_permissions (name, slug, http_method, http_path, created_at, updated_at) VALUES
('代理商列表', 'agent.list', '["GET"]', '["/agents"]', NOW(), NOW()),
('代理商创建', 'agent.create', '["GET","POST"]', '["/agents/create","/agents"]', NOW(), NOW()),
('代理商编辑', 'agent.edit', '["GET","PUT"]', '["/agents/*/edit","/agents/*"]', NOW(), NOW()),
('代理商查看', 'agent.show', '["GET"]', '["/agents/*"]', NOW(), NOW()),
('代理商删除', 'agent.delete', '["DELETE"]', '["/agents/*"]', NOW(), NOW()),
('代理商管理', 'agent.manage', '["GET","POST","PUT"]', '["/agents/**"]', NOW(), NOW());

-- 商铺模块权限
INSERT INTO admin_permissions (name, slug, http_method, http_path, created_at, updated_at) VALUES
('商铺列表', 'store.list', '["GET"]', '["/stores"]', NOW(), NOW()),
('商铺创建', 'store.create', '["GET","POST"]', '["/stores/create","/stores"]', NOW(), NOW()),
('商铺编辑', 'store.edit', '["GET","PUT"]', '["/stores/*/edit","/stores/*"]', NOW(), NOW()),
('商铺查看', 'store.show', '["GET"]', '["/stores/*"]', NOW(), NOW()),
('商铺删除', 'store.delete', '["DELETE"]', '["/stores/*"]', NOW(), NOW()),
('商铺配置', 'store.config', '["GET","POST","PUT"]', '["/stores/config","/stores/*/config"]', NOW(), NOW());

-- 素材模块权限
INSERT INTO admin_permissions (name, slug, http_method, http_path, created_at, updated_at) VALUES
('素材列表', 'material.list', '["GET"]', '["/materials"]', NOW(), NOW()),
('素材创建', 'material.create', '["GET","POST"]', '["/materials/create","/materials"]', NOW(), NOW()),
('素材编辑', 'material.edit', '["GET","PUT"]', '["/materials/*/edit","/materials/*"]', NOW(), NOW()),
('素材查看', 'material.show', '["GET"]', '["/materials/*"]', NOW(), NOW()),
('素材删除', 'material.delete', '["DELETE"]', '["/materials/*"]', NOW(), NOW());

-- 业务员模块权限
INSERT INTO admin_permissions (name, slug, http_method, http_path, created_at, updated_at) VALUES
('业务员列表', 'salesperson.list', '["GET"]', '["/salespersons"]', NOW(), NOW()),
('业务员创建', 'salesperson.create', '["GET","POST"]', '["/salespersons/create","/salespersons"]', NOW(), NOW()),
('业务员编辑', 'salesperson.edit', '["GET","PUT"]', '["/salespersons/*/edit","/salespersons/*"]', NOW(), NOW()),
('业务员查看', 'salesperson.show', '["GET"]', '["/salespersons/*"]', NOW(), NOW()),
('业务员删除', 'salesperson.delete', '["DELETE"]', '["/salespersons/*"]', NOW(), NOW());

-- 统计模块权限
INSERT INTO admin_permissions (name, slug, http_method, http_path, created_at, updated_at) VALUES
('统计列表', 'statistics.list', '["GET"]', '["/statistics"]', NOW(), NOW()),
('统计查看', 'statistics.show', '["GET"]', '["/statistics/*"]', NOW(), NOW()),
('统计导出', 'statistics.export', '["GET","POST"]', '["/statistics/export"]', NOW(), NOW());
```

### 第二步：创建角色（admin_roles表）

```sql
INSERT INTO admin_roles (name, slug, created_at, updated_at) VALUES
('超级管理员', 'super-admin', NOW(), NOW()),
('平台管理员', 'platform-admin', NOW(), NOW()),
('一级代理商', 'primary-agent', NOW(), NOW()),
('二级代理商', 'secondary-agent', NOW(), NOW()),
('运营人员', 'operation-staff', NOW(), NOW());
```

### 第三步：分配权限给角色（admin_role_permissions表）

```sql
-- 超级管理员 - 获得所有权限
INSERT INTO admin_role_permissions (role_id, permission_id, created_at, updated_at)
SELECT 1, id, NOW(), NOW() FROM admin_permissions;

-- 平台管理员 - 业务管理权限
INSERT INTO admin_role_permissions (role_id, permission_id, created_at, updated_at)
SELECT 2, id, NOW(), NOW() FROM admin_permissions 
WHERE slug IN (
    'agent.list', 'agent.create', 'agent.edit', 'agent.show', 'agent.delete',
    'store.list', 'store.create', 'store.edit', 'store.show', 'store.config',
    'material.list', 'material.create', 'material.edit', 'material.show', 'material.delete',
    'salesperson.list', 'salesperson.create', 'salesperson.edit', 'salesperson.show', 'salesperson.delete',
    'statistics.list', 'statistics.show', 'statistics.export'
);

-- 一级代理商 - 有限管理权限
INSERT INTO admin_role_permissions (role_id, permission_id, created_at, updated_at)
SELECT 3, id, NOW(), NOW() FROM admin_permissions 
WHERE slug IN (
    'agent.list', 'agent.show', 'agent.manage',
    'store.list', 'store.create', 'store.edit', 'store.show',
    'material.list', 'material.show',
    'salesperson.list', 'salesperson.create', 'salesperson.edit', 'salesperson.show',
    'statistics.list', 'statistics.show'
);

-- 二级代理商 - 查看权限为主
INSERT INTO admin_role_permissions (role_id, permission_id, created_at, updated_at)
SELECT 4, id, NOW(), NOW() FROM admin_permissions 
WHERE slug IN (
    'store.list', 'store.show',
    'material.list', 'material.show',
    'salesperson.list', 'salesperson.show',
    'statistics.list', 'statistics.show'
);

-- 运营人员 - 特定模块权限
INSERT INTO admin_role_permissions (role_id, permission_id, created_at, updated_at)
SELECT 5, id, NOW(), NOW() FROM admin_permissions 
WHERE slug IN (
    'material.list', 'material.create', 'material.edit', 'material.show', 'material.delete',
    'salesperson.list', 'salesperson.show',
    'statistics.list', 'statistics.show'
);
```

### 第四步：分配角色给用户（admin_role_users表）

```sql
-- 假设用户ID对应关系：
-- 1 = super_admin (超级管理员)
-- 2 = platform_admin (平台管理员)  
-- 3 = agent_001 (一级代理商)
-- 4,5,6 = agent_002,003,004 (二级代理商)

INSERT INTO admin_role_users (role_id, user_id, created_at, updated_at) VALUES
(1, 1, NOW(), NOW()), -- super_admin -> 超级管理员
(2, 2, NOW(), NOW()), -- platform_admin -> 平台管理员
(3, 3, NOW(), NOW()), -- agent_001 -> 一级代理商
(4, 4, NOW(), NOW()), -- agent_002 -> 二级代理商
(4, 5, NOW(), NOW()), -- agent_003 -> 二级代理商
(4, 6, NOW(), NOW()); -- agent_004 -> 二级代理商
```

### 第五步：配置菜单权限（admin_menu表）

```sql
-- 更新菜单项的权限字段
UPDATE admin_menu SET permission = 'agent.list' WHERE uri = 'agents';
UPDATE admin_menu SET permission = 'agent.create' WHERE uri = 'agents/create';
UPDATE admin_menu SET permission = 'store.list' WHERE uri = 'stores';
UPDATE admin_menu SET permission = 'store.config' WHERE uri = 'stores/config';
UPDATE admin_menu SET permission = 'material.list' WHERE uri = 'materials';
UPDATE admin_menu SET permission = 'salesperson.list' WHERE uri = 'salespersons';
UPDATE admin_menu SET permission = 'statistics.list' WHERE uri = 'statistics';

-- 示例：完整的菜单权限配置
UPDATE admin_menu SET permission = CASE 
    WHEN uri = 'agents' THEN 'agent.list'
    WHEN uri = 'agents/create' THEN 'agent.create'
    WHEN uri = 'stores' THEN 'store.list'
    WHEN uri = 'stores/create' THEN 'store.create'
    WHEN uri = 'stores/config' THEN 'store.config'
    WHEN uri = 'materials' THEN 'material.list'
    WHEN uri = 'materials/create' THEN 'material.create'
    WHEN uri = 'salespersons' THEN 'salesperson.list'
    WHEN uri = 'salespersons/create' THEN 'salesperson.create'
    WHEN uri = 'statistics' THEN 'statistics.list'
    ELSE permission
END;
```

---

## 🎯 控制器权限实现

### PermissionService 权限服务类

```php
<?php

namespace App\Services;

use Encore\Admin\Auth\Database\Administrator;

class PermissionService
{
    /**
     * 检查用户是否拥有指定权限
     */
    public static function hasPermission(Administrator $user, string $permission): bool
    {
        // 超级管理员拥有所有权限
        if ($user->isRole('super-admin')) {
            return true;
        }

        // 检查用户是否拥有该权限
        return $user->can($permission);
    }

    /**
     * 获取用户可访问的代理商ID列表
     */
    public static function getAccessibleAgentIds(Administrator $user): array
    {
        if ($user->isRole(['super-admin', 'platform-admin'])) {
            // 管理员可访问所有代理商
            return Agent::pluck('id')->toArray();
        }

        if ($user->user_type == 2) { // 一级代理商
            // 可访问自己和下级代理商
            $agentIds = [$user->agent_id];
            $subAgents = Agent::where('parent_agent_id', $user->agent_id)->pluck('id')->toArray();
            return array_merge($agentIds, $subAgents);
        }

        if ($user->user_type == 3) { // 二级代理商
            // 只能访问自己
            return [$user->agent_id];
        }

        return [];
    }

    /**
     * 获取用户可访问的商铺ID列表
     */
    public static function getAccessibleStoreIds(Administrator $user): array
    {
        if ($user->isRole(['super-admin', 'platform-admin'])) {
            return Store::pluck('id')->toArray();
        }

        $accessibleAgentIds = self::getAccessibleAgentIds($user);
        return Store::whereIn('agent_id', $accessibleAgentIds)->pluck('id')->toArray();
    }
}
```

### 控制器权限实现示例

```php
<?php

namespace App\Admin\Controllers;

use App\Services\PermissionService;
use Encore\Admin\Controllers\AdminController;

class AgentController extends AdminController
{
    /**
     * 代理商列表
     */
    public function index()
    {
        // 权限检查
        if (!PermissionService::hasPermission(auth('admin')->user(), 'agent.list')) {
            return redirect()->route('admin.unauthorized')
                ->with('error', '您没有权限访问代理商列表');
        }

        $user = auth('admin')->user();
        
        // 数据权限过滤
        $accessibleAgentIds = PermissionService::getAccessibleAgentIds($user);
        $agents = Agent::whereIn('id', $accessibleAgentIds)->paginate(15);

        return view('admin.agents.index', compact('agents'));
    }

    /**
     * 创建代理商页面
     */
    public function create()
    {
        if (!PermissionService::hasPermission(auth('admin')->user(), 'agent.create')) {
            abort(403, '您没有权限创建代理商');
        }

        return view('admin.agents.create');
    }

    /**
     * 保存代理商
     */
    public function store(Request $request)
    {
        if (!PermissionService::hasPermission(auth('admin')->user(), 'agent.create')) {
            return response()->json(['success' => false, 'message' => '权限不足']);
        }

        // 业务逻辑...
        $agent = Agent::create($request->validated());

        return response()->json(['success' => true, 'message' => '创建成功']);
    }

    /**
     * 编辑代理商页面
     */
    public function edit($id)
    {
        if (!PermissionService::hasPermission(auth('admin')->user(), 'agent.edit')) {
            abort(403, '您没有权限编辑代理商');
        }

        $user = auth('admin')->user();
        $accessibleAgentIds = PermissionService::getAccessibleAgentIds($user);
        
        if (!in_array($id, $accessibleAgentIds)) {
            abort(403, '您没有权限编辑此代理商');
        }

        $agent = Agent::findOrFail($id);
        return view('admin.agents.edit', compact('agent'));
    }

    /**
     * 更新代理商
     */
    public function update(Request $request, $id)
    {
        if (!PermissionService::hasPermission(auth('admin')->user(), 'agent.edit')) {
            return response()->json(['success' => false, 'message' => '权限不足']);
        }

        $user = auth('admin')->user();
        $accessibleAgentIds = PermissionService::getAccessibleAgentIds($user);
        
        if (!in_array($id, $accessibleAgentIds)) {
            return response()->json(['success' => false, 'message' => '无权限编辑此代理商']);
        }

        // 业务逻辑...
        $agent = Agent::findOrFail($id);
        $agent->update($request->validated());

        return response()->json(['success' => true, 'message' => '更新成功']);
    }

    /**
     * 删除代理商
     */
    public function destroy($id)
    {
        if (!PermissionService::hasPermission(auth('admin')->user(), 'agent.delete')) {
            return response()->json(['success' => false, 'message' => '权限不足']);
        }

        $user = auth('admin')->user();
        $accessibleAgentIds = PermissionService::getAccessibleAgentIds($user);
        
        if (!in_array($id, $accessibleAgentIds)) {
            return response()->json(['success' => false, 'message' => '无权限删除此代理商']);
        }

        // 业务逻辑...
        Agent::findOrFail($id)->delete();

        return response()->json(['success' => true, 'message' => '删除成功']);
    }
}
```

### Laravel-Admin Grid权限控制

```php
protected function grid()
{
    $grid = new Grid(new Agent());
    
    $user = auth('admin')->user();
    
    // 数据权限过滤
    $accessibleAgentIds = PermissionService::getAccessibleAgentIds($user);
    $grid->model()->whereIn('id', $accessibleAgentIds);

    // 按钮权限控制
    $grid->actions(function ($actions) use ($user) {
        if (!PermissionService::hasPermission($user, 'agent.show')) {
            $actions->disableView();
        }
        if (!PermissionService::hasPermission($user, 'agent.edit')) {
            $actions->disableEdit();
        }
        if (!PermissionService::hasPermission($user, 'agent.delete')) {
            $actions->disableDelete();
        }
    });

    // 工具栏权限控制
    $grid->tools(function ($tools) use ($user) {
        if (!PermissionService::hasPermission($user, 'agent.create')) {
            $tools->append('<style>.btn-group .btn-success { display: none !important; }</style>');
        }
    });

    return $grid;
}
```

---

## 🎨 前端权限控制

### Blade模板权限控制

```blade
{{-- 代理商列表页面示例 --}}
@extends('admin.layouts.app')

@section('content')
<div class="content-wrapper">
    <div class="content-header">
        <h1>代理商管理</h1>
        
        {{-- 创建按钮权限控制 --}}
        @if(PermissionService::hasPermission(auth('admin')->user(), 'agent.create'))
            <a href="{{ route('admin.agents.create') }}" class="btn btn-primary">
                <i class="fa fa-plus"></i> 创建代理商
            </a>
        @endif
    </div>

    <div class="content">
        <div class="box">
            <div class="box-body">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>代理商名称</th>
                            <th>联系人</th>
                            <th>电话</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($agents as $agent)
                        <tr>
                            <td>{{ $agent->id }}</td>
                            <td>{{ $agent->name }}</td>
                            <td>{{ $agent->contact_name }}</td>
                            <td>{{ $agent->phone }}</td>
                            <td>
                                <span class="label label-{{ $agent->status == 'active' ? 'success' : 'danger' }}">
                                    {{ $agent->status == 'active' ? '启用' : '禁用' }}
                                </span>
                            </td>
                            <td>
                                {{-- 查看按钮 --}}
                                @if(PermissionService::hasPermission(auth('admin')->user(), 'agent.show'))
                                    <a href="{{ route('admin.agents.show', $agent->id) }}" class="btn btn-info btn-sm">
                                        <i class="fa fa-eye"></i> 查看
                                    </a>
                                @endif

                                {{-- 编辑按钮 --}}
                                @if(PermissionService::hasPermission(auth('admin')->user(), 'agent.edit'))
                                    <a href="{{ route('admin.agents.edit', $agent->id) }}" class="btn btn-warning btn-sm">
                                        <i class="fa fa-edit"></i> 编辑
                                    </a>
                                @endif

                                {{-- 删除按钮 --}}
                                @if(PermissionService::hasPermission(auth('admin')->user(), 'agent.delete'))
                                    <button class="btn btn-danger btn-sm delete-btn" data-id="{{ $agent->id }}">
                                        <i class="fa fa-trash"></i> 删除
                                    </button>
                                @endif
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
// 权限数据传递给JavaScript
window.userPermissions = @json(auth('admin')->user()->allPermissions()->pluck('slug'));
window.userType = {{ auth('admin')->user()->user_type ?? 0 }};
</script>
@endsection
```

### JavaScript权限控制

```javascript
/**
 * 权限控制工具类
 */
window.PermissionHelper = {
    
    /**
     * 检查用户是否拥有指定权限
     */
    hasPermission: function(permission) {
        return window.userPermissions && window.userPermissions.includes(permission);
    },

    /**
     * 根据权限显示/隐藏元素
     */
    toggleElement: function(selector, permission, show = true) {
        if (this.hasPermission(permission) === show) {
            $(selector).show();
        } else {
            $(selector).hide();
        }
    },

    /**
     * 根据权限启用/禁用元素
     */
    toggleEnabled: function(selector, permission, enabled = true) {
        if (this.hasPermission(permission) === enabled) {
            $(selector).prop('disabled', false);
        } else {
            $(selector).prop('disabled', true);
        }
    },

    /**
     * 初始化页面权限控制
     */
    initPagePermissions: function() {
        // 创建按钮权限控制
        this.toggleElement('.create-btn', 'agent.create');
        this.toggleElement('.edit-btn', 'agent.edit');
        this.toggleElement('.delete-btn', 'agent.delete');
        
        // 导出按钮权限控制
        this.toggleElement('.export-btn', 'statistics.export');
        
        // 批量操作权限控制
        this.toggleElement('.batch-delete-btn', 'agent.delete');
        this.toggleElement('.batch-approve-btn', 'store.edit');
    }
};

// 页面加载完成后初始化权限控制
$(document).ready(function() {
    PermissionHelper.initPagePermissions();
    
    // 动态权限控制示例
    $('.permission-controlled').each(function() {
        var $this = $(this);
        var permission = $this.data('permission');
        
        if (!PermissionHelper.hasPermission(permission)) {
            $this.hide();
        }
    });
    
    // 数据表格权限控制
    if (typeof DataTable !== 'undefined') {
        DataTable.initWithPermissions();
    }
});

/**
 * 数据表格权限控制扩展
 */
window.DataTable = {
    
    initWithPermissions: function() {
        var user = window.userType;
        var config = {
            processing: true,
            serverSide: true,
            ajax: {
                url: '/admin/agents/api',
                data: function(d) {
                    // 根据用户类型添加数据过滤参数
                    if (user == 2) { // 一级代理商
                        d.filter_subordinates = true;
                    } else if (user == 3) { // 二级代理商
                        d.filter_self_only = true;
                    }
                }
            },
            columns: [
                {data: 'id'},
                {data: 'name'},
                {data: 'contact_name'},
                {data: 'phone'},
                {data: 'status'},
                {
                    data: 'actions',
                    render: function(data, type, row) {
                        var actions = '';
                        
                        if (PermissionHelper.hasPermission('agent.show')) {
                            actions += '<a href="/admin/agents/' + row.id + '" class="btn btn-info btn-sm">查看</a> ';
                        }
                        
                        if (PermissionHelper.hasPermission('agent.edit')) {
                            actions += '<a href="/admin/agents/' + row.id + '/edit" class="btn btn-warning btn-sm">编辑</a> ';
                        }
                        
                        if (PermissionHelper.hasPermission('agent.delete')) {
                            actions += '<button class="btn btn-danger btn-sm delete-btn" data-id="' + row.id + '">删除</button>';
                        }
                        
                        return actions;
                    }
                }
            ]
        };
        
        $('#agents-table').DataTable(config);
    }
};
```

### 权限控制CSS类

```css
/* 权限控制相关样式 */
.permission-hidden {
    display: none !important;
}

.permission-disabled {
    opacity: 0.5;
    pointer-events: none;
}

/* 角色特定样式 */
.primary-agent-only {
    display: none;
}

.secondary-agent-only {
    display: none;
}

.platform-admin-only {
    display: none;
}

/* 根据用户类型显示对应元素 */
body[data-user-type="1"] .platform-admin-only,
body[data-user-type="2"] .primary-agent-only,
body[data-user-type="3"] .secondary-agent-only {
    display: block !important;
}

/* 权限按钮组样式 */
.permission-btn-group {
    display: flex;
    gap: 5px;
}

.permission-btn-group .btn:not(.permission-allowed) {
    display: none;
}
```

---

## 🧪 权限测试与维护

### 权限测试脚本

```php
<?php

/**
 * 权限系统测试脚本
 */
class PermissionSystemTest
{
    /**
     * 测试权限配置完整性
     */
    public function testPermissionCompleteness()
    {
        $expectedPermissions = [
            // 代理商模块
            'agent.list', 'agent.create', 'agent.edit', 'agent.show', 'agent.delete', 'agent.manage',
            // 商铺模块
            'store.list', 'store.create', 'store.edit', 'store.show', 'store.delete', 'store.config',
            // 素材模块
            'material.list', 'material.create', 'material.edit', 'material.show', 'material.delete',
            // 业务员模块
            'salesperson.list', 'salesperson.create', 'salesperson.edit', 'salesperson.show', 'salesperson.delete',
            // 统计模块
            'statistics.list', 'statistics.show', 'statistics.export'
        ];

        $existingPermissions = AdminPermission::pluck('slug')->toArray();
        $missingPermissions = array_diff($expectedPermissions, $existingPermissions);

        if (!empty($missingPermissions)) {
            echo "缺失的权限: " . implode(', ', $missingPermissions) . "\n";
            return false;
        }

        echo "所有权限配置完整\n";
        return true;
    }

    /**
     * 测试角色权限分配
     */
    public function testRolePermissions()
    {
        $roles = [
            'platform-admin' => [
                'should_have' => ['agent.list', 'agent.create', 'store.list', 'store.create', 'material.list'],
                'should_not_have' => []
            ],
            'primary-agent' => [
                'should_have' => ['agent.list', 'agent.show', 'store.list', 'store.create'],
                'should_not_have' => ['agent.delete', 'system.config']
            ],
            'secondary-agent' => [
                'should_have' => ['store.list', 'store.show', 'material.list'],
                'should_not_have' => ['agent.create', 'agent.delete', 'store.delete']
            ]
        ];

        foreach ($roles as $roleSlug => $tests) {
            $role = AdminRole::where('slug', $roleSlug)->first();
            if (!$role) {
                echo "角色 {$roleSlug} 不存在\n";
                continue;
            }

            $rolePermissions = $role->permissions()->pluck('slug')->toArray();

            // 检查应该拥有的权限
            foreach ($tests['should_have'] as $permission) {
                if (!in_array($permission, $rolePermissions)) {
                    echo "角色 {$roleSlug} 缺少权限: {$permission}\n";
                }
            }

            // 检查不应该拥有的权限
            foreach ($tests['should_not_have'] as $permission) {
                if (in_array($permission, $rolePermissions)) {
                    echo "角色 {$roleSlug} 拥有不应该有的权限: {$permission}\n";
                }
            }
        }

        echo "角色权限测试完成\n";
    }

    /**
     * 测试用户权限功能
     */
    public function testUserPermissions()
    {
        $testUsers = [
            'agent_001' => 'primary-agent',
            'agent_002' => 'secondary-agent'
        ];

        foreach ($testUsers as $username => $expectedRole) {
            $user = AdminUser::where('username', $username)->first();
            if (!$user) {
                echo "用户 {$username} 不存在\n";
                continue;
            }

            // 测试角色分配
            $userRoles = $user->roles()->pluck('slug')->toArray();
            if (!in_array($expectedRole, $userRoles)) {
                echo "用户 {$username} 角色分配错误\n";
            }

            // 测试权限检查
            $testPermissions = [
                'agent.list' => $expectedRole === 'primary-agent',
                'agent.create' => false,
                'store.list' => true,
                'store.delete' => false
            ];

            foreach ($testPermissions as $permission => $expected) {
                $actual = PermissionService::hasPermission($user, $permission);
                if ($actual !== $expected) {
                    echo "用户 {$username} 权限 {$permission} 检查失败: 期望 " . 
                         ($expected ? 'true' : 'false') . ", 实际 " . ($actual ? 'true' : 'false') . "\n";
                }
            }
        }

        echo "用户权限测试完成\n";
    }

    /**
     * 测试数据权限过滤
     */
    public function testDataPermissions()
    {
        $primaryAgent = AdminUser::where('username', 'agent_001')->first();
        $secondaryAgent = AdminUser::where('username', 'agent_002')->first();

        // 测试一级代理商数据访问
        $primaryAccessibleAgents = PermissionService::getAccessibleAgentIds($primaryAgent);
        echo "一级代理商可访问的代理商: " . implode(', ', $primaryAccessibleAgents) . "\n";

        // 测试二级代理商数据访问
        $secondaryAccessibleAgents = PermissionService::getAccessibleAgentIds($secondaryAgent);
        echo "二级代理商可访问的代理商: " . implode(', ', $secondaryAccessibleAgents) . "\n";

        // 验证数据隔离
        $intersection = array_intersect($primaryAccessibleAgents, $secondaryAccessibleAgents);
        if (count($intersection) > 1) {
            echo "数据权限隔离可能存在问题\n";
        }

        echo "数据权限测试完成\n";
    }

    /**
     * 运行所有测试
     */
    public function runAllTests()
    {
        echo "开始权限系统测试...\n\n";
        
        $this->testPermissionCompleteness();
        echo "\n";
        
        $this->testRolePermissions();
        echo "\n";
        
        $this->testUserPermissions();
        echo "\n";
        
        $this->testDataPermissions();
        echo "\n";
        
        echo "权限系统测试完成！\n";
    }
}

// 运行测试
$test = new PermissionSystemTest();
$test->runAllTests();
```

### 权限维护脚本

```php
<?php

/**
 * 权限维护工具
 */
class PermissionMaintenance
{
    /**
     * 同步菜单权限
     */
    public function syncMenuPermissions()
    {
        $menus = AdminMenu::whereNotNull('uri')->get();
        
        foreach ($menus as $menu) {
            if (empty($menu->permission)) {
                // 根据URI推断权限
                $permission = $this->inferPermissionFromUri($menu->uri);
                if ($permission && AdminPermission::where('slug', $permission)->exists()) {
                    $menu->update(['permission' => $permission]);
                    echo "菜单 '{$menu->title}' 设置权限: {$permission}\n";
                }
            }
        }
    }

    /**
     * 根据URI推断权限
     */
    private function inferPermissionFromUri($uri)
    {
        $patterns = [
            'agents' => 'agent.list',
            'agents/create' => 'agent.create',
            'stores' => 'store.list',
            'stores/create' => 'store.create',
            'stores/config' => 'store.config',
            'materials' => 'material.list',
            'materials/create' => 'material.create',
            'salespersons' => 'salesperson.list',
            'salespersons/create' => 'salesperson.create',
            'statistics' => 'statistics.list'
        ];

        return $patterns[$uri] ?? null;
    }

    /**
     * 清理无效权限
     */
    public function cleanupInvalidPermissions()
    {
        // 清理没有关联角色的权限
        $unusedPermissions = AdminPermission::whereDoesntHave('roles')->get();
        
        foreach ($unusedPermissions as $permission) {
            echo "发现未使用的权限: {$permission->slug}\n";
            // 可选择删除或标记
        }

        // 清理无效的角色权限关联
        $invalidRolePermissions = AdminRolePermissions::whereDoesntHave('permission')
                                                    ->orWhereDoesntHave('role')
                                                    ->get();
        
        foreach ($invalidRolePermissions as $relation) {
            $relation->delete();
            echo "删除无效的角色权限关联: role_id={$relation->role_id}, permission_id={$relation->permission_id}\n";
        }
    }

    /**
     * 权限数据备份
     */
    public function backupPermissions()
    {
        $backup = [
            'permissions' => AdminPermission::all()->toArray(),
            'roles' => AdminRole::all()->toArray(),
            'role_permissions' => AdminRolePermissions::all()->toArray(),
            'role_users' => AdminRoleUsers::all()->toArray(),
            'menu_permissions' => AdminMenu::whereNotNull('permission')->select('id', 'title', 'uri', 'permission')->get()->toArray()
        ];

        $filename = 'permissions_backup_' . date('Y-m-d_H-i-s') . '.json';
        file_put_contents(storage_path('app/backups/' . $filename), json_encode($backup, JSON_PRETTY_PRINT));
        
        echo "权限数据已备份到: {$filename}\n";
    }

    /**
     * 生成权限报告
     */
    public function generatePermissionReport()
    {
        $report = [];
        
        // 统计权限数量
        $report['statistics'] = [
            'total_permissions' => AdminPermission::count(),
            'total_roles' => AdminRole::count(),
            'total_users' => AdminUser::count()
        ];

        // 各角色权限统计
        $roles = AdminRole::with('permissions')->get();
        foreach ($roles as $role) {
            $report['roles'][$role->slug] = [
                'name' => $role->name,
                'permission_count' => $role->permissions->count(),
                'permissions' => $role->permissions->pluck('slug')->toArray()
            ];
        }

        // 用户角色分布
        $userRoles = AdminUser::with('roles')->get();
        foreach ($userRoles as $user) {
            $report['users'][$user->username] = [
                'name' => $user->name,
                'roles' => $user->roles->pluck('slug')->toArray()
            ];
        }

        $filename = 'permission_report_' . date('Y-m-d_H-i-s') . '.json';
        file_put_contents(storage_path('app/reports/' . $filename), json_encode($report, JSON_PRETTY_PRINT));
        
        echo "权限报告已生成: {$filename}\n";
        return $report;
    }
}

// 运行维护任务
$maintenance = new PermissionMaintenance();
$maintenance->syncMenuPermissions();
$maintenance->cleanupInvalidPermissions();
$maintenance->backupPermissions();
$maintenance->generatePermissionReport();
```

---

## 📝 权限系统最佳实践

### 1. 权限设计原则

- **最小权限原则** - 用户只获得完成工作所需的最少权限
- **职责分离** - 不同角色拥有不同的权限范围  
- **分层权限** - 通过角色层级实现权限继承
- **数据隔离** - 确保用户只能访问授权范围内的数据

### 2. 命名规范

- **权限命名** - 使用 `模块.动作` 格式，如 `agent.create`
- **角色命名** - 使用描述性名称，如 `primary-agent`
- **一致性** - 保持命名风格的一致性

### 3. 性能优化

- **权限缓存** - 缓存用户权限信息减少数据库查询
- **批量检查** - 一次性检查多个权限而非逐个检查
- **索引优化** - 为权限相关字段添加数据库索引

### 4. 安全考虑

- **输入验证** - 验证权限参数防止注入攻击
- **审计日志** - 记录权限变更和敏感操作
- **定期审查** - 定期检查和清理无效权限

### 5. 维护策略

- **版本控制** - 权限配置变更应纳入版本控制
- **测试覆盖** - 确保权限功能有充分的测试覆盖
- **文档更新** - 及时更新权限配置文档

---

**最后更新：** 2025-01-09  
**维护者：** lauJinyu 